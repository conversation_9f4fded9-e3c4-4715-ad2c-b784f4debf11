package com.anytech.anytxn.account.job.errorfile.step;

import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.transaction.mapper.SettlementLogMapper;
import com.anytech.anytxn.business.dao.transaction.model.SettlementLog;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmTransactionCode;
import com.anytech.anytxn.parameter.base.common.domain.model.unicast.ErrorFile;
import com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmErrorTcConvert;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmTransactionCodeSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.unicast.ErrorFileSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.unicast.ParmErrorTcConvertSelfMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.spring.SqlSessionTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-06-25
 */
public class ErrorFileTasklet implements Tasklet {

    private static final Logger logger = LoggerFactory.getLogger(ErrorFileTasklet.class);

    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;
    @Autowired
    private ParmTransactionCodeSelfMapper transactionCodeSelfMapper;
    @Autowired
    private ParmErrorTcConvertSelfMapper errorTcConvertSelfMapper;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private SettlementLogMapper settlementLogMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    private SqlSessionTemplate sqlSessionTemplateCommon;
    public ErrorFileTasklet(SqlSessionTemplate sqlSessionTemplateCommon) {
        this.sqlSessionTemplateCommon = sqlSessionTemplateCommon;
    }

    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) throws Exception {
        logger.info("Error file processing version: 0806");
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        ErrorFileSelfMapper errorFileSelfMapper = sqlSessionTemplateCommon.getMapper(ErrorFileSelfMapper.class);
        List<ErrorFile> errorFiles = errorFileSelfMapper.selectByProcessingStatus("1", organizationInfo.getToday());
        if (CollectionUtils.isNotEmpty(errorFiles)){
            for (ErrorFile errorFile : errorFiles){
                String cardNumber = errorFile.getCardNumber();
                CardAuthorizationInfo authorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(cardNumber, OrgNumberUtils.getOrg());
                if (ObjectUtils.isNotEmpty(authorizationInfo)){
                    SettlementLog settlementLog = buildSettlementLog(errorFile, authorizationInfo);
                    if (ObjectUtils.isNotEmpty(settlementLog)){
                        settlementLogMapper.insertSelective(settlementLog);
                    }
                }
            }
        }
        return RepeatStatus.FINISHED;
    }

    private SettlementLog buildSettlementLog(ErrorFile errorFile, CardAuthorizationInfo authorizationInfo){
        SettlementLog settlementLog = null;
        String cardNumber = errorFile.getCardNumber();
        LocalDate processDate = errorFile.getProcessDate();
        BigDecimal transactionAmount = errorFile.getTransactionAmount();
        String transactionCurrencyCode = errorFile.getTransactionCurrencyCode();
        String authorizationCode = errorFile.getAuthorizationCode();
        String merchantCategoryCode = errorFile.getMerchantCategoryCode();
        String disputeTranscationInd = errorFile.getDisputeTranscationInd();
        if (StringUtils.isNotEmpty(disputeTranscationInd)
                && ("E74".equals(disputeTranscationInd) || "E80".equals(disputeTranscationInd))){
            merchantCategoryCode = "";
        }
        ParmErrorTcConvert parmErrorTcConvert = errorTcConvertSelfMapper.findParmSettleTcConvert(OrgNumberUtils.getOrg(), disputeTranscationInd, merchantCategoryCode);
        if (ObjectUtils.isNotEmpty(parmErrorTcConvert)){
            settlementLog = new SettlementLog();
            String transactionCode = parmErrorTcConvert.getTransactionCode();
            if (StringUtils.isNotEmpty(transactionCode)){
                ParmTransactionCode parmTransactionCode = transactionCodeSelfMapper.selectByOrgNumberAndCode(OrgNumberUtils.getOrg(), transactionCode);
                settlementLog.setTxnTransactionCode(parmTransactionCode.getTransactionCode());
                settlementLog.setTxnTransactionDescription(parmTransactionCode.getDescription());
            }
            String primaryCustomerId = authorizationInfo.getPrimaryCustomerId();
            List<AccountManagementInfo> accountManagementInfos = accountManagementInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(), primaryCustomerId);
            if (CollectionUtils.isNotEmpty(accountManagementInfos)){
                String accountManagementId = accountManagementInfos.get(0).getAccountManagementId();
                settlementLog.setTxnAccountManageId(accountManagementId);
            }
            settlementLog.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            settlementLog.setTxnCardNumber(cardNumber);
            settlementLog.setTxnPostMethod("1");
            settlementLog.setTxnRepostFromSuspend("0");
            settlementLog.setTxnReverseFeeIndicator("1");
            settlementLog.setTxnTransactionSource("C");
            settlementLog.setTxnTransactionDate(processDate.atTime(0,0,0));
            settlementLog.setTxnTransactionAmount(transactionAmount);
            settlementLog.setTxnTransactionCurrency(transactionCurrencyCode);
            settlementLog.setTxnBillingDate(processDate);
            settlementLog.setTxnBillingAmount(transactionAmount);
            settlementLog.setTxnBillingCurrency(transactionCurrencyCode);
            settlementLog.setTxnSettlementAmount(transactionAmount);
            settlementLog.setTxnSettlementCurrency(transactionCurrencyCode);
            settlementLog.setTxnAuthorizationCode(authorizationCode);
            settlementLog.setTxnMerchantCategoryCode(merchantCategoryCode);
            settlementLog.setTxnAuthMatchIndicator("0");
            settlementLog.setTxnReleaseAuthAmount("Y");
            settlementLog.setMessageIndicator("1");
            settlementLog.setCreateTime(LocalDateTime.now());
            settlementLog.setUpdateTime(LocalDateTime.now());
            settlementLog.setUpdateBy(LoginUserUtils.getLoginUserName());
            settlementLog.setVersionNumber(1L);
        }
        return settlementLog;
    }
}
