package com.anytech.anytxn.installment.batch.job.utils;

import com.anytech.anytxn.common.core.utils.DateHelper;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2019-07-16 14:25
 **/
public class InstallmentBatchCreateFileUtil {
    private static final Logger logger = LoggerFactory.getLogger(InstallmentBatchCreateFileUtil.class);


    public static File newFile(String folder, String name) {
        try {
            String format = DateHelper.format2Ymd(LocalDate.now());
            String fileFullPathName = folder + File.separator + name + "-" + format + ".txt";
            File file = new File(fileFullPathName);
            logger.info("Starting to create file: {}", fileFullPathName);
            if(!file.getParentFile().exists()){
                //先得到文件的上级目录，并创建上级目录，在创建文件
                String directoryPath = file.getParentFile().getPath();
                Path path = Paths.get(directoryPath);
                Path directory = Files.createDirectories(path);
            }
            if (!file.exists()) {
                //创建文件
                boolean flag = file.createNewFile();
                if (flag) {
                    return file;
                }
            }
            return file;
        } catch (Exception e) {
            logger.error("Failed to create file: exception={}", e.getMessage(), e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_CREATE_FILE_FAIL_FAULT,e);
        }
    }
}
