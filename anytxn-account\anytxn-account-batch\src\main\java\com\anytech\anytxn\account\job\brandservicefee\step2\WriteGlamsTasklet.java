package com.anytech.anytxn.account.job.brandservicefee.step2;

import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlamsMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlams;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.PartitionKeyUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.model.unicast.CmBrandServiceFee;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.parameter.common.mapper.unicast.CmBrandServiceFeeSelfMapper;
import org.apache.commons.collections.CollectionUtils;
import org.mybatis.spring.SqlSessionTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 汇总当日接收的品牌服务费的金额 生成GLAMS流水
 *
 * <AUTHOR>
 * @date 2021/8/4
 */
public class WriteGlamsTasklet implements Tasklet {

    private static final Logger logger = LoggerFactory.getLogger(WriteGlamsTasklet.class);

    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private AccountantGlamsMapper accountantGlamsMapper;
    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;

    private SqlSessionTemplate sqlSessionTemplateCommon;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    public WriteGlamsTasklet(SqlSessionTemplate sqlSessionTemplateCommon) {
        this.sqlSessionTemplateCommon = sqlSessionTemplateCommon;
    }

    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) throws Exception {
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        if (ObjectUtils.isEmpty(organizationInfo)) {
            logger.error("Organization parameter data not found for organization number: {}", OrgNumberUtils.getOrg());
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST);
        }
        CmBrandServiceFeeSelfMapper cmBrandServiceFeeSelfMapper = sqlSessionTemplateCommon.getMapper(CmBrandServiceFeeSelfMapper.class);
        List<CmBrandServiceFee> cmBrandServiceFees = cmBrandServiceFeeSelfMapper.selectByProcessDate(organizationInfo.getNextProcessingDay());
        BigDecimal sumBrandServiceFee = null;
        AccountManagementInfo accountManagementInfo = null;
        if (CollectionUtils.isNotEmpty(cmBrandServiceFees)) {
            for (CmBrandServiceFee cmBrandServiceFee : cmBrandServiceFees) {
                String cardNumber = cmBrandServiceFee.getCardNumber();
                CardAuthorizationInfo authorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(cardNumber, OrgNumberUtils.getOrg());
                if (!ObjectUtils.isEmpty(authorizationInfo)) {
                    if (sumBrandServiceFee == null){
                        sumBrandServiceFee = BigDecimal.ZERO;
                    }
                    sumBrandServiceFee = sumBrandServiceFee.add(cmBrandServiceFee.getBrandServiceFee());
                    String primaryCustomerId = authorizationInfo.getPrimaryCustomerId();
                    List<AccountManagementInfo> accountManagementInfos = accountManagementInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(), primaryCustomerId);
                    accountManagementInfo = accountManagementInfos.get(0);
                }
            }
            if (sumBrandServiceFee != null && accountManagementInfo != null){
                AccountantGlams accountantGlams = new AccountantGlams();
                logger.info("Management account id: {}", accountManagementInfo.getAccountManagementId());
                accountantGlams.setAccountManagementId(accountManagementInfo.getAccountManagementId());
                accountantGlams.setAcctLogo(accountManagementInfo.getProductNumber());
                accountantGlams.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                accountantGlams.setOrganizationNumber(OrgNumberUtils.getOrg());
                accountantGlams.setBranchid("110110");
                accountantGlams.setGlobalFlowNo(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                accountantGlams.setModuleFlag("0");
                accountantGlams.setTxnCode("60001");
                accountantGlams.setPostingDate(organizationInfo.getNextProcessingDay());
                accountantGlams.setPostingCurrencyCode("156");
                accountantGlams.setPostingAmt(sumBrandServiceFee);
                accountantGlams.setFinanceStatus("0");
                accountantGlams.setInterestInd("0");
                accountantGlams.setPriceTaxFlg("Y");
                accountantGlams.setTxnCodeOrig("60001");
                accountantGlams.setTxnInd("0");
                accountantGlams.setBalProcessInd("0");
                accountantGlams.setAbsStatus("N");
                accountantGlams.setOrigTxnAbsInd("0");
                accountantGlams.setProcessInd("0");
                accountantGlams.setChannelId("C");
                accountantGlams.setDebitCreditIndicator("D");
                accountantGlams.setAmortizeInd("N");
                accountantGlams.setCreateTime(LocalDateTime.now());
                accountantGlams.setUpdateTime(LocalDateTime.now());
                accountantGlams.setUpdateBy(LoginUserUtils.getLoginUserName());
                accountantGlams.setVersionNumber(1L);
                accountantGlams.setPartitionKey(PartitionKeyUtils.partitionKey(accountManagementInfo.getCustomerId()));
                accountantGlamsMapper.insertSelective(accountantGlams);
            }
        }
        return RepeatStatus.FINISHED;
    }
}
