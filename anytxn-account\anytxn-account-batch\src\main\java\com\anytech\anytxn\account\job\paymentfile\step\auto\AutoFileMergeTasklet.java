package com.anytech.anytxn.account.job.paymentfile.step.auto;

import com.anytech.anytxn.account.utils.FileUtil;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import com.anytech.anytxn.transaction.base.constants.FileConstants;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.io.*;

/**
 * <AUTHOR>
 * @date 2021/1/5
 * 扣款文件分区合并处理
 */
public class AutoFileMergeTasklet implements Tasklet, StepExecutionListener {

    private static final Logger logger = LoggerFactory.getLogger(AutoFileMergeTasklet.class);

    @Autowired
    private ParmOrganizationInfoSelfMapper organizationInfoSelfMapper;

    /**
     * 分片文件目录前缀
     */
    private final String shardFilePathPre;
    /**
     * 合并后文件完整目录
     */
    private final String commonFilePath;
    /**
     * 文件名称
     */
    private final String fileName;

    private String okFileName;

    public AutoFileMergeTasklet(String shardFilePathPre, String commonFilePath, String fileName) {
        this.shardFilePathPre = shardFilePathPre;
        this.commonFilePath = commonFilePath;
        this.fileName = fileName;
    }

    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) throws Exception {

        String orgNum = OrgNumberUtils.getOrg();
        ParmOrganizationInfo org = organizationInfoSelfMapper.selectByOrganizationNumber(orgNum);

        StopWatch st = new StopWatch();
        st.start();

        logger.info("Shard directory: {}", shardFilePathPre);
        logger.info("Output directory: {}", commonFilePath);
        // 2. 读取sharding目录下分片目录列表
        File[] files = FileUtil.getChildFiles(shardFilePathPre);
        logger.info("Number of files in shard: {}", files.length);
        if (files != null) {
            // 声明公共文件，后续判断存在数据时再构建
            File commonFile = new File(commonFilePath + File.separator + fileName);
            boolean isFirst = true;
            FileOutputStream fos = null;
            OutputStreamWriter osw = null;
            BufferedWriter bw = null;
            // 不存在数据时构建空文件
            FileUtil.mkdirFile(commonFilePath + File.separator + fileName, true);
            okFileName = fileName + FileConstants.OK_SUFFIX;
            try {
                // 读取files中同机构下不同分片的文件
                for (File file : files) {
                    File dataFile = FileUtil.getFileFile(file.getPath() + File.separator + fileName);
                    if (dataFile != null && fileName.equals(dataFile.getName())) {

                        if (bw == null) {
                            fos = new FileOutputStream(commonFile);
                            osw = new OutputStreamWriter(fos,"GBK");
                            bw = new BufferedWriter(osw);
                        }

                        try (BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(dataFile),"GBK"))){
                            // 不断追加到
                            String str;
                            while (StringUtils.isNotEmpty((str = br.readLine()))) {
                                // 空行不处理
                                if (StringUtils.isEmpty(str)) {
                                    break;
                                }
                                // 非第一个文件加入一个换行符
                                if (!isFirst) {
                                    bw.write("\n");
                                }
                                isFirst = false;
                                bw.write(str);
                            }
                        }
                    }
                }
            }finally {
                if (bw != null) {
                    bw.close();
                }
                if (osw != null) {
                    osw.close();
                }
                if (fos != null) {
                    fos.close();
                }
            }
        }

        st.stop();
        logger.info("Deduction file merge completed, organization number: {}, time elapsed: {}ms", org.getOrganizationNumber(), st.getTotalTimeMillis());

        return RepeatStatus.FINISHED;
    }

    @Override
    public void beforeStep(StepExecution stepExecution) {

    }

    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        stepExecution.getJobExecution().getExecutionContext().put("flag", true);
        stepExecution.getJobExecution().getExecutionContext().put("filePath", commonFilePath);
        stepExecution.getJobExecution().getExecutionContext().put("okFileName", okFileName);
        return ExitStatus.COMPLETED;
    }
}
