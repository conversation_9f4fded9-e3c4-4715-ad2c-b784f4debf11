package com.anytech.anytxn.installment.controller.order;

import com.anytech.anytxn.installment.base.domain.dto.InstallAutoSignDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallAutoSignSearchKeyDTO;
import com.anytech.anytxn.installment.base.enums.InstallRepDetailEnum;
import com.anytech.anytxn.installment.base.service.IInstallAutoSignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * anytxn-installment
 * 分期自动分期协议
 *
 * <AUTHOR>
 * @date 2019-07-08
 */
@RestController
@Api(tags = "分期自动分期协议")
public class InstallmentAutoSignController extends BizBaseController {

    private static final Logger logger = LoggerFactory.getLogger(InstallmentAutoSignController.class);

    @Autowired
    private IInstallAutoSignService installAutoSignService;

    @ApiOperation(value = "分页查询分期自动分期协议表", notes = "分页查询分期自动分期协议表")
    @PostMapping(value = "/install/istallautosignbypage")
    public AnyTxnHttpResponse<PageResultDTO<InstallAutoSignDTO>> getInstallmentAutoSignByPage(@RequestBody(required = false) InstallAutoSignSearchKeyDTO installAutoSignSearchKeyDTO) {
        logger.info("Starting get installment auto sign by page");
        PageResultDTO<InstallAutoSignDTO> pageResultDTO = installAutoSignService.findAll(installAutoSignSearchKeyDTO);
        logger.info("Get installment auto sign by page completed successfully: resultCount={}",
                   pageResultDTO != null && pageResultDTO.getRecords() != null ? pageResultDTO.getRecords().size() : 0);
        return AnyTxnHttpResponse.success(pageResultDTO);
    }

    @ApiOperation(value = "添加分期自动分期协议参数", notes = "添加分期自动分期协议参数")
    @PostMapping(value = "/install/istallautosign")
    public AnyTxnHttpResponse<InstallAutoSignDTO> addInstallAutoSign(@RequestBody InstallAutoSignDTO installAutoSignReq) {
        logger.info("Starting add install auto sign: cardNumber={}", installAutoSignReq != null ? installAutoSignReq.getCardNumber() : null);
        InstallAutoSignDTO installAutoSignDTO = installAutoSignService.addInstallAutoSign(installAutoSignReq);
        logger.info("Add install auto sign completed successfully: id={}", installAutoSignDTO != null ? installAutoSignDTO.getId() : null);
        return AnyTxnHttpResponse.success(installAutoSignDTO, InstallRepDetailEnum.AS.message());
    }

    @ApiOperation(value = "修改分期自动分期协议参数", notes = "修改分期自动分期协议参数")
    @PutMapping(value = "/install/istallautosign")
    public AnyTxnHttpResponse<InstallAutoSignDTO> modifyInstallAutoSign(@RequestBody InstallAutoSignDTO installAutoSignReq) {
        logger.info("Starting modify install auto sign: id={}, cardNumber={}",
                   installAutoSignReq != null ? installAutoSignReq.getId() : null,
                   installAutoSignReq != null ? installAutoSignReq.getCardNumber() : null);
        InstallAutoSignDTO installAutoSignDTO = installAutoSignService.modifyInstallAutoSign(installAutoSignReq);
        logger.info("Modify install auto sign completed successfully: id={}", installAutoSignDTO != null ? installAutoSignDTO.getId() : null);
        return AnyTxnHttpResponse.success(installAutoSignDTO, InstallRepDetailEnum.MS.message());
    }

    @ApiOperation(value = "根据卡号和分期类型删除分期自动分期协议参数", notes = "根据卡号和分期类型删除分期自动分期协议参数")
    @PutMapping("/install/istallautosign/cardNumber/{cardNumber}/autoSignType/{autoSignType}/currencyCode/{currencyCode}")
    public AnyTxnHttpResponse<Boolean> remove(@PathVariable String cardNumber, @PathVariable String autoSignType, @PathVariable String currencyCode) {
        logger.info("Starting remove install auto sign: cardNumber={}, autoSignType={}, currencyCode={}", cardNumber, autoSignType, currencyCode);
        Boolean deleted = installAutoSignService.removeInstallAutoSign(cardNumber, autoSignType,currencyCode);
        logger.info("Remove install auto sign completed successfully: cardNumber={}, deleted={}", cardNumber, deleted);
        return AnyTxnHttpResponse.success(deleted, InstallRepDetailEnum.DS.message());
    }

    @ApiOperation(value = "根据id查询分期自动分期协议参数", notes = "根据id查询分期自动分期协议参数")
    @GetMapping("/install/istallautosign/id/{id}")
    public AnyTxnHttpResponse<InstallAutoSignDTO> getById(@PathVariable("id") String id) {
        logger.info("Starting get install auto sign by id: id={}", id);
        InstallAutoSignDTO autoSignServiceById = installAutoSignService.findById(id);
        logger.info("Get install auto sign by id completed successfully: id={}, found={}", id, autoSignServiceById != null);
        return AnyTxnHttpResponse.success(autoSignServiceById);
    }
}
