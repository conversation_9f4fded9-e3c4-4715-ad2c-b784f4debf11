package com.anytech.anytxn.accounting.batch.config.file.newConfigs;

import com.anytech.anytxn.accounting.base.constants.FileConstants;
import com.anytech.anytxn.accounting.base.domain.model.AccountantGlOriRecordDetail;
import com.anytech.anytxn.accounting.base.domain.model.AccountantGlOriRecordDetailHeader;
import com.anytech.anytxn.accounting.service.FileWriteHandle;
import com.anytech.anytxn.accounting.service.GlvcherFileServiceImpl;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcherOriDetailInfo;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.common.service.system.OrganizationInfoServiceImpl;
import com.anytech.batch.job.file.common.FileHeader;
import com.anytech.batch.job.file.handler.TextLineHandler;
import com.anytech.batch.job.file.write.FileBody;
import com.anytech.batch.job.file.write.ParallelWriteFileBatchJob;
import com.anytech.batch.job.file.write.ParallelWriteFileBatchJobConfig;
import com.anytech.batch.job.file.write.SimpleWriteFileBatchJob;
import com.anytech.batch.job.handler.CalcProcessRecordPartition;
import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.Resource;
import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;

@Configuration
public class OriRecordDetailNewConfig extends ParallelWriteFileBatchJobConfig<AccountantGlvcherOriDetailInfo, AccountantGlOriRecordDetail, Long> {
    private static final Logger logger = LoggerFactory.getLogger(OriRecordDetailNewConfig.class);

    private GlvcherFileServiceImpl glDetailService;
    private AnytxnFilePathConfig ordFileFormatPathConfig;
    private OrganizationInfoServiceImpl organizationInfoService;
    private CalcProcessRecordPartition calcProcessRecordPartition;
    @Resource
    private FileWriteHandle fileWriteHandle;

    public OriRecordDetailNewConfig(JobBuilderFactory jobs, StepBuilderFactory steps,
                                    SqlSessionFactory sqlSessionFactory,
                                    AnytxnFilePathConfig ordFileFormatPathConfig,
                                    GlvcherFileServiceImpl glDetailService,
                                    OrganizationInfoServiceImpl organizationInfoService,
                                    CalcProcessRecordPartition oriRecordDetailCalcProcessRecordPartitionImpl) {
        super(jobs, steps, sqlSessionFactory);
        this.ordFileFormatPathConfig = ordFileFormatPathConfig;
        this.glDetailService = glDetailService;
        this.organizationInfoService = organizationInfoService;
        this.calcProcessRecordPartition = oriRecordDetailCalcProcessRecordPartitionImpl;

    }

    @Override
    public ParallelWriteFileBatchJob<AccountantGlvcherOriDetailInfo, AccountantGlOriRecordDetail, Long> getBatchJob() {
        ParallelWriteFileBatchJob<AccountantGlvcherOriDetailInfo, AccountantGlOriRecordDetail, Long> parallelWriteFileBatchJob = ParallelWriteFileBatchJob.of();

        String cdaFilePath = ordFileFormatPathConfig.getCommonPath();
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        String sign = FileConstants.getPathForSystem();
        LocalDate today = organizationInfo.getToday();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyMMdd");
        String format = dateTimeFormatter.format(today);
        HashMap<String, Object> map = new HashMap<>();
        map.put("postingDate", today);

        String fileNamePath = cdaFilePath+ sign + "ORI.RECORD.DETAIL.OUT." + format + ".csv";
        File file = new File(fileNamePath);
        if (!file.exists()) {
            AccountantGlOriRecordDetailHeader headerDto = new AccountantGlOriRecordDetailHeader();
            String head = headerDto.toString();
            fileWriteHandle.outFile(fileNamePath, null);
            fileWriteHandle.inputHeader(file,head);
        }
        SimpleWriteFileBatchJob<AccountantGlvcherOriDetailInfo, AccountantGlOriRecordDetail, Long> batchJob = SimpleWriteFileBatchJob.of();

        batchJob.name("ordFileProcess")
                .path(cdaFilePath)
                .resolveClass(AccountantGlOriRecordDetail.class)
                .fileName("ORI.RECORD.DETAIL.OUT." + format)
                .header(new FileHeader(AccountantGlOriRecordDetailHeader.class, (TextLineHandler) () -> {
                    AccountantGlOriRecordDetailHeader headerDto = new AccountantGlOriRecordDetailHeader();
                    return headerDto;
                }))
                .body(FileBody.<AccountantGlvcherOriDetailInfo, AccountantGlOriRecordDetail, Long>of()
                        .queryId("com.anytech.anytxn.core.accountant.mapper.AccountantGlvcherSelfMapper.queryGlvchersForToday")
                        .parameterValues(map)
                        .processorHandler(detailInfo ->
                                glDetailService.buildOriRecordDetail(detailInfo, organizationInfo)));


        parallelWriteFileBatchJob.simpleWriteFileBatchJob(batchJob)
                .calcPartition(calcProcessRecordPartition)
                .queryMinMaxParam(map)
                .queryTotalParam(map);

        return parallelWriteFileBatchJob;

    }
}
