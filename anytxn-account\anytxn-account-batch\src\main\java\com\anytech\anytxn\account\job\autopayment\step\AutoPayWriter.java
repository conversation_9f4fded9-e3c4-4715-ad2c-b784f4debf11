package com.anytech.anytxn.account.job.autopayment.step;

import com.anytech.anytxn.account.base.domain.bo.AutoPaymentBO;
import com.anytech.anytxn.account.base.service.IAutoPaymentLogHistoryService;
import com.anytech.anytxn.account.base.service.IAutoPaymentLogService;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 * @date 2019-09-02
 **/
public class AutoPayWriter implements ItemWriter<AutoPaymentBO> {
    private static final Logger logger = LoggerFactory.getLogger(AutoPayWriter.class);

    @Autowired
    JdbcTemplate jdbcTemplate;

    @Autowired
    private IAutoPaymentLogService autoPaymentLogService;
    @Autowired
    private IAutoPaymentLogHistoryService autoPaymentLogHistoryService;


    public void write(List<? extends AutoPaymentBO> list) throws Exception {
        logger.info("Calling autoPaymentLogService.batchSave, size={}", list == null ? 0 : list.size());
        autoPaymentLogService.batchSave((List<AutoPaymentBO>) list);
        logger.info("Calling autoPaymentLogHistoryService.batchSave, size={}", list == null ? 0 : list.size());
        //插入自动还款历史
        autoPaymentLogHistoryService.batchSave((List<AutoPaymentBO>) list);
    }

    @Override
    public void write(Chunk<? extends AutoPaymentBO> chunk) throws Exception {
        write(chunk.getItems());
    }
}
