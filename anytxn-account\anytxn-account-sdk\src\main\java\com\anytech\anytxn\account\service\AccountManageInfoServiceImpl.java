package com.anytech.anytxn.account.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.account.base.constants.*;
import com.anytech.anytxn.account.base.domain.bo.AutoPaymentBO;
import com.anytech.anytxn.account.base.domain.bo.GiroAutoPayBO;
import com.anytech.anytxn.account.base.domain.dto.AccountLimitInfoDTO;
import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.enums.RecordedEnum;
import com.anytech.anytxn.account.base.enums.TransactionTypeCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.account.base.service.IAccountManageInfoService;
import com.anytech.anytxn.account.base.utils.AccountingPartitionKeyHelper;
import com.anytech.anytxn.account.base.utils.HttpClientUtils;
import com.anytech.anytxn.business.base.account.domain.bo.ExchangePaymentBO;
import com.anytech.anytxn.business.base.account.domain.dto.AccMaDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.account.domain.dto.InCollectionDTO;
import com.anytech.anytxn.business.base.account.enums.FinanceStatusEnum;
import com.anytech.anytxn.business.base.accounting.enums.CycleDueEnum;
import com.anytech.anytxn.business.base.card.constants.CardBusinessConstant;
import com.anytech.anytxn.business.base.card.domain.dto.AutoEbitSignUpInforDTO;
import com.anytech.anytxn.business.base.common.constants.MaintenanceConstant;
import com.anytech.anytxn.business.base.common.domain.dto.MaintenanceLogDTO;
import com.anytech.anytxn.business.base.common.service.IMaintenanceLogBisService;
import com.anytech.anytxn.business.base.customer.domain.dto.BlockCodeMaintenanceLogDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAuthorizationInfoDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.base.monetary.exception.AnyTxnCustAccountLockException;
import com.anytech.anytxn.business.base.monetary.service.ICustReconciliationControlService;
import com.anytech.anytxn.business.base.transaction.domain.bo.RecordedBO;
import com.anytech.anytxn.business.dao.account.mapper.*;
import com.anytech.anytxn.business.dao.account.model.*;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardBasicInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardVirtualAccountInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.card.model.CardBasicInfo;
import com.anytech.anytxn.business.dao.card.model.CardVirtualAccountInfo;
import com.anytech.anytxn.business.dao.customer.mapper.*;
import com.anytech.anytxn.business.dao.customer.model.BlockCodeMaintenanceLog;
import com.anytech.anytxn.business.dao.customer.model.CustomerAddressInfo;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import com.anytech.anytxn.business.dao.customer.model.CustomerBasicInfo;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.dao.limit.mapper.LimitCustCreditInfoMapper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.enums.*;
import com.anytech.anytxn.common.core.utils.*;
import com.anytech.anytxn.common.rule.dto.DataInputDTO;
import com.anytech.anytxn.common.rule.matcher.RuleMatcherManager;
import com.anytech.anytxn.common.rule.matcher.TxnRuleMatcher;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.limit.base.domain.dto.CustomerLimitInfoDTO;
import com.anytech.anytxn.limit.base.domain.dto.TokenVirtualDTO;
import com.anytech.anytxn.limit.base.enums.VaTaTypeEnum;
import com.anytech.anytxn.limit.base.service.ITokenVirtualInfoService;
import com.anytech.anytxn.limit.service.CustomerLimitInfoService;
import com.anytech.anytxn.parameter.account.mapper.ParmAutoPaymentTableSelfMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmBlockCodeAccountSelfMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmStatementProcessSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.dto.BlockCodeAccountResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlResDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmBlockCodeAccount;
import com.anytech.anytxn.parameter.base.account.service.IBlockCodeAccountService;
import com.anytech.anytxn.parameter.base.account.service.IDelinquentControlService;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardCurrencyInfo;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardProductInfo;
import com.anytech.anytxn.parameter.base.common.domain.dto.CurrencyRateResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCtrlUnitDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCtrlUnitDebitDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CardProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.ProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SystemTableDTO;
import com.anytech.anytxn.parameter.base.common.domain.model.IFProductInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmProductInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmTransactionType;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.common.service.ICurrencyRateService;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeConfigService;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCtrlUnitService;
import com.anytech.anytxn.parameter.base.common.service.product.ICardProductInfoService;
import com.anytech.anytxn.parameter.base.common.service.product.IProductInfoService;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.parameter.base.common.service.system.ISystemTableService;
import com.anytech.anytxn.parameter.base.monetary.bo.CustAccountParamBO;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardCurrencyInfoSelfMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardProductInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmAcctProductMainInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmProductInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmTransactionTypeMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ibatis.exceptions.PersistenceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 账户管理信息
 *
 * <AUTHOR>
 * @date 2018-09-29
 */
@Service
public class AccountManageInfoServiceImpl implements IAccountManageInfoService {
    private static final Logger logger = LoggerFactory.getLogger(AccountManageInfoServiceImpl.class);
    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;
    @Autowired
    private AccountBalanceInfoSelfMapper accountBalanceInfoSelfMapper;
    @Autowired
    private AccountStatisticsInfoSelfMapper accountStatisticsInfoSelfMapper;
    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;

    @Autowired
    private  AccountLoyaltyInfoMapper accountLoyaltyInfoMapper;
    @Autowired
    private AccountStatementInfoSelfMapper accountStatementInfoSelfMapper;
    @Autowired
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;
    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private ITransactionCodeConfigService transactionCodeConfigService;
    @Autowired
    private BlockCodeMaintenanceLogMapper blockCodeMaintenanceLogMapper;

    @Autowired
    private CardBasicInfoSelfMapper cardBasicInfoSelfMapper;
    private static final String PAYMENT_HISTORY = "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX";
    @Autowired
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;
    @Autowired
    private IProductInfoService productInfoService;
    @Autowired
    private CustomerBasicInfoSelfMapper customerBasicInfoSelfMapper;
    @Autowired
    private CustomerBasicInfoMapper customerBasicInfoMapper;
    @Autowired
    private InstallOrderSelfMapper installOrderSelfMapper;
    @Autowired
    private IBlockCodeAccountService blockCodeAccountService;
    @Autowired
    private IDelinquentControlService delinquentControlService;
    @Autowired
    private ICurrencyRateService currencyRateService;
    @Autowired
    private ITransactionCodeService transactionCodeService;
    @Autowired
    private ISystemTableService systemTableService;
    @Autowired
    private ICustReconciliationControlService custReconciliationControlService;
    @Autowired
    private IMaintenanceLogBisService maintenanceLogService;
    @Autowired
    LimitCustCreditInfoMapper custCreditInfoMapper;
    @Autowired
    private ParmTransactionTypeMapper parmTransactionTypeMapper;
    @Autowired
    private ITransactionCtrlUnitService transactionCtrlUnitService;
    @Autowired
    private ParmCardProductInfoSelfMapper cardProductInfoSelfMapper;
    @Autowired
    private CustomerAddressInfoSelfMapper customerAddressInfoSelfMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;


    /*    @Value("${ptsecif.url}")
    private String PtsEcifUri;
    @Value("${ptscust.url}")
    private String PtsCustUri;
    @Value("${custcard.url}")
    private String CustCardUri;
    @Value("${flag}")
    private Boolean flag;*/
    @Autowired
    private ParmBlockCodeAccountSelfMapper parmBlockCodeAccountSelfMapper;
    @Autowired
    private ParmProductInfoSelfMapper parmProductInfoSelfMapper;
    @Autowired
    private CustomerAuthorizationInfoMapper customerAuthorizationInfoMapper;
    @Autowired
    private ICardProductInfoService cardProductInfoService;
    @Autowired
    private CustomerLimitInfoService customerLimitInfoService;
    @Autowired
    private ParmAutoPaymentTableSelfMapper parmAutoPaymentTableSelfMapper;
    @Autowired
    private ParmStatementProcessSelfMapper parmStatementProcessSelfMapper;
    @Autowired
    private ParmAcctProductMainInfoSelfMapper parmAcctProductMainInfoSelfMapper;
    @Autowired
    private com.anytech.anytxn.parameter.common.mapper.product.IFProductInfoMapper ifProductInfoMapper;
    @Autowired
    private ParmCardProductInfoSelfMapper parmCardProductInfoSelfMapper;
    @Autowired
    private CardVirtualAccountInfoSelfMapper cardVirtualAccountInfoSelfMapper;
    @Autowired
    private ITokenVirtualInfoService tokenVirtualInfoService;

    @Resource
    private ChargeOffInfoServiceImpl chargeOffInfoService;
    @Resource
    private AccountStatisticsInfoMapper statisticsInfoMapper;

    @Autowired
    private ParmCardCurrencyInfoSelfMapper parmCardCurrencyInfoSelfMapper;

    public static final List<String> accountStatusOfERSW = Arrays.asList("SA", "VA", "FA", "CA",
            "WA", "NA", "XA", "NS", "FS", "OA", "BA", "GA", "RA", "DA");


    @Override
    public PageResultDTO<AccountManagementInfoDTO> queryByCustomerId(
            String customerId, int pageNum, int pageSize) {
        logger.info("Query paginated account management info by customer id: customerId={}", customerId);
        PageHelper.startPage(pageNum, pageSize);
        List<AccountManagementInfoDTO> accountManagementInfoDtos = null;
        try {
            List<AccountManagementInfo> accountManagementInfos =
                    accountManagementInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(),customerId);
            if (!CollectionUtils.isEmpty(accountManagementInfos)) {
                accountManagementInfoDtos = new ArrayList<>();
                BeanCopier copierAccountManagementModelToDTO =
                        BeanCopier.create(AccountManagementInfo.class, AccountManagementInfoDTO.class, false);
                for (AccountManagementInfo info : accountManagementInfos) {
                    AccountManagementInfoDTO dto = new AccountManagementInfoDTO();
                    copierAccountManagementModelToDTO.copy(info, dto, null);
                    accountManagementInfoDtos.add(dto);
                }
            }
        } catch (Exception e) {
            logger.error("Failed to query account management info by customer ID: customerId={}", customerId, e);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR);
        }
        PageInfo<AccountManagementInfoDTO> pageInfo = new PageInfo<>(accountManagementInfoDtos);
        return new PageResultDTO<>(
                pageNum, pageSize, pageInfo.getTotal(), pageInfo.getPages(), accountManagementInfoDtos);
    }

    @Override
    public List<AccountManagementInfoDTO> findListByCustomerId(String customerId) {
        logger.info("Query all account management info by customer id: customerId={}", customerId);
        String organizationNumber = OrgNumberUtils.getOrg();
        List<CardAuthorizationInfo> primaryCardAuths =
                cardAuthorizationInfoSelfMapper.selectByPrimaryCustomerId(organizationNumber,customerId);
        if (CollectionUtils.isEmpty(primaryCardAuths)) {
            List<CardAuthorizationInfo> supCardAuths =
                    cardAuthorizationInfoSelfMapper.selectBySupplementaryCustomerCustomerId(organizationNumber,customerId);
            if (CollectionUtils.isEmpty(supCardAuths)) {
                logger.error("Card authorization info not found for customer id: customerId={}", customerId);
            } else {
                customerId = supCardAuths.get(0).getPrimaryCustomerId();
            }
        } else {
            customerId = primaryCardAuths.get(0).getPrimaryCustomerId();
        }
        List<AccountManagementInfo> accountManagementInfos =
                accountManagementInfoSelfMapper.selectByCustomerId(organizationNumber,customerId);
        //筛选出公司清偿的活跃公司卡，根据公司客户号、产品和币种查询账户
        primaryCardAuths.stream()
                .filter(p -> LiabilityEnum.CORPORATE.getCode().equals(p.getLiability()))
                .forEach(p -> {
                    List<ParmCardCurrencyInfo> parmCardCurrencyInfoList = parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum(organizationNumber, p.getProductNumber());
                    ParmCardProductInfo parmCardProductInfo = parmCardProductInfoSelfMapper.selectByOrgAndProductNum(organizationNumber, p.getProductNumber());
                    for (ParmCardCurrencyInfo parmCardCurrencyInfo : parmCardCurrencyInfoList) {
                        AccountManagementInfo accountManagementInfo = accountManagementInfoSelfMapper.selectByCorpCusIdAndProNumAndOrgNoAndCurrency(p.getCorporateCustomerId(), parmCardProductInfo.getAccountProductNumber(), organizationNumber, parmCardCurrencyInfo.getCurrencyCode());
                        if (Objects.nonNull(accountManagementInfo)) {
                            accountManagementInfos.add(accountManagementInfo);
                        }
                    }
                });
        if(CollectionUtils.isEmpty(accountManagementInfos)){
            return null;
        }
        List<AccountManagementInfoDTO> res = BeanMapping.copyList(accountManagementInfos, AccountManagementInfoDTO.class);
        for(AccountManagementInfoDTO accountManagementInfoDTO : res){
            AccountStatementInfo accountStatementInfoMax = accountStatementInfoSelfMapper.selectAllByOrgCusAndDate(accountManagementInfoDTO.getOrganizationNumber(),accountManagementInfoDTO.getAccountManagementId());
            if(!ObjectUtils.isEmpty(accountStatementInfoMax)){
                accountManagementInfoDTO.setCloseBalance(accountStatementInfoMax.getCloseBalance());
            }
        }
        return res;
    }

    /**
     * 根据查询类型（卡号-C,证件号：I）、查询值(卡号/证件号)，查询管理账户
     *
     * @param flag   flag
     * @param idType 证件类型
     * @param number number
     * @return List<AccountManagementInfoDTO>
     */
    @Override
    public List<AccountManagementInfoDTO> findListBySearchTypeAndNum(String flag, String idType, String number, String partnerId) {
        String organizationNumber = OrgNumberUtils.getOrg();
        List<ParmCardProductInfo> accountProductList = cardProductInfoSelfMapper.selectCardProductsByOrg(organizationNumber);
        List<AccountManagementInfoDTO> accountManagementInfos = new ArrayList<>();
        if ("I".equals(flag)) {
            //根据证件号查询
            String[] split = number.split("-");
            List<CustomerAuthorizationInfo> authInfos = new ArrayList<>();
            if (split.length > 1) {
                StringBuilder splitIdNumber = new StringBuilder(split[1]);
                for (int i = 2; i < split.length; i++) {
                    splitIdNumber.append("-").append(split[i]);
                }
                CustomerAuthorizationInfo customerAuthorizationInfo = customerAuthorizationInfoSelfMapper.selectByIdTypeAndIdNumberAndPartnerId(
                        organizationNumber, split[0], splitIdNumber.toString(), StringUtils.isBlank(partnerId) || "null".equals(partnerId) ? null : partnerId);
                authInfos.add(customerAuthorizationInfo);
            } else {
                authInfos = customerAuthorizationInfoSelfMapper.selectByIdNumber(organizationNumber, number.trim());
            }
            if (authInfos.isEmpty()) {
                return null;
            } else if (authInfos.size() > 1) {
                logger.error("Duplicate customer found for ID number: number={}", number);
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_RESULT_MULTIPLE, AccountingRepDetailEnum.DC_CN);
            }
            CustomerAuthorizationInfo authInfo = authInfos.get(0);
            if (authInfo != null) {
                String customerId = authInfo.getCustomerId();
                List<CardAuthorizationInfo> subCardAuthList = cardAuthorizationInfoSelfMapper.selectByOrgAndSupplementaryCustomerId(organizationNumber,customerId);
                List<AccountManagementInfo> accountManagementInfoList = accountManagementInfoSelfMapper.selectByCustomerIdWithClosedAcct(organizationNumber,customerId);
                List<AccountManagementInfo> suppAccountManagementInfoList = accountManagementInfoSelfMapper.selectBySuppCustomerIdWithClosedAcct(organizationNumber, customerId);
                List<String> cardProductKeys = Optional.ofNullable(subCardAuthList).orElseGet(Collections::emptyList).stream().map(e -> e.getProductNumber()).collect(Collectors.toList());
                List<String> accountProductKeys = Optional.ofNullable(accountProductList).orElseGet(Collections::emptyList).stream().filter(e->cardProductKeys.contains(e.getProductNumber())).map(e -> e.getAccountProductNumber()).collect(Collectors.toList());
                suppAccountManagementInfoList = Optional.ofNullable(suppAccountManagementInfoList).orElseGet(Collections::emptyList).stream().filter(e -> accountProductKeys.contains(e.getProductNumber())).collect(Collectors.toList());
                accountManagementInfoList.addAll(suppAccountManagementInfoList);
                List<AccountManagementInfoDTO> accountManagementInfoDTOS = BeanMapping.copyList(accountManagementInfoList, AccountManagementInfoDTO.class);
                addAccountLoyaltyProjectId(accountManagementInfoDTOS);
                return accountManagementInfoDTOS;
            }
        } else if ("C".equals(flag)) {
            //根据卡号查询
            CardAuthorizationInfo cardAuth = cardAuthorizationInfoMapper.selectByPrimaryKey(number == null ? null :
                    number.trim(), organizationNumber);
            if (cardAuth != null) {
                String customerId = cardAuth.getPrimaryCustomerId();
                List<AccountManagementInfo> accountManagementInfoList = accountManagementInfoSelfMapper.selectByCustomerIdWithClosedAcct(organizationNumber,customerId);
                if(cardAuth.getRelationshipIndicator().equals(CardBusinessConstant.SUPPLEMENT_CUSTOMER_INDICATOR)){
                    List<String> accountProductKeys = Optional.ofNullable(accountProductList).orElseGet(Collections::emptyList).stream().filter(e->cardAuth.getProductNumber().equals(e.getProductNumber())).map(e -> e.getAccountProductNumber()).collect(Collectors.toList());
                    accountManagementInfoList = Optional.ofNullable(accountManagementInfoList).orElseGet(Collections::emptyList).stream().filter(e -> accountProductKeys.contains(e.getProductNumber())).collect(Collectors.toList());
                }
                //添加账户权益信息ID
                List<AccountManagementInfoDTO> accountManagementInfoDTOS = BeanMapping.copyList(accountManagementInfoList, AccountManagementInfoDTO.class);
                addAccountLoyaltyProjectId(accountManagementInfoDTOS);
                return accountManagementInfoDTOS;
            }
        }else if("M".equals(flag)){
            AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(number);
            if(Objects.nonNull(accountManagementInfo)){
                List<AccountManagementInfo> accountManagementInfoList = accountManagementInfoSelfMapper.selectByCustomerIdWithClosedAcct(organizationNumber,accountManagementInfo.getCustomerId());
                List<AccountManagementInfoDTO> accountManagementInfoDTOS = BeanMapping.copyList(accountManagementInfoList, AccountManagementInfoDTO.class);
                addAccountLoyaltyProjectId(accountManagementInfoDTOS);
                return accountManagementInfoDTOS;
            }
        }
        return accountManagementInfos;
    }

    /**
     * 添加账户权益ID
     * @param accountManagementInfoDTOS
     */
    private void addAccountLoyaltyProjectId(List<AccountManagementInfoDTO> accountManagementInfoDTOS) {
        if (CollectionUtils.isNotEmpty(accountManagementInfoDTOS)){
            accountManagementInfoDTOS.forEach(e->{
                AccountLoyaltyInfo accountLoyaltyInfo = accountLoyaltyInfoMapper.selectByAccountManagementId(e.getAccountManagementId(), OrgNumberUtils.getOrg());
                if(Objects.nonNull(accountLoyaltyInfo)){
                    e.setLoyaltyProjectId(accountLoyaltyInfo.getProjectId());
                }
            });
        }
    }

    @Override
    public AccountManagementInfoDTO findAccManInfo(String accountManagementId) {
        logger.info("Query account management info by id: accountManagementId={}", accountManagementId);
        AccountManagementInfoDTO dto = null;
        try {
            AccountManagementInfo info =
                    accountManagementInfoMapper.selectByPrimaryKey(accountManagementId);
            if (null != info) {
                dto = new AccountManagementInfoDTO();
                BeanCopier copierAccountManagementModelToDTO =
                        BeanCopier.create(AccountManagementInfo.class, AccountManagementInfoDTO.class, false);
                copierAccountManagementModelToDTO.copy(info, dto, null);
//                List<AccountStatisticsInfo> accountStatisticsInfos = accountStatisticsInfoSelfMapper.selectAsiByMid(dto.getAccountManagementId());
//                BigDecimal sumCycleToDateAmount = accountStatisticsInfos.stream().filter(x -> "00000".equals(x.getTransactionTypeCode())).map(AccountStatisticsInfo::getCycleToDateCreditAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//                BigDecimal sumCycleToDateCreditAmount = accountStatisticsInfos.stream().filter(x -> !"00000".equals(x.getTransactionTypeCode()) && !"OV999".equals(x.getTransactionTypeCode()))
//                        .map(AccountStatisticsInfo::getCycleToDateCreditAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//                dto.setTotalPaymentAmount(sumCycleToDateAmount.subtract(sumCycleToDateCreditAmount));
//                AccountManagementInfo accountManagementInfo = new AccountManagementInfo();
//                accountManagementInfo.setAccountManagementId(accountManagementId);
//                accountManagementInfo.setTotalPaymentAmount(dto.getTotalPaymentAmount());
//                accountManagementInfo.setVersionNumber(dto.getVersionNumber());
//                accountManagementInfoMapper.updateByPrimaryKeySelective(accountManagementInfo);
            }
        } catch (Exception e) {
            logger.error("AccountManageInfoServiceImpl findAccManInfo error", e);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR);
        }
        return dto;
    }

    @Override
    public AccountManagementInfoDTO findAccManInfoByErn(String externalReferenceNumber, String orgNum) {
        logger.info("Query account management info by external reference number: externalReferenceNumber={}", externalReferenceNumber);
        AccountManagementInfoDTO dto = null;
        try {
            AccountManagementInfo info = accountManagementInfoMapper.selectByExternalReferenceNumber(externalReferenceNumber, orgNum);
            if (null != info) {
                dto = new AccountManagementInfoDTO();
                BeanCopier copierAccountManagementModelToDTO =
                        BeanCopier.create(AccountManagementInfo.class, AccountManagementInfoDTO.class, false);
                copierAccountManagementModelToDTO.copy(info, dto, null);
            }
        } catch (Exception e) {
            logger.error("AccountManageInfoServiceImpl findAccManInfoByErn error", e);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR);
        }
        return dto;
    }

    /**
     * 根据机构号、管理账户id查询管理账户明细
     *
     * @param orgNum              机构号
     * @param accountManagementId 管理账户id
     * @return 管理账户
     */
    @Override
    public AccountManagementInfoDTO findByOrgNumAndMid(String orgNum, String accountManagementId) {
        AccountManagementInfo accountManagementInfo =
                accountManagementInfoSelfMapper.getByOrgNumAndMid(orgNum, accountManagementId);
        AccountManagementInfoDTO accountManagementInfoDto = null;
        if (accountManagementInfo != null) {
            accountManagementInfoDto = new AccountManagementInfoDTO();
            BeanCopier copierAccountManagementDtoToModel = BeanCopier.create(AccountManagementInfo.class,
                    AccountManagementInfoDTO.class, false);
            copierAccountManagementDtoToModel.copy(accountManagementInfo, accountManagementInfoDto, null);
        }
        return accountManagementInfoDto;
    }

    /**
     * 其他后端程序用修改管理账户信息。
     * 与画面修改分开，画面修改用modifyAccountManagementInfoCms方法
     * 2020/02/24 去掉原画面遗留的验证条件，单纯修改。封锁码修改历史逻辑保留 by zhangnan
     *
     * @param newAccountManagementInfo 待更新管理账户信息
     */
    @Override
    public void modifyAccountManagementInfo(AccountManagementInfoDTO newAccountManagementInfo) {
        if (null != newAccountManagementInfo) {
            // 客户对账控表
            CustReconciliationControlDTO controlDTO;
            // 机构
            OrganizationInfoResDTO organizationInfo;
            // 库中管理账户(参数中管理账户信息可能不全)
            AccountManagementInfoDTO oldManagementInfo;
            // 入库对象
            AccountManagementInfoDTO saveManagementInfo;
            // 批中使用内存查询
            if (CustAccountBO.isBatch()) {
                CustAccountBO<CustAccountParamBO> custAccountBO = CustAccountBO.threadCustAccountBO.get();
                controlDTO = custAccountBO.getCustReconciliationControl();
                oldManagementInfo = newAccountManagementInfo;
                saveManagementInfo = newAccountManagementInfo;
                organizationInfo = custAccountBO.getParams().getOrganizationInfoResDTO();
            // 非批中进行数据库查询
            } else {
                // 查询对账表
                logger.info("Calling custReconciliationControlService.getControl:   customerId={}, organizationNumber={}", newAccountManagementInfo.  getCustomerId(), newAccountManagementInfo.getOrganizationNumber   ());
                controlDTO = custReconciliationControlService.getControl    (newAccountManagementInfo.getCustomerId(),  newAccountManagementInfo.getOrganizationNumber());
                logger.info("Called custReconciliationControlService.getControl     successfully");
                // 查询管理站户
                AccountManagementInfo info = accountManagementInfoMapper.   selectByPrimaryKey(newAccountManagementInfo.getAccountManagementId ());
                oldManagementInfo = info == null ? null : BeanMapping.copy(info,    AccountManagementInfoDTO.class);
                // 构建存储容器
                saveManagementInfo = new AccountManagementInfoDTO();
                BeanMapping.copy(newAccountManagementInfo, saveManagementInfo);
                // 查询机构
                logger.info("Calling organizationInfoService.   findOrganizationInfo: organizationNumber={}",  newAccountManagementInfo.getOrganizationNumber());
                organizationInfo = organizationInfoService.findOrganizationInfo (newAccountManagementInfo.getOrganizationNumber());
                logger.info("Called organizationInfoService.findOrganizationInfo    successfully");
            }

            //如果有修改标志,需要记录标志代号维护历史
            if (null != oldManagementInfo) {
                // 账户封锁码更新逻辑
                String oldBlockCode = StringUtils.isBlank(oldManagementInfo.getBlockCode()) ? StringUtils.EMPTY : oldManagementInfo.getBlockCode();
                String newBlockCode = StringUtils.isBlank(newAccountManagementInfo.getBlockCode()) ? StringUtils.EMPTY : newAccountManagementInfo.getBlockCode();
                if (!oldBlockCode.equals(newBlockCode)) {
                    //更新账户封锁码字段
                    saveManagementInfo.setPreviousBlockCode(oldManagementInfo.getBlockCode());
                    saveManagementInfo.setPreviousBlockCodeSetDate(oldManagementInfo.getBlockCodeSetDate());
                    saveManagementInfo.setBlockCode(oldManagementInfo.getBlockCode());
                    saveManagementInfo.setBlockCodeSetDate(organizationInfo.getToday());
                    saveManagementInfo.setUpdateTime(LocalDateTime.now());
                    saveManagementInfo.setUpdateBy(LoginUserUtils.getLoginUserName());
                    saveManagementInfo.setVersionNumber(oldManagementInfo.getVersionNumber());

                    BlockCodeMaintenanceLogDTO blockCodeMaintenanceLog = new BlockCodeMaintenanceLogDTO();
                    blockCodeMaintenanceLog.setLogId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                    blockCodeMaintenanceLog.setKey(oldManagementInfo.getAccountManagementId());
                    blockCodeMaintenanceLog.setKeyType(CardBusinessConstant.KEY_TYPE_A);
                    blockCodeMaintenanceLog.setBranchNumber(oldManagementInfo.getOrganizationNumber());
                    blockCodeMaintenanceLog.setBlockCodeBefore(oldManagementInfo.getBlockCode());
                    blockCodeMaintenanceLog.setBlockCodeDateBefore(oldManagementInfo.getBlockCodeSetDate());
                    blockCodeMaintenanceLog.setPreviousBlockCodeBefore(oldManagementInfo.getPreviousBlockCode());
                    blockCodeMaintenanceLog.setPreviousBlockDateBefore(oldManagementInfo.getPreviousBlockCodeSetDate());
                    blockCodeMaintenanceLog.setPreBlockStopDateBefore(oldManagementInfo.getPreviousBlockCodeSetDate());
                    //更新后
                    blockCodeMaintenanceLog.setBlockCodeAfter(saveManagementInfo.getBlockCode());
                    blockCodeMaintenanceLog.setBlockCodeDateAfter(saveManagementInfo.getBlockCodeSetDate());
                    blockCodeMaintenanceLog.setPreviousBlockCodeAfter(saveManagementInfo.getPreviousBlockCode());
                    blockCodeMaintenanceLog.setPreBlockDateAfter(saveManagementInfo.getPreviousBlockCodeSetDate());
                    blockCodeMaintenanceLog.setPreBlockStopDateAfter(null);
                    blockCodeMaintenanceLog.setCreateTime(LocalDateTime.now());
                    blockCodeMaintenanceLog.setUpdateTime(LocalDateTime.now());
                    blockCodeMaintenanceLog.setUpdateBy(LoginUserUtils.getLoginUserName());
                    blockCodeMaintenanceLog.setVersionNumber(1);
                    if (CustAccountBO.isBatch()) {
                        CustAccountBO.threadCustAccountBO.get().insertBlockCodeMaintenanceLogList(blockCodeMaintenanceLog);
                    } else {
                        blockCodeMaintenanceLogMapper.insertSelective(BeanMapping.copy(blockCodeMaintenanceLog, BlockCodeMaintenanceLog.class));
                    }
                }
                // 账单日更新逻辑
                // 注意： newManagementInfo 和 oldManagementInfo 在批次中为一个对象永远不回走此逻辑
                if (!newAccountManagementInfo.getCycleDay().equals(oldManagementInfo.getCycleDay())) {
                    // 更新客户基础表
                    CustomerBasicInfo customerBasicInfo = customerBasicInfoSelfMapper.selectByOrgAndCustId(newAccountManagementInfo.getOrganizationNumber(), newAccountManagementInfo.getCustomerId());
                    if (customerBasicInfo == null) {
                        logger.error("Customer basic info not found: customerId={}", newAccountManagementInfo.getCustomerId());
                        throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.CB_NF);
                    }

                    customerBasicInfo.setCycleDay(newAccountManagementInfo.getCycleDay());
                    customerBasicInfo.setUpdateBy(newAccountManagementInfo.getUpdateBy());
                    customerBasicInfo.setUpdateTime(newAccountManagementInfo.getUpdateTime());
                    int customerBasicInfoResult = customerBasicInfoMapper.updateByPrimaryKeySelective(customerBasicInfo);
                    if (customerBasicInfoResult == 0) {
                        logger.error("Failed to update customer basic info cycle day: customerId={}", newAccountManagementInfo.getCustomerId());
                        throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_UPDATE_FAIL);
                    }
                }
            }

            if (!CustAccountBO.isBatch()) {
                // 超长免息期追加，更新所有交易账户中的实际出账单日、实际免息结息日
                modifyAccountBalanceCycleDay(newAccountManagementInfo.getAccountManagementId(), newAccountManagementInfo.getOrganizationNumber(), newAccountManagementInfo.getCycleDay());
                int result = accountManagementInfoMapper.updateByPrimaryKeySelective(BeanMapping.copy(saveManagementInfo, AccountManagementInfo.class));
                if (result == 0) {
                    logger.error("Failed to update account management info: customerId={}", newAccountManagementInfo.getCustomerId());
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_UPDATE_FAIL);
                }
                // 更新客户对账控制表中的乐观锁计数字段 2020-05-13 zcli
                if (null != controlDTO) {
                    logger.info("Calling custReconciliationControlService.commitLock: customerId={}", controlDTO.getCustomerId());
                    custReconciliationControlService.commitLock(controlDTO);
                    logger.info("Called custReconciliationControlService.commitLock successfully");
                }
            } else {
                // 超长免息期追加，更新所有交易账户中的实际出账单日、实际免息结息日(批量更新方式)
                CustAccountBO<Object> custAccountBO = CustAccountBO.threadCustAccountBO.get();
                custAccountBO.getCustomerBO().getBalanceByManagementId(newAccountManagementInfo.getAccountManagementId())
                        .forEach(balanceInfo -> {
                            balanceInfo.setActualStatementDate(cycleDayModify(newAccountManagementInfo.getCycleDay(), balanceInfo.getActualStatementDate()));
                            balanceInfo.setActualInterestBillingDate(cycleDayModify(newAccountManagementInfo.getCycleDay(), balanceInfo.getActualInterestBillingDate()));

                            // >> 更新入容器
                            custAccountBO.getCustomerBO().updateBalanceInfo(balanceInfo, true);
                        });
            }
        }
    }

    /**
     * 更新交易账户的账单日或免息结息日
     * @param accountManagementInfoId
     * @param orgNum
     * @param newCycleDayShort
     */
    private void modifyAccountBalanceCycleDay(String accountManagementInfoId, String orgNum, Short newCycleDayShort) {
        List<AccountBalanceInfo> accountBalanceInfos = accountBalanceInfoSelfMapper.selectByAccountManagementIdAndOrgForCycleDayModify(accountManagementInfoId, orgNum);
        accountBalanceInfos.forEach(accountBalanceInfo -> {
            LocalDate newActualStatementDate = cycleDayModify(newCycleDayShort, accountBalanceInfo.getActualStatementDate());
            LocalDate newActualInterestBillingDate = cycleDayModify(newCycleDayShort, accountBalanceInfo.getActualInterestBillingDate());
            int result = accountBalanceInfoSelfMapper.updateForCycleDayModify(newActualStatementDate,
                    newActualInterestBillingDate, accountBalanceInfo.getTransactionBalanceId());
            if (result != 1) {
                logger.error("Failed to update transaction account cycle day: accountManagementInfoId={}", accountManagementInfoId);
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_UPDATE_FAIL, AccountingRepDetailEnum.UT_AB);
            }
        });
    }

    /**
     * 获取更新后的日期
     * 如果在原日期后，改到本月，如果在原日期前，改到下月。
     * @param newCycleDay
     * @param oldCycleDay
     * @return
     */
    private LocalDate cycleDayModify(Short newCycleDay, LocalDate oldCycleDay) {
        if (oldCycleDay == null) {
            logger.error("Transaction account cycle day or interest free date is null");
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL, AccountingRepDetailEnum.OAD_IF);
        }
        if (newCycleDay < oldCycleDay.getDayOfMonth()) {
            LocalDate nextMonth = oldCycleDay.plusMonths(1);
            return LocalDate.of(nextMonth.getYear(), nextMonth.getMonth(), newCycleDay);
        } else {
            return LocalDate.of(oldCycleDay.getYear(), oldCycleDay.getMonth(), newCycleDay);
        }
    }

    @Override
    public AccountManagementInfoDTO getAccountManagementInfo(String accountId) {
        AccountManagementInfoDTO dto = null;
        try {
            AccountManagementInfo accountManagementInfo =
                    accountManagementInfoMapper.selectByPrimaryKey(accountId);
            if (null == accountManagementInfo) {
                logger.error("Account management info not found: accountId={}", accountId);
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.MA_E);
            }
            dto = new AccountManagementInfoDTO();
            BeanCopier copierAccountManagementModelToDTO =
                    BeanCopier.create(AccountManagementInfo.class, AccountManagementInfoDTO.class, false);
            copierAccountManagementModelToDTO.copy(accountManagementInfo, dto, null);
        } catch (Exception e) {

            if (e instanceof AnyTxnAccountingException) {
                throw e;
            } else {
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR, AccountingRepDetailEnum.Q_AM, accountId);
            }
        }
        return dto;
    }

    @Override
    public void addAccManInfo(AccMaDTO accMaDto) {
        AccountManagementInfo accountManagementInfo = new AccountManagementInfo();
        accountManagementInfo.setAccountManagementId(accMaDto.getAccountManagementId());
        accountManagementInfo.setCurrency(accMaDto.getCurrency());
        accountManagementInfo.setCloseDate(Constants.INITIAL_DATE);
        accountManagementInfo.setAutoExchangeIndicator(AutoExIndicator.CLOSE.getCode());
        accountManagementInfo.setAccountStatus(AccountStatusEnum.NEW.getCode());
        accountManagementInfo.setAccountStatusSetDate(Constants.INITIAL_DATE);
        accountManagementInfo.setFinanceStatus(FinanceStatusEnum.NORMAL.getCode());
        accountManagementInfo.setFinanceStatusSetDate(accMaDto.getOpenDate());
        accountManagementInfo.setWaiveLateFeeFlg(WaiveLateFeeFlg.NOT_EXEMPT.getCode());
        accountManagementInfo.setWaiveInterestFlg(WaiveInterestFlg.ZERO.getCode());
        accountManagementInfo.setWaiveLateFeeNum(0);
        accountManagementInfo.setLastStatementDate(Constants.INITIAL_DATE);
        accountManagementInfo.setPreviousStatementDate(Constants.INITIAL_DATE);
        accountManagementInfo.setBlockCodeSetDate(Constants.INITIAL_DATE);
        accountManagementInfo.setPreviousBlockCodeSetDate(Constants.INITIAL_DATE);
        accountManagementInfo.setChargeOffDate(Constants.INITIAL_DATE);
        accountManagementInfo.setLastCycleCreditAdjAmount(BigDecimal.ZERO);
        accountManagementInfo.setTotalGracePaymentAmount(BigDecimal.ZERO);
        accountManagementInfo.setTotalDueAmount(BigDecimal.ZERO);
        accountManagementInfo.setCurrentDueAmount(BigDecimal.ZERO);
        accountManagementInfo.setPastDueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay30DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay60DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay90DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay120DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay150DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay180DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay210DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay240DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay270DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay300DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay330DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay360DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setDay390DueAmount(BigDecimal.ZERO);
        accountManagementInfo.setCycleDue(CycleDueEnum.NO_DEBIT.getCode());
        accountManagementInfo.setLastAgedDate(Constants.INITIAL_DATE);
        accountManagementInfo.setPastDueCount(0);
        accountManagementInfo.setDay30DueCount(0);
        accountManagementInfo.setDay60DueCount(0);
        accountManagementInfo.setDay90DueCount(0);
        accountManagementInfo.setDay120DueCount(0);
        accountManagementInfo.setDay150DueCount(0);
        accountManagementInfo.setDay180DueCount(0);
        accountManagementInfo.setDay210DueCount(0);
        accountManagementInfo.setDay240DueCount(0);
        accountManagementInfo.setDay270DueCount(0);
        accountManagementInfo.setDay300DueCount(0);
        accountManagementInfo.setDay330DueCount(0);
        accountManagementInfo.setDay360DueCount(0);
        accountManagementInfo.setDay390DueCount(0);
        accountManagementInfo.setPaymentHistory(PAYMENT_HISTORY);
        accountManagementInfo.setInCollectionIndicator(InCollectionIndicator.NOT_COLLECTION.getCode());
        accountManagementInfo.setAutoPaymentFirstDate(Constants.INITIAL_DATE);
        accountManagementInfo.setAutoPaymentSecondDate(Constants.INITIAL_DATE);
        accountManagementInfo.setCreateTime(LocalDateTime.now());
        accountManagementInfo.setUpdateTime(LocalDateTime.now());
        accountManagementInfo.setUpdateBy(LoginUserUtils.getLoginUserName());
        accountManagementInfo.setVersionNumber(1L);

        accountManagementInfo.setCustomerId(accMaDto.getCustomerId());
        accountManagementInfo.setOrganizationNumber(accMaDto.getOrganizationNumber());
        accountManagementInfo.setProductNumber(accMaDto.getProductNumber());
        accountManagementInfo.setBranchNumber(accMaDto.getBranchNumber());
        accountManagementInfo.setNetpointNumber(accMaDto.getNetpointNumber());
        accountManagementInfo.setOpenDate(accMaDto.getOpenDate());
        accountManagementInfo.setCycleDay(accMaDto.getCycleDay());
        /** 账单剩余未还金额 */
        accountManagementInfo.setStatementDueAmount(BigDecimal.ZERO);
        /** 一次约定扣款日期 */
        accountManagementInfo.setAutoPaymentFirstDate(Constants.INITIAL_LOCALDATE);
        /** 二次约定扣款日期 */
        accountManagementInfo.setAutoPaymentSecondDate(Constants.INITIAL_LOCALDATE);
        /** 自动购汇还款类型 */
        accountManagementInfo.setAutoExchangePaymentType(null);
        if (accountManagementInfo.getFinanceStatusInd() == null) {
            accountManagementInfo.setFinanceStatusInd("Y");
        }
        /** 新增ABS类型 首次封包资产包编号  当前资产包编号 全账户abs截至日期 核算状态和cycledue联动状态 核算状态和cycledue联动状态更新日期
         账户管理信息表新增6个字段，插入新记录时，给他们赋一下值：
         ABS_TYPE='0' （未abs）
         ABS_PRODUCT_CODE_FIRST=空
         ABS_PRODUCT_CODE_CURR=空
         ABS_END_DATE=空
         FINANCE_STATUS_IND='Y' （联动）**/
        accountManagementInfo.setAbsType("0");
        accountManagementInfo.setFinanceStatusInd("Y");
        accountManagementInfo.setFinanceStatusIndDate(accountManagementInfo.getOpenDate());

        //设置分区键值
        int partition = AccountingPartitionKeyHelper.getPartitionKeyInt(accMaDto.getAccountManagementId());

        accountManagementInfo.setPartitionKey(partition);
        try {
            int result = accountManagementInfoMapper.insertSelective(accountManagementInfo);
            if (result == 0) {
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_INSERT_FAIL);
            }
        } catch (AnyTxnAccountingException e) {
            logger.warn("modifyAccountManagementInfo : {}", e.getErrMsg());
            throw e;
        } catch (PersistenceException e) {
            logger.error("modifyAccountManagementInfo : ", e);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_MYBATIS_FAIL);

        } catch (Exception e) {
            logger.error(
                    "Failed to call [{}] insert database table [{}]",
                    "addAccManInfo",
                    "ACCOUNT_MANAGEMENT_INFO",
                    e);

            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR, AccountingRepDetailEnum.I_AM, accMaDto.getAccountManagementId());
        }
    }

    @Override
    public void registAccountManagementInfo(AccountManagementInfoDTO accountManagementInfoDTO) {

        try {
            AccountManagementInfo accountManagementInfo = new AccountManagementInfo();
            if (null != accountManagementInfoDTO) {
                if (accountManagementInfoDTO.getFinanceStatusInd() == null) {
                    accountManagementInfoDTO.setFinanceStatusInd("Y");
                }
                BeanCopier copierAccountManagementDtoToModel =
                        BeanCopier.create(AccountManagementInfoDTO.class, AccountManagementInfo.class, false);
                copierAccountManagementDtoToModel.copy(
                        accountManagementInfoDTO, accountManagementInfo, null);
            }
            int result = accountManagementInfoMapper.insertSelective(accountManagementInfo);
            if (result == 0) {
                /*throw new AnyTXNBusRuntimeException(StatusCode.INSERT_INVALID.getCode(),
                        StatusCode.INSERT_INVALID.getMsg());*/
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_INSERT_FAIL);
            }
        } catch (AnyTxnAccountingException e) {
            logger.warn("modifyAccountManagementInfo : {}", e.getErrMsg());
            throw e;
        } catch (PersistenceException e) {
            logger.error("modifyAccountManagementInfo : ", e);
            /*throw new AnyTXNBusRuntimeException(
                    StatusCode.MYBATIS_FAIL.getCode(), StatusCode.MYBATIS_FAIL.getMsg());*/
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_MYBATIS_FAIL);
        } catch (Exception e) {
            logger.error("AccountManageInfoServiceImpl registAccountManagementInfo error", e);
            /*throw new AnyTXNBusRuntimeException(
                    TransactionEnum.DATABASE_ERROR.getCode(), TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR);
        }
    }

    @Override
    public AccountManagementInfoDTO getAccountManagementInfo(
            String orgNumber, String accountManagerId, String currency) {
        AccountManagementInfo accountManagementInfo =
                accountManagementInfoSelfMapper.getAccountManagementInfo(orgNumber, accountManagerId, currency);
        AccountManagementInfoDTO accountManagementInfoDTO = null;
        if (accountManagementInfo != null) {
            accountManagementInfoDTO = new AccountManagementInfoDTO();
            BeanCopier copierAccountManagementDtoToModel = BeanCopier.create(AccountManagementInfo.class,
                    AccountManagementInfoDTO.class, false);
            copierAccountManagementDtoToModel.copy(accountManagementInfo, accountManagementInfoDTO, null);
        }
        return accountManagementInfoDTO;
    }

    @Override
    public int getAccountManagementIdCount(
            String partitionKey, List<Map<String, Object>> organizations) {
        Integer partitionKey0 = null;
        Integer partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = (null == partitionKey.split("-")[0] ? null : Integer.valueOf(partitionKey.split("-")[0]));
            partitionKey1 = (null == partitionKey.split("-")[1] ? null : Integer.valueOf(partitionKey.split("-")[1]));
        }
        int i = accountManagementInfoSelfMapper.getAccountManagementIdCount(
                partitionKey0, partitionKey1, organizations);
        return i;
    }

    @Override
    public List<String> getAccountManagementIds(
            String partitionKey, List<Map<String, Object>> organizations, List<Integer> rowNumbers) {
        Integer partitionKey0 = null;
        Integer partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = (null == partitionKey.split("-")[0] ? null : Integer.valueOf(partitionKey.split("-")[0]));
            partitionKey1 = (null == partitionKey.split("-")[1] ? null : Integer.valueOf(partitionKey.split("-")[1]));
        }
        return accountManagementInfoSelfMapper.getAccountManagementIds(
                partitionKey0, partitionKey1, organizations, rowNumbers);
    }

    /**
     * 封锁码更新公共处理（勿删）
     * @param paramProductInfo      产品参数
     * @param cycleDue              延滞等级
     * @param accountManagementInfo 管理账户
     * @param organization          机构
     */
    public void updateBlockCode(ProductInfoResDTO paramProductInfo,
                                short cycleDue,
                                AccountManagementInfoDTO accountManagementInfo,
                                OrganizationInfoResDTO organization) {
        if (cycleDue < Constants.CYCLE_DUE_AMOUNT_FLAG) {
            getAccountManagementInfoWithNotCycleDue(paramProductInfo, accountManagementInfo, organization);
        } else {
            updateBlockCodeWithCycleDue( paramProductInfo, cycleDue, accountManagementInfo, organization);
        }
    }

    /**
     * 校验参数
     *
     * @param parameterConfig 参数查询结果
     */
    public static void checkParameterConfig(Object parameterConfig) {
        if (null == parameterConfig) {
            LoggerFactory.getLogger(AccountManageInfoServiceImpl.class).error("Parameter configuration is null");
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL);
        }
    }

    /**
     * 非延滞户的封锁码处理
     *
     * @param parameterConfig       产品参数
     * @param accountManagementInfo 管理账户信息
     * @param organization          机构
     */
    private void getAccountManagementInfoWithNotCycleDue(ProductInfoResDTO parameterConfig,
                                                         AccountManagementInfoDTO accountManagementInfo,
                                                         OrganizationInfoResDTO organization) {
        if (!StringUtils.isBlank(accountManagementInfo.getBlockCode())) {
            String oldBlockType = getPriority(parameterConfig, organization.getOrganizationNumber(), accountManagementInfo.getBlockCode()).getType();
            if (Constants.BLOCK_CODE_TYPE_CYCLE.equals(oldBlockType)) {
                updateWithOldBlockCodeTypeIsCycle(parameterConfig, accountManagementInfo, organization.getOrganizationNumber(), organization);
            }
        }
    }

    /**
     * 原封锁码类型为延滞类型处理
     *
     * @param parameterConfig    parameterConfig
     * @param managementAccount  managementAccount
     * @param organizationNumber organizationNumber
     */
    private void updateWithOldBlockCodeTypeIsCycle(
            ProductInfoResDTO parameterConfig,
            AccountManagementInfoDTO managementAccount,
            String organizationNumber,
            OrganizationInfoResDTO organization) {
        managementAccount.setBlockCode(null);
        managementAccount.setBlockCodeSetDate(organization.getToday());
        BlockCodeAccountResDTO lastPriorityConfig = getPriority(parameterConfig, organizationNumber, managementAccount.getPreviousBlockCode());
        String lastBlockCodeType = lastPriorityConfig.getType();
        if (!Constants.BLOCK_CODE_TYPE_CYCLE.equals(lastBlockCodeType)) {
            updateBlockCodeAndPreviousBlockCode(
                    managementAccount.getBlockCode(),
                    managementAccount.getPreviousBlockCode(),
                    parameterConfig,
                    managementAccount,
                    lastBlockCodeType,
                    organization);
        }
    }

    /**
     * 更新封锁码
     *
     * @param oldBlockCode      原封锁码
     * @param newBlockCode      新封锁码
     * @param parameterConfig   参数
     * @param managementAccount 管理账户信息
     * @param lastBlockCodeType lastBlockCodeType
     */
    private void updateBlockCodeAndPreviousBlockCode(
            String oldBlockCode,
            String newBlockCode,
            ProductInfoResDTO parameterConfig,
            AccountManagementInfoDTO managementAccount,
            String lastBlockCodeType,
            OrganizationInfoResDTO organization) {
        if (isUpdateBlockCode(oldBlockCode, newBlockCode, parameterConfig, managementAccount)) {
            if (Constants.BLOCK_CODE_TYPE_OVER.equals(lastBlockCodeType)) {
                managementAccount.setBlockCode(newBlockCode);
                managementAccount.setBlockCodeSetDate(organization.getToday());
            } else {
                managementAccount.setPreviousBlockCode(oldBlockCode);
                managementAccount.setPreviousBlockCodeSetDate(managementAccount.getBlockCodeSetDate());
                managementAccount.setBlockCode(newBlockCode);
                managementAccount.setBlockCodeSetDate(organization.getToday());
            }
        }
    }

    /**
     * 是否需要更新封锁码
     *
     * @param oldBlockCode          oldBlockCode
     * @param newBlockCode          newBlockCode
     * @param accountManagementInfo accountManagementInfo
     * @param parameterConfig       产品参数
     * @return boolean
     */
    public boolean isUpdateBlockCode(
            String oldBlockCode,
            String newBlockCode,
            ProductInfoResDTO parameterConfig,
            AccountManagementInfoDTO accountManagementInfo) {
        if (null != newBlockCode && newBlockCode.equals(oldBlockCode)) {
            return false;
        }
        Integer oldPriority =
                getPriority(parameterConfig, accountManagementInfo.getOrganizationNumber(), oldBlockCode)
                        .getPriority();
        Integer newPriority =
                getPriority(parameterConfig, accountManagementInfo.getOrganizationNumber(), newBlockCode)
                        .getPriority();
        return newPriority > oldPriority;
    }

    /**
     * 获取封锁码的优先级
     *
     * @param parameterConfig    参数配置
     * @param organizationNumber 机构号
     * @param blockCode          封锁码
     * @return String
     */
    private BlockCodeAccountResDTO getPriority(ProductInfoResDTO parameterConfig, String organizationNumber, String blockCode) {
        if (StringUtils.isNotBlank(blockCode)) {
            return blockCodeAccountService.findBlockCodeAccount(
                    organizationNumber,
                    parameterConfig.getAccountBlockCodeTableId(),
                    blockCode);
        } else {
            return blockCodeAccountService.findBlockCodeAccount(
                    organizationNumber,
                    parameterConfig.getAccountBlockCodeTableId(),
                    null);
        }

    }

    /**
     * 延滞户的封锁码处理
     *
     * @param parameterConfig       接口参数
     * @param cycleDue              延滞等级
     * @param accountManagementInfo 账户管理信息
     */
    private void updateBlockCodeWithCycleDue(
            ProductInfoResDTO parameterConfig,
            short cycleDue,
            AccountManagementInfoDTO accountManagementInfo,
            OrganizationInfoResDTO organization) {
        String orgCode = accountManagementInfo.getOrganizationNumber();
        String cycleDueString = Short.toString(cycleDue);
        DelinquentControlResDTO paramDelinquentControl = new DelinquentControlResDTO();
        if (StringUtils.isNotBlank(cycleDueString)) {
            paramDelinquentControl = delinquentControlService.findDelinquentControl(orgCode,
                    parameterConfig.getDelinquentControlTableId(),
                    Integer.valueOf(cycleDueString)).get(0);
        }

        checkParameterConfig(parameterConfig);
        String newBlockCode = paramDelinquentControl.getBlockCode();
        String oldBlockCode = accountManagementInfo.getBlockCode();
        String oldBlockType = getPriority(parameterConfig, orgCode, oldBlockCode).getType();
        if (Constants.BLOCK_CODE_TYPE_CYCLE.equals(oldBlockType)) {
            if (!newBlockCode.equals(oldBlockCode)) {
                accountManagementInfo.setBlockCode(newBlockCode);
                accountManagementInfo.setBlockCodeSetDate(organization.getToday());
            }
        } else {
            updateBlockCodeAndPreviousBlockCode(
                    oldBlockCode, newBlockCode,
                    parameterConfig, accountManagementInfo,
                    oldBlockType, organization);
        }
    }

    /**
     * 如果传入的数据为空，返回Zero。反之，返回其自身
     *
     * @param amount 金额
     * @return BigDecimal
     */
    private BigDecimal getBigDecimal(BigDecimal amount) {
        return amount == null ? BigDecimal.ZERO : amount;
    }

    @Override
    public int getAccountManagementIdCount4Delin(
            String partitionKey, List<Map<String, Object>> organizations) {
        Integer partitionKey0 = null;
        Integer partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = (null == partitionKey.split("-")[0] ? null : Integer.valueOf(partitionKey.split("-")[0]));
            partitionKey1 = (null == partitionKey.split("-")[1] ? null : Integer.valueOf(partitionKey.split("-")[1]));
        }
        return accountManagementInfoSelfMapper.getAccountManagementIdCount4Delin(
                partitionKey0, partitionKey1, organizations);
    }

    @Override
    public List<String> queryAccountManagementIds4Delin(
            String partitionKey, List<Map<String, Object>> organizations, List<Integer> rowNumbers) {
        Integer partitionKey0 = null;
        Integer partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = (null == partitionKey.split("-")[0] ? null : Integer.valueOf(partitionKey.split("-")[0]));
            partitionKey1 = (null == partitionKey.split("-")[1] ? null : Integer.valueOf(partitionKey.split("-")[1]));
        }
        return accountManagementInfoSelfMapper.queryAccountManagementIds4Delin(
                partitionKey0, partitionKey1, organizations, rowNumbers);
    }

    @Override
    public AutoPaymentBO batchProcess4AutoPayment(AutoEbitSignUpInforDTO autoEbitSignUpInfor) {
        AutoPaymentBO autoPaymentBO = new AutoPaymentBO();
        List<AutoPaymentLog> autoPaymentLogs = new ArrayList<>();
        List<AccountManagementInfo> accountManagementInfos = accountManagementInfoMapper.selectByCustomerIdAndOrganizationNumber(autoEbitSignUpInfor.getCustomerId(), OrgNumberUtils.getOrg());
        for (AccountManagementInfo accountManagementInfo : accountManagementInfos) {
            if (!"9".equals(accountManagementInfo.getAccountStatus()) && !"8".equals(accountManagementInfo.getAccountStatus())) {
                if (accountManagementInfo.getTotalDueAmount().compareTo(BigDecimal.ZERO) > 0) {
                    AutoPaymentLog autoPaymentLog = getAutoPaymentLog(accountManagementInfo, autoEbitSignUpInfor);
                    if (!ObjectUtils.isEmpty(autoPaymentLog)){
                        autoPaymentLogs.add(autoPaymentLog);
                    }
                }
            }
        }
        autoPaymentBO.setAutoPayments(autoPaymentLogs);
        return autoPaymentBO;
        /*AutoPaymentLog autoPaymentLog = null;
        //签约 & 约定扣款
        if ("0".equals(autoEbitSignUpInfor.getContractType()) && "0".equals(autoEbitSignUpInfor.getAutoDebitType())) {
            List<AccountManagementInfo> accountManagementInfos = accountManagementInfoMapper.selectByCustomerIdAndOrganizationNumber(autoEbitSignUpInfor.getCustomerId(), OrgNumberUtils.getOrg());
            for (AccountManagementInfo accountManagementInfo : accountManagementInfos) {
                if (!"9".equals(accountManagementInfo.getAccountStatus()) && !"8".equals(accountManagementInfo.getAccountStatus())) {
                    if (accountManagementInfo.getTotalDueAmount().compareTo(BigDecimal.ZERO) > 0) {
                        autoPaymentLog = getAutoPaymentLog(accountManagementInfo,autoEbitSignUpInfor);
                    }
                }
            }
        }else if("0".equals(autoEbitSignUpInfor.getContractType()) && "1".equals(autoEbitSignUpInfor.getAutoDebitType())){
            List<AccountManagementInfo> accountManagementInfos = accountManagementInfoMapper.selectByCustomerIdAndOrganizationNumber(autoEbitSignUpInfor.getCustomerId(), OrgNumberUtils.getOrg());
            for (AccountManagementInfo accountManagementInfo : accountManagementInfos) {
                OrganizationInfoResDTO orgConfig = organizationInfoService.findOrganizationInfo(accountManagementInfo.getOrganizationNumber());
                if (!"9".equals(accountManagementInfo.getAccountStatus()) && !"8".equals(accountManagementInfo.getAccountStatus())) {
                    if (accountManagementInfo.getTotalDueAmount().compareTo(BigDecimal.ZERO) > 0) {
                        AccountManagementInfoDTO accountManagementInfoDTO = BeanMapping.copy(accountManagementInfo, AccountManagementInfoDTO.class);
//                        checkTempPaymentType(accountManagementInfoDTO,orgConfig);
                        autoPaymentLog = getAutoPaymentLog(accountManagementInfo,autoEbitSignUpInfor);
                    }
                }
            }
        }*/
    }

    /**
     * 自扣process处理
     *
     * @param accountManagementInfo AccountManagementInfo
     * @return AutoPaymentLog
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public GiroAutoPayBO batchProcessAutoPay(AccountManagementInfo accountManagementInfo) {
        if (null != accountManagementInfo) {
            GiroAutoPayBO giroAutoPayBO = new GiroAutoPayBO();
            // 获取机构参数信息
            logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", accountManagementInfo.getOrganizationNumber());
            OrganizationInfoResDTO paramOrganizationInfo = organizationInfoService.findOrganizationInfo(accountManagementInfo.getOrganizationNumber());
            logger.info("Called organizationInfoService.findOrganizationInfo successfully");
            if (paramOrganizationInfo == null) {
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.GMP);
            }
            // 获取机构参数表中的当前累计日期
            LocalDate accruedThruDay = paramOrganizationInfo.getAccruedThruDay();
            // 获取机构参数表中的上次累计日期
            LocalDate lastAccruedThruDay = paramOrganizationInfo.getLastAccruedThruDay();
            if (logger.isDebugEnabled()) {
                logger.debug("Account management info auto payment first date: {}", accountManagementInfo.getAutoPaymentFirstDate());
            }
            if (accountManagementInfo.getAutoPaymentFirstDate() != null) {
                BigDecimal autoPayAmount;
                //约定自扣  一次约定扣款日期   在当前累计日期、上次累计日期之间
                boolean flag = (lastAccruedThruDay.compareTo(accountManagementInfo.getAutoPaymentFirstDate()) < 0
                        && accountManagementInfo.getAutoPaymentFirstDate().compareTo(accruedThruDay) <= 0);
                if (flag){
                    // 计算约定扣款金额
                    autoPayAmount = calculateAgreePayAmount(accountManagementInfo, accountManagementInfo.getAutoPaymentType());
                    // 约定扣款金额大于0时，写入约定扣款记录流水
//                if (autoPayAmount.compareTo(BigDecimal.ZERO) > 0) {
                    giroAutoPayBO.setAutoPaymentLog(buildAutoPaymentLog(accountManagementInfo, autoPayAmount, paramOrganizationInfo));
//                }
                }
                //
                LocalDate today = paramOrganizationInfo.getToday();
                /*Integer year = today.getYear();
                Integer month = today.getMonthValue();
                List<ParmProductInfo> productInfoList = parmProductInfoSelfMapper.isExists(OrgNumberUtils.getOrg(), accountManagementInfo.getProductNumber(), accountManagementInfo.getCurrency());
                ParmAutoPaymentTable parmAutoPaymentTable = parmAutoPaymentTableSelfMapper.queryByAutoPaymentTableIdAndOrgNo(productInfoList.get(0).getAutoPaymentTableId(),OrgNumberUtils.getOrg());
                ParmStatementProcess parmStatementProcess = parmStatementProcessSelfMapper.isExists(OrgNumberUtils.getOrg(),productInfoList.get(0).getStatementProcessingTableId());
                Integer day = Integer.valueOf(accountManagementInfo.getCycleDay()) + parmStatementProcess.getDueDays() - Integer.parseInt(String.valueOf(parmAutoPaymentTable.getAutoPaymentFirstDays()));
*/

                List<LocalDate> dates = parmAutoPaymentTableSelfMapper.queryPaymentDateListByYear(today.getYear(),paramOrganizationInfo.getOrganizationNumber());
                boolean flag3 = dates.contains(today);

                //LocalDate formatDate = LocalDate.of(year, month, day);
                boolean flag2 = (lastAccruedThruDay.compareTo(today) < 0
                        && today.compareTo(accruedThruDay) <= 0);
                if (flag3 && flag2){
                    giroAutoPayBO.setAccountManagementInfo(accountManagementInfo);
                }
            }
            return giroAutoPayBO;
            //更新自扣类型
           /* if("1".equals(accountManagementInfo.getAutoPaymentType())){
                accountManagementInfoSelfMapper.updateAutoPayType("3",accountManagementInfo.getAccountManagementId());
            }else if("2".equals(accountManagementInfo.getAutoPaymentType())){
                accountManagementInfoSelfMapper.updateAutoPayType("4",accountManagementInfo.getAccountManagementId());
            }*/
        }
        return null;
    }

    private AutoPaymentLog getAutoPaymentLog(AccountManagementInfo accountManagementInfo,AutoEbitSignUpInforDTO autoEbitSignUpInfor) {
        if (null != accountManagementInfo) {
            // 获取机构参数信息
            OrganizationInfoResDTO paramOrganizationInfo = organizationInfoService.findOrganizationInfo(accountManagementInfo.getOrganizationNumber());
            if (paramOrganizationInfo == null) {
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.GMP);
            }
            // 获取机构参数表中的当前累计日期
            LocalDate accruedThruDay = paramOrganizationInfo.getAccruedThruDay();
            // 获取机构参数表中的上次累计日期
            LocalDate lastAccruedThruDay = paramOrganizationInfo.getLastAccruedThruDay();

            if (logger.isDebugEnabled()) {
                logger.debug("Account management info auto payment first date: {}", accountManagementInfo.getAutoPaymentFirstDate());
            }

            if (accountManagementInfo.getAutoPaymentFirstDate() != null) {
                String payType = null;
                BigDecimal autoPayAmount = BigDecimal.ZERO;

                //约定自扣  一次约定扣款日期 或者 二次约定扣款日期  在当前累计日期、上次累计日期之间
                if ("0".equals(autoEbitSignUpInfor.getAutoDebitType())){
                    boolean flag = ((lastAccruedThruDay.compareTo(accountManagementInfo.getAutoPaymentFirstDate()) < 0
                            && accountManagementInfo.getAutoPaymentFirstDate().compareTo(accruedThruDay) <= 0)
                            || (lastAccruedThruDay.compareTo(accountManagementInfo.getAutoPaymentSecondDate()) < 0
                            && accountManagementInfo.getAutoPaymentSecondDate().compareTo(accruedThruDay) <= 0));
                    if (flag){
                        payType = "1";
                        // 计算约定扣款金额
                        autoPayAmount = calculateAgreePayAmount(accountManagementInfo, autoEbitSignUpInfor.getAutoPaymentType());
                    }
                }else if ("1".equals(autoEbitSignUpInfor.getAutoDebitType())){
                    boolean flag = lastAccruedThruDay.compareTo(autoEbitSignUpInfor.getTemporaryAutoDebitOperationDate()) < 0
                            && autoEbitSignUpInfor.getTemporaryAutoDebitOperationDate().compareTo(accruedThruDay) <= 0;
                    if (flag){
                        payType = "0";
                        // 计算约定扣款金额
                        autoPayAmount = calculateAgreePayAmount(accountManagementInfo, autoEbitSignUpInfor.getAutoPaymentType());
                    }
                }

                // 约定扣款金额大于0时，写入约定扣款记录流水
                if (autoPayAmount.compareTo(BigDecimal.ZERO) > 0 && payType != null) {
                    return savePaymentLog(accountManagementInfo, autoPayAmount, payType, paramOrganizationInfo,autoEbitSignUpInfor);
                }
            }
        }
        return null;
    }

    /**
     * @Description 构建自扣流水生成处理
     * @Method savePaymentLog
     * @Params [accountManagementInfo, autoPayAmount]
     * @Return com.anytech.anytxn.core.accounting.model.AutoPaymentLog
     * @Date 2020/6/17
     **/
    private AutoPaymentLog savePaymentLog(AccountManagementInfo accountManagementInfo, BigDecimal autoPayAmount,String payType, OrganizationInfoResDTO paramOrganizationInfo,AutoEbitSignUpInforDTO autoEbitSignUpInfor ) {
        AutoPaymentLog autoPaymentLog =new AutoPaymentLog();
        autoPaymentLog.setAutoPaymentId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        autoPaymentLog.setAccountManagementId(accountManagementInfo.getAccountManagementId());
        autoPaymentLog.setOrganizationNumber(accountManagementInfo.getOrganizationNumber());
        if (null != autoEbitSignUpInfor) {
//            if ("1".equals(payType)) {
                if("0".equals(autoEbitSignUpInfor.getBankflag())){
                    autoPaymentLog.setAutoPaymentBranchNumber("0023");
                    autoPaymentLog.setAutoPaymentDebitAcctNumber(autoEbitSignUpInfor.getAutoDebitAffiliatedAccount());
                    autoPaymentLog.setAutoPaymentDebitBankNumber(autoEbitSignUpInfor.getAutoDebitRelatedAccountBankNumber());
                }else if("1".equals(autoEbitSignUpInfor.getBankflag())){
                    // 新增万外行信息
                    autoPaymentLog.setAutoPaymentOtherAcctNumber(autoEbitSignUpInfor.getAutoDebitAffiliatedAccount());
                    autoPaymentLog.setAutoPaymentOtherBankNumber(autoEbitSignUpInfor.getAutoDebitRelatedAccountBankNumber());
                    autoPaymentLog.setAutoPaymentOtherBranch(autoEbitSignUpInfor.getAutoDebitRelatedAccountBranchNumber());
                }


           /* }else{
                autoPaymentLog.setAutoPaymentBranchNumber(null);
                autoPaymentLog.setAutoPaymentDebitAcctNumber(
                        accountManagementInfo.getTempPaymentDebitAcctNumber());
                autoPaymentLog.setAutoPaymentDebitBankNumber(
                        accountManagementInfo.getTempPaymentDebitBankNumber());
                // 新增万外行信息
                autoPaymentLog.setAutoPaymentOtherAcctNumber(
                        accountManagementInfo.getTempPaymentOtherAcctNumber());
                autoPaymentLog.setAutoPaymentOtherBankNumber(
                        accountManagementInfo.getTempPaymentOtherBankNumber());
                autoPaymentLog.setAutoPaymentOtherBranch(null);
            }*/
        }
        autoPaymentLog.setOriginalCurrency(accountManagementInfo.getCurrency());
        autoPaymentLog.setOriginalPaymentAmount(autoPayAmount);
        if (Constants.RMB.equals(accountManagementInfo.getCurrency())) {
            autoPaymentLog.setExchangeRate(BigDecimal.ONE);
        } else {
            // TODO 外币情况下计算扣款汇率，现获取汇率参数信息
            CurrencyRateResDTO paramCurrencyRate = currencyRateService.findByOrgAndCurrencyAndRateType(
                    accountManagementInfo.getOrganizationNumber(),
                    accountManagementInfo.getCurrency(),
                    Constants.RMB,
                    "0");
            if (null != paramCurrencyRate) {
                // 汇率
                BigDecimal rateValue = new BigDecimal(paramCurrencyRate.getRateValue());
                // 汇率小数位
                int exponent = paramCurrencyRate.getExponent();
                // 扣款汇率 = 汇率 / （10 ** 汇率小数位）,10的N次方
                BigDecimal rate = rateValue.divide(BigDecimal.valueOf(Math.pow(10, exponent)));
                autoPaymentLog.setExchangeRate(rate);
            }
        }
        // 扣款类型是9，则购汇标志为1，否则为0
       /* if("9".equals(accountManagementInfo.getAutoPaymentType())||"9".equals(accountManagementInfo.getTempPaymentType())){
            autoPaymentLog.setExchangePurchasingIndicator(Constants.PfExchangeFlag.PF_EXCHANGE_FLAG_Y);
        }else{
            autoPaymentLog.setExchangePurchasingIndicator(Constants.PfExchangeFlag.PF_EXCHANGE_FLAG_N);
        }*/
        autoPaymentLog.setExchangePurchasingIndicator(autoEbitSignUpInfor.getAutoDebitExchangeSign());
        // 如果购汇标志是1并且原始扣款币种（original_currency）not = 156（人民币），最终扣款金额 = 上述计算后的原始扣款金额 * 扣款汇率
        if (Constants.PfExchangeFlag.PF_EXCHANGE_FLAG_Y.equals(
                autoPaymentLog.getExchangePurchasingIndicator())
                && !Constants.RMB.equals(accountManagementInfo.getCurrency())) {
            autoPaymentLog.setFinalPaymentAmount(
                    autoPaymentLog.getOriginalPaymentAmount().multiply(autoPaymentLog.getExchangeRate()));
            autoPaymentLog.setFinalPaymentAmountCurrency(Constants.RMB);
        } else {
            autoPaymentLog.setFinalPaymentAmount(autoPaymentLog.getOriginalPaymentAmount());
            autoPaymentLog.setFinalPaymentAmountCurrency(autoPaymentLog.getOriginalCurrency());
        }
        autoPaymentLog.setTrnDate(paramOrganizationInfo.getToday());
        CustomerAuthorizationInfo customerAuthorizationInfo = customerAuthorizationInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(),accountManagementInfo.getCustomerId());
        autoPaymentLog.setMoPhone(customerAuthorizationInfo.getMobilePhone());
        autoPaymentLog.setIdType(customerAuthorizationInfo.getIdType());
        autoPaymentLog.setIdNumber(customerAuthorizationInfo.getIdNumber());
        autoPaymentLog.setCustomerName(customerAuthorizationInfo.getChineseName());
        autoPaymentLog.setCreateTime(LocalDateTime.now());
        autoPaymentLog.setUpdateTime(LocalDateTime.now());
        autoPaymentLog.setVersionNumber(0L);
        autoPaymentLog.setUpdateBy(accountManagementInfo.getUpdateBy());
        autoPaymentLog.setPartitionKey(PartitionKeyUtils.StrPartitionKey(accountManagementInfo.getCustomerId()));
        autoPaymentLog.setCustomerId(accountManagementInfo.getCustomerId());
        autoPaymentLog.setOutFile("0");

        // 输入活跃的主卡
        List<CardAuthorizationInfo> cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByPrimaryCustomerId(accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId());
        cardAuthorizationInfos.stream().filter(x -> "1".equals(x.getStatus())).findFirst().ifPresent(x-> {
            autoPaymentLog.setCardNbr(x.getCardNumber());
        });

        logger.info("Auto payment debit bank number: {}", autoPaymentLog.getAutoPaymentDebitBankNumber());
        return autoPaymentLog;
    }

    /**
     * 构建约定扣款流水
     *
     * @param accountManagementInfo AccountManagementInfo
     * @param autoPayAmount BigDecimal
     * @param paramOrganizationInfo OrganizationInfoResDTO
     * @return AutoPaymentLog
     */
    private AutoPaymentLog buildAutoPaymentLog(AccountManagementInfo accountManagementInfo,
                                               BigDecimal autoPayAmount,
                                               OrganizationInfoResDTO paramOrganizationInfo ) {
        AutoPaymentLog autoPaymentLog =new AutoPaymentLog();
        autoPaymentLog.setAutoPaymentId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        autoPaymentLog.setAccountManagementId(accountManagementInfo.getAccountManagementId());
        autoPaymentLog.setOrganizationNumber(accountManagementInfo.getOrganizationNumber());
        autoPaymentLog.setOriginalCurrency(accountManagementInfo.getCurrency());
        autoPaymentLog.setOriginalPaymentAmount(autoPayAmount);
        if (Constants.SGD.equals(accountManagementInfo.getCurrency())) {
            autoPaymentLog.setExchangeRate(BigDecimal.ONE);
        } else {
            CurrencyRateResDTO paramCurrencyRate = currencyRateService.findByOrgAndCurrencyAndRateType(
                    accountManagementInfo.getOrganizationNumber(),
                    accountManagementInfo.getCurrency(),
                    Constants.SGD,
                    "0");
            if (null != paramCurrencyRate) {
                // 汇率
                BigDecimal rateValue = new BigDecimal(paramCurrencyRate.getRateValue());
                // 汇率小数位
                int exponent = paramCurrencyRate.getExponent();
                // 扣款汇率 = 汇率 / （10 ** 汇率小数位）,10的N次方
                BigDecimal rate = rateValue.divide(BigDecimal.valueOf(Math.pow(10, exponent)));
                autoPaymentLog.setExchangeRate(rate);
            }
        }
        autoPaymentLog.setExchangePurchasingIndicator(accountManagementInfo.getAutoExchangeIndicator());
        // 如果购汇标志是1并且原始扣款币种（original_currency）not = 702（新元），最终扣款金额 = 上述计算后的原始扣款金额 * 扣款汇率
        if (Constants.PfExchangeFlag.PF_EXCHANGE_FLAG_Y.equals(
                autoPaymentLog.getExchangePurchasingIndicator())
                && !Constants.SGD.equals(accountManagementInfo.getCurrency())) {
            autoPaymentLog.setFinalPaymentAmount(
                    autoPaymentLog.getOriginalPaymentAmount().multiply(autoPaymentLog.getExchangeRate()));
            autoPaymentLog.setFinalPaymentAmountCurrency(Constants.SGD);
        } else {
            autoPaymentLog.setFinalPaymentAmount(autoPaymentLog.getOriginalPaymentAmount());
            autoPaymentLog.setFinalPaymentAmountCurrency(autoPaymentLog.getOriginalCurrency());
        }
        autoPaymentLog.setTrnDate(paramOrganizationInfo.getToday());
        CustomerAuthorizationInfo customerAuthorizationInfo = customerAuthorizationInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(),accountManagementInfo.getCustomerId());

        autoPaymentLog.setMoPhone(ObjectUtils.isEmpty(customerAuthorizationInfo)?"":customerAuthorizationInfo.getMobilePhone());
        autoPaymentLog.setIdType(ObjectUtils.isEmpty(customerAuthorizationInfo)?"":customerAuthorizationInfo.getIdType());
        autoPaymentLog.setIdNumber(ObjectUtils.isEmpty(customerAuthorizationInfo)?"":customerAuthorizationInfo.getIdNumber());
        autoPaymentLog.setCustomerName(ObjectUtils.isEmpty(customerAuthorizationInfo)?"":customerAuthorizationInfo.getChineseName());
        autoPaymentLog.setCreateTime(LocalDateTime.now());
        autoPaymentLog.setUpdateTime(LocalDateTime.now());
        autoPaymentLog.setVersionNumber(0L);
        autoPaymentLog.setUpdateBy(accountManagementInfo.getUpdateBy());
        autoPaymentLog.setPartitionKey(PartitionKeyUtils.StrPartitionKey(accountManagementInfo.getCustomerId()));
        autoPaymentLog.setCustomerId(accountManagementInfo.getCustomerId());
        autoPaymentLog.setOutFile("0");
        // 输入活跃的主卡
        String accountProductNumber = accountManagementInfo.getProductNumber();
        List<ParmCardProductInfo> parmCardProductInfos = cardProductInfoSelfMapper.selectByOrgAndAccountProductNum(accountManagementInfo.getOrganizationNumber(), accountProductNumber);
        if (parmCardProductInfos == null || parmCardProductInfos.isEmpty()){
            logger.error("Card product parameter not found for organization: orgNumber={}, accountProductNumber={}", 
                    accountManagementInfo.getOrganizationNumber(), accountProductNumber);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST);
        }
        ParmCardProductInfo parmCardProductInfo = parmCardProductInfos.get(0);
        String productNumber = parmCardProductInfo.getProductNumber();

        CardAuthorizationInfo cardAuthorizationInfo = null;
        if("C".equals(accountManagementInfo.getLiability())){
             cardAuthorizationInfo = cardAuthorizationInfoSelfMapper.selectCardNumByCorpForGiro( accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId(),
                    "P", productNumber);
        }else{
             cardAuthorizationInfo = cardAuthorizationInfoSelfMapper.selectCardNumForGiro(
                    accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId(),
                    "P", productNumber);
        }

        if (cardAuthorizationInfo == null){
            logger.error("Card authorization info not found: orgNumber={}, customerId={}, relationshipIndicator={}, productNumber={}",
                accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId(), "P", productNumber);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST);
        }
        String cardNumber = cardAuthorizationInfo.getCardNumber();
        autoPaymentLog.setCardNbr(cardNumber);
//        List<CardAuthorizationInfo> cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByPrimaryCustomerId(accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId());
//        cardAuthorizationInfos.stream().filter(x -> "1".equals(x.getStatus())).findFirst().ifPresent(x-> {
//            autoPaymentLog.setCardNbr(x.getCardNumber());
//        });
        autoPaymentLog.setAutoPaymentDebitAcctNumber(accountManagementInfo.getAutoPaymentAcctNumber());
        autoPaymentLog.setAutoPaymentDebitBankNumber(accountManagementInfo.getAutoPaymentBankNumber());
        logger.info("Auto payment debit bank number: {}", autoPaymentLog.getAutoPaymentDebitBankNumber());
        return autoPaymentLog;
    }

    /**
     * 计算约定扣款金额
     *
     * @param managementAccount 账户管理对象
     * @return BigDecimal
     */
    private BigDecimal calculateAgreePayAmount(AccountManagementInfo managementAccount,String paymentMethod) {
        BigDecimal autoPayAmount = BigDecimal.ZERO;
        // auto_payment_method 约定扣款方式=1(按最小还款额进行约定扣款), 2:按全额进行约定扣款
        if (AutoPaymentMethod.ALL_TWO.getCode().equals(paymentMethod) || AutoPaymentMethod.ALL_FOUR.getCode().equals(paymentMethod)) {
            if(managementAccount.getLastStatementDate()==null){
                return autoPayAmount;
            }
            if(LocalDate.parse("0001-01-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")).compareTo(managementAccount.getLastStatementDate()) == 0){
                return BigDecimal.ZERO;
            }
            AccountStatementInfo info =
                    accountStatementInfoSelfMapper.selectByIdAndDate(
                            managementAccount.getAccountManagementId(), managementAccount.getLastStatementDate());
            if (null != info) {
                autoPayAmount =info.getCloseBalance()
                                .subtract(managementAccount.getTotalGracePaymentAmount())
                                .subtract(managementAccount.getLastCycleCreditAdjAmount());
            }
            //如果账户管理信息表(account_management_info)中的约定扣款方式（auto_payment_method）= 1（按最小还款额进行约定扣款）or 临时自扣方式（temp_payment_method）= 1（按最小还款额临时自扣）
        } else if(AutoPaymentMethod.MIN_ONE.getCode().equals(paymentMethod) || AutoPaymentMethod.MIN_THREE.getCode().equals(paymentMethod)){
           //最小还款额
            autoPayAmount = managementAccount.getTotalDueAmount();
        }
        return autoPayAmount;
    }

    @Override
    public ExchangePaymentBO batchProcess4ExchangePayment(AccountManagementInfo accountManagementInfo) {
        //应还款总额
        BigDecimal totalDueAmount = accountManagementInfo.getTotalDueAmount();
        String currency = accountManagementInfo.getCurrency();
        String autoExchangeIndicator = accountManagementInfo.getAutoExchangeIndicator();
        if (totalDueAmount.compareTo(BigDecimal.ZERO) <= 0
                || Constants.RMB.equals(currency)
                || AutoExIndicator.CLOSE.getCode().equals(autoExchangeIndicator)) {
            return null;
        }

        // <逻辑A2> 购汇条件判断
        // 机构参数表
        OrganizationInfoResDTO orgconfig = getParameterConfig(accountManagementInfo);
        // 1.最新一期账单
        AccountStatementInfo accountStatementInfo = getAccountStatementInfo(accountManagementInfo);
        // 2.比较日期
        // 机构参数表的上次累计日期
        // 账单账户表中的还款日
        LocalDate paymentDueDate = accountStatementInfo.getPaymentDueDate();
        // 机构参数表的当前累计日期
        //add by zcli 2020-05-12
        LocalDate lastProcessingDay = orgconfig.getLastProcessingDay();
        LocalDate nextProcessingDay = orgconfig.getNextProcessingDay();
        if (null == lastProcessingDay || null == paymentDueDate || null == nextProcessingDay) {
            logger.error("Required date parameters are null: lastProcessingDay={}, paymentDueDate={}, nextProcessingDay={}", lastProcessingDay, paymentDueDate, nextProcessingDay);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL, AccountingRepDetailEnum.OPD_ND);
        }
        boolean flag =
                lastProcessingDay.isBefore(paymentDueDate)
                        && (paymentDueDate.isBefore(nextProcessingDay) || paymentDueDate.isEqual(nextProcessingDay));
        if (flag) {
            // 3-4 获取交易码和机构参数表中的当前系统处理日
            Pair<String, String> result = getTransactionCodeAndDate(accountManagementInfo);
            // 购汇转出交易码
            String outCode = result.getLeft();
            // 购汇转入交易码
            String inCode = result.getRight();

            // 5.读取卡片授权信息表card_authorization_info
            // 6.读取人民币管理账户
            AccountManagementInfo accountManagementInfoRmb = getRmbManagement(accountManagementInfo,orgconfig.getOrganizationCurrency());

            // 7.得到外币与人民币之间的兑换汇率和汇率小数点位数
            List exRate = getExRate(accountManagementInfo);
            // 汇率
            BigDecimal rateValue =
                    Optional.ofNullable((BigDecimal) exRate.get(0)).orElse(BigDecimal.ZERO);
            // 汇率小数位
            int exponsent = Optional.of((int) exRate.get(1)).orElse(0);

            // 8.计算金额
            BigDecimal ovsBill = BigDecimal.ZERO;
            if (AutoExchangePaymentType.MIN
                    .getCode()
                    .equals(accountManagementInfo.getAutoExchangePaymentType())) {
                // 外币需还款金额
                ovsBill = accountManagementInfo.getTotalDueAmount();
            } else if (AutoExchangePaymentType.MAX
                    .getCode()
                    .equals(accountManagementInfo.getAutoExchangePaymentType())) {
                ovsBill = accountManagementInfo.getStatementDueAmount();
            }
            // 人民币需还款金额
            BigDecimal rmbBill =
                    ovsBill
                            .multiply(rateValue.divide(BigDecimal.valueOf(Math.pow(10, exponsent))))
                            .setScale(2, RoundingMode.HALF_UP);
            // 9.计算人民币可转出金额
            //调用交易路由规则获取交易管控单元ID,获取借记类交易管控单元参数，
            String transCtrlUnitCode = matchRule(outCode,accountManagementInfoRmb.getCurrency(),accountManagementInfoRmb.getProductNumber());
            logger.info("Calling transactionCtrlUnitService.findByUnitCodeAndOrg: organizationNumber={}, unitCode={}", accountManagementInfoRmb.getOrganizationNumber(), transCtrlUnitCode);
            TransactionCtrlUnitDTO transactionCtrlUnitDTO = transactionCtrlUnitService.findByUnitCodeAndOrg(accountManagementInfoRmb.getOrganizationNumber(), transCtrlUnitCode);
            logger.info("Called transactionCtrlUnitService.findByUnitCodeAndOrg successfully");
            if (ObjectUtils.isEmpty(transactionCtrlUnitDTO)) {
                logger.error("Transaction control unit parameter not found: orgNumber={}, transCtrlUnitCode={}", accountManagementInfo.getOrganizationNumber(), transCtrlUnitCode);
                throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT);
            }
            //借记类交易管控单元参数
            List<TransactionCtrlUnitDebitDTO> transactionCtrlUnitDebits = transactionCtrlUnitDTO.getTransactionCtrlUnitDebits();
            BigDecimal balance1 = new BigDecimal(0);
            ArrayList<String> transTypeCodelist = new ArrayList<>();
            for (TransactionCtrlUnitDebitDTO transactionCtrlUnitDebitDTO:transactionCtrlUnitDebits){
                ParmTransactionType parmTransactionType = parmTransactionTypeMapper.selectBytTransactionTypeCode(accountManagementInfoRmb.getOrganizationNumber(), transactionCtrlUnitDebitDTO.getTransactionTypeCode());
                if ("C".equals(parmTransactionType.getBalanceLoanDirection())) {
                    transTypeCodelist.add(transactionCtrlUnitDebitDTO.getTransactionTypeCode());
                } else {
                    logger.error("Transaction type balance loan direction is not credit: balanceLoanDirection={}", parmTransactionType.getBalanceLoanDirection());
                    throw new AnyTxnParameterException(AnyTxnParameterRespCodeEnum.S_BALANCE_LOAN_DIRECTION_IS_DEBIT);
                }
            }
            for (String transTypeCode : transTypeCodelist) {
                //查询统计账户
                List<AccountStatisticsInfo> accountStatisticsInfo = accountStatisticsInfoSelfMapper.selectByOrganAndType(accountManagementInfo.getOrganizationNumber(), transTypeCode,accountManagementInfo.getCurrency(),accountManagementInfo.getAccountManagementId());
                if (CollectionUtils.isEmpty(accountStatisticsInfo)) {
                    logger.info("Account statistics info not found: orgNumber={}, transTypeCode={}", accountManagementInfo.getOrganizationNumber(), transTypeCode);
                }
                for (AccountStatisticsInfo accountStatisticsInfo1 : accountStatisticsInfo) {
                    balance1 = balance1.add(accountStatisticsInfo1.getBalance());
                }
            }
//            AccountBalanceInfo accountBalanceInfo = getAccountBalanceInfo(accountManagementInfoRmb);
            //跳过，不断批
//            if (accountBalanceInfo == null) {
//                return null;
//            }
//            BigDecimal balance = accountBalanceInfo.getBalance() == null ? BigDecimal.ZERO : accountBalanceInfo.getBalance();
            BigDecimal balance = balance1 == null ? BigDecimal.ZERO : balance1;
            if (balance.compareTo(BigDecimal.ZERO) <= 0) {

                logger.info("Insufficient RMB amount");
                return null;
            } else if (balance.compareTo(rmbBill) < 0) {
                rmbBill = balance;
                ovsBill =
                        rmbBill.divide(
                                rateValue.divide(BigDecimal.valueOf(Math.pow(10, exponsent))),
                                2,
                                RoundingMode.HALF_UP);
            }
            // <逻辑A3>购汇转出转入交易入账
            LocalDate today = orgconfig.getToday();
            // 1. 组人民币购汇转出金融接口
            RecordedBO recordedOut =
                    exCurrency(
                            accountManagementInfoRmb, outCode, rmbBill, nextProcessingDay, today, TransactionSourceEnum.LOCAL.getCode());
            // 2.组外币购汇转入金融接口>。
            RecordedBO recordedIn =
                    exCurrency(
                            accountManagementInfo, inCode, ovsBill, nextProcessingDay, today, TransactionSourceEnum.LOCAL.getCode());

            return new ExchangePaymentBO(recordedIn, recordedOut);
        }
        return null;
    }
    /**
     * 匹配路由规则
     * @param transactionCode
     * @return
     */
    private String matchRule(String transactionCode,String currency,String productNumber) {
        if (ObjectUtils.isNotEmpty(transactionCode)&& ObjectUtils.isNotEmpty(currency)) {
            HashMap<String, Object> param = new HashMap<>();
            param.put("postTxnCode", transactionCode);
            param.put("accountCurrency", currency);
            param.put("cardProductCode", productNumber);
            //基于规则类型找到规则匹配器
            logger.info("parm:{}", JSON.toJSONString(param));
            TxnRuleMatcher ruleMatcher = RuleMatcherManager.getMatcher(RuleTypeEnum.TRANSACTION_ROUTING_RULE.getRuleType(), OrgNumberUtils.getOrg());
            if (ruleMatcher != null) {
                DataInputDTO dataInput = new DataInputDTO(param, RuleTypeEnum.TRANSACTION_ROUTING_RULE.getRuleType());
                Map<String, Object> ruleMap = ruleMatcher.execute(dataInput);
                if (ruleMap != null && !ruleMap.isEmpty()) {
                    return String.valueOf(ruleMap.entrySet().iterator().next().getValue());
                }
            }
            if (logger.isDebugEnabled()) {
                logger.debug("Transaction control unit rule matching failed, posting rejected: param={}", param);
            }
            logger.error("Transaction control unit rule matching failed: transactionCode={}, currency={}, productNumber={}", transactionCode, currency, productNumber);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.S_ACCOUNTING_RULE_MATCH_FAILE);
        }

        return null;
    }
    /**
     * @param accountManagementInfo 管理账户
     * @param txnTransactionCode    交易码
     * @param amount                人民币购汇转出金额
     * @param nextProcessingDay     下一系统处理日
     * @param transactionSource     自动购汇给0，手动购汇给1
     * @return 入账接口赋值
     */
    private RecordedBO exCurrency(
            AccountManagementInfo accountManagementInfo,
            String txnTransactionCode,
            BigDecimal amount,
            LocalDate nextProcessingDay,
            LocalDate today,
            String transactionSource) {
        // 交易码参数表
        logger.info("Calling transactionCodeService.findTransactionCode: organizationNumber={}, transactionCode={}", accountManagementInfo.getOrganizationNumber(), txnTransactionCode);
        TransactionCodeResDTO codeConfig =
                transactionCodeService.findTransactionCode(
                        accountManagementInfo.getOrganizationNumber(), txnTransactionCode);
        logger.info("Called transactionCodeService.findTransactionCode successfully");


        RecordedBO recorded = new RecordedBO();
        recorded.setTxnAccountManageId(accountManagementInfo.getAccountManagementId());
        recorded.setTxnAuthorizationMatchIndicator(RecordedEnum.NOT_MATCH_AUTH.getCode());
        // 入账金额
        recorded.setTxnBillingAmount(amount);
        // 入账币种
        recorded.setTxnBillingCurrency(accountManagementInfo.getCurrency());
        // 入账日期
        recorded.setTxnBillingDate(nextProcessingDay);
        recorded.setTxnExchangeRate(BigDecimal.ZERO);
        //因为批量购汇移到日切前了,所以入账方式改为实时入账
        recorded.setTxnPostMethod(RecordedEnum.REAL_TIME.getCode());
        recorded.setTxnReleaseAuthorizationAmount(RecordedEnum.NOT_RECOVER.getCode());
        recorded.setTxnReverseFeeIndicator(RecordedEnum.YES.getCode());

        // 清算金额
        recorded.setTxnTransactionAmount(amount);
        // 清算币种
        recorded.setTxnSettlementCurrency(accountManagementInfo.getCurrency());
        // 交易金额
        recorded.setTxnTransactionAmount(amount);
        // 交易码
        recorded.setTxnTransactionCode(txnTransactionCode);
        // 交易币种
        recorded.setTxnTransactionCurrency(accountManagementInfo.getCurrency());
        // 交易日期
        recorded.setTxnTransactionDate(nextProcessingDay.atTime(LocalTime.now()));
        recorded.setTxnTransactionDescription(codeConfig.getDescription());
        recorded.setTxnTransactionSource(transactionSource);

        recorded.setCustomerId(accountManagementInfo.getCustomerId());

        return recorded;
    }

    /**
     * @param accountManagementInfoRmb RMB管理账户
     * @return 溢缴款交易账户
     */
    private AccountBalanceInfo getAccountBalanceInfo(
            AccountManagementInfo accountManagementInfoRmb) {
        AccountBalanceInfo accountBalanceInfo =
                accountBalanceInfoSelfMapper.selectOverPaymentAbi(
                        accountManagementInfoRmb.getAccountManagementId());
        if (null == accountBalanceInfo
                || accountBalanceInfo.getBalance().compareTo(BigDecimal.ZERO) == 0) {
            //跳过，批处理不断批
            logger.info("Insufficient RMB amount");
        }
        return accountBalanceInfo;
    }

    /**
     * @param accountManagementInfo 外币管理账户
     */
    private List getExRate(AccountManagementInfo accountManagementInfo) {
        // 7.得到外币与人民币之间的兑换汇率和汇率小数点位数
        String currency = accountManagementInfo.getCurrency();
        logger.info("Calling currencyRateService.findByOrgAndCurrencyAndRateType: organizationNumber={}, currency={}", accountManagementInfo.getOrganizationNumber(), currency);
        CurrencyRateResDTO paramCurrencyRate = currencyRateService.findByOrgAndCurrencyAndRateType(
                accountManagementInfo.getOrganizationNumber(), currency, Constants.RMB, "0");
        logger.info("Called currencyRateService.findByOrgAndCurrencyAndRateType successfully");
        // 汇率
        BigDecimal rateValue = new BigDecimal(paramCurrencyRate.getRateValue());
        // 汇率小数位
        int exponsent = paramCurrencyRate.getExponent();
        return Arrays.asList(rateValue, exponsent);
    }

    /**
     * @param accountManagementInfo 外币管理账户
     * @return RMB管理账户
     */
    private AccountManagementInfo getRmbManagement(
            AccountManagementInfo accountManagementInfo,String orgCurry) {
       AccountManagementInfo accountManagementInfoRmb =
                accountManagementInfoSelfMapper.selectVaildByOrgProNumCurrAndCusId(accountManagementInfo.getOrganizationNumber(),accountManagementInfo.getProductNumber(),orgCurry,accountManagementInfo.getCustomerId());
        if (null == accountManagementInfoRmb) {
            logger.error("RMB account not found");
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.RA);
        }
        return accountManagementInfoRmb;
    }

    /**
     * @param accountManagementInfo 外币管理账户
     * @return 账户账单
     */
    private AccountStatementInfo getAccountStatementInfo(
            AccountManagementInfo accountManagementInfo) {
        // 1.最新一期账单
        logger.info(
                "Query latest statement for foreign currency account: accountId={}, lastStatementDate={}",
                accountManagementInfo.getAccountManagementId(),
                accountManagementInfo.getLastStatementDate());

        return accountStatementInfoSelfMapper.selectByAccountManagementIdAndDate(
                accountManagementInfo.getAccountManagementId(),
                accountManagementInfo.getLastStatementDate());
    }

    /**
     * @param accountManagementInfo 外币管理账户
     * @return 机构参数表
     */
    private OrganizationInfoResDTO getParameterConfig(AccountManagementInfo accountManagementInfo) {
        // 机构参数表
        /*ParameterConfig orgconfig =
                parameterService.findParamOrganizationInfo(accountManagementInfo.getOrganizationNumber());*/
        logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", accountManagementInfo.getOrganizationNumber());
        OrganizationInfoResDTO orgconfig = organizationInfoService.findOrganizationInfo(accountManagementInfo.getOrganizationNumber());
        logger.info("Called organizationInfoService.findOrganizationInfo successfully");
        if (orgconfig == null) {
            //throw new AnyTXNBusRuntimeException("机构号不存在");
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.GMP);
        }
        return orgconfig;
    }

    private Pair<String, String> getTransactionCodeAndDate(AccountManagementInfo accountManagementInfo) {
        // 3.购汇转出交易码TRF-OUT-CDE
        logger.info("Calling transactionCodeConfigService.findByOrgAndType: organizationNumber={}, type={}", accountManagementInfo.getOrganizationNumber(), "1");
        String outCode = transactionCodeConfigService.findByOrgAndType(accountManagementInfo.getOrganizationNumber(), "1").getTransactionCode();
        logger.info("Called transactionCodeConfigService.findByOrgAndType successfully");
        // 4.购汇转入交易码TRF-IN-CDE
        logger.info("Calling transactionCodeConfigService.findByOrgAndType: organizationNumber={}, type={}", accountManagementInfo.getOrganizationNumber(), "2");
        String inCode = transactionCodeConfigService.findByOrgAndType(accountManagementInfo.getOrganizationNumber(), "2").getTransactionCode();
        logger.info("Called transactionCodeConfigService.findByOrgAndType successfully");
        return Pair.of(outCode, inCode);
    }

    @Override
    public int getAccountManagementIdCount4Exchange(String partitionKey) {
        Integer partitionKey0 = null;
        Integer partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = (null == partitionKey.split("-")[0] ? null : Integer.valueOf(partitionKey.split("-")[0]));
            partitionKey1 = (null == partitionKey.split("-")[1] ? null : Integer.valueOf(partitionKey.split("-")[1]));
        }
        return accountManagementInfoSelfMapper.getAccountManagementIdCount4Exchange(
                partitionKey0, partitionKey1);
    }

    @Override
    public List<String> queryAccountManagementIds4Exchange(
            String partitionKey, List<Integer> rowNumbers) {
        Integer partitionKey0 = null;
        Integer partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = (null == partitionKey.split("-")[0] ? null : Integer.valueOf(partitionKey.split("-")[0]));
            partitionKey1 = (null == partitionKey.split("-")[1] ? null : Integer.valueOf(partitionKey.split("-")[1]));
        }
        return accountManagementInfoSelfMapper.queryAccountManagementIds4Exchange(
                partitionKey0, partitionKey1, rowNumbers);
    }

    /**
     * 修改账户对应状态，若为0 - 新户 1 - 静止 则改为2 - 活跃
     *
     * @param accountManagementId
     */
    @Override
    public void modifyAccountManagementAccountStatus(String accountManagementId) {
        try {
            AccountManagementInfo accountManagementInfo =
                    accountManagementInfoMapper.selectByPrimaryKey(accountManagementId);
            if (AccountStatusEnum.NEW.getCode().equals(accountManagementInfo.getAccountStatus())
                    || AccountStatusEnum.SLEEP.getCode().equals(accountManagementInfo.getAccountStatus())) {
                accountManagementInfo.setAccountStatus(AccountStatusEnum.ACTIVE.getCode());
                int result = accountManagementInfoMapper.updateByPrimaryKeySelective(accountManagementInfo);
                if (result == 0) {
                    /*throw new AnyTXNBusRuntimeException(StatusCode.UPDATE_INVALID.getCode(),
                            StatusCode.UPDATE_INVALID.getMsg());*/
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_UPDATE_FAIL);
                }
            }
        } catch (AnyTxnAccountingException e) {
            logger.warn("modifyAccountManagementInfo : {}", e.getErrMsg());
            throw e;
        } catch (PersistenceException e) {
            logger.error("modifyAccountManagementInfo : ", e);
            /*throw new AnyTXNBusRuntimeException(
                    StatusCode.MYBATIS_FAIL.getCode(), StatusCode.MYBATIS_FAIL.getMsg());*/
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_MYBATIS_FAIL);
        } catch (Exception e) {
            logger.error("AccountManageInfoServiceImpl modifyAccountManagementAccountStatus error", e);
            /*throw new AnyTXNBusRuntimeException(
                    TransactionEnum.DATABASE_ERROR.getCode(), TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR);
        }

    }

    @Override
    public InCollectionDTO batchProcess4InCollection(AccountManagementInfo accountManagementInfo) {
        InCollectionDTO inCollectionDTO = new InCollectionDTO();
        String inCollectionFlag = null;
        String inCollectionIndicator = accountManagementInfo.getInCollectionIndicator();
        if (StringUtils.isBlank(inCollectionIndicator)) {
            //throw new AnyTXNBusRuntimeException("管理账户信息表in_collection_indicato为空");
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL, AccountingRepDetailEnum.MAI_E);
        }
        if (InCollectionIndicator.COLLECTION.getCode().equals(inCollectionIndicator)) {
            inCollectionFlag = "1";
        } else if (InCollectionIndicator.NOT_COLLECTION.getCode().equals(inCollectionIndicator)) {
            inCollectionFlag = "2";
        }

        inCollectionDTO.setCollectionFlag(inCollectionFlag);
        inCollectionDTO.setAccountManagementId(accountManagementInfo.getAccountManagementId());
        List<CardAuthorizationInfo> cardAuthorizationInfos =
                cardAuthorizationInfoSelfMapper.selectByPrimaryCustomerId(OrgNumberUtils.getOrg(),
                        accountManagementInfo.getCustomerId());
        String cardNumber = "                ";
        if (!cardAuthorizationInfos.isEmpty()) {
            cardNumber = cardAuthorizationInfos.get(0).getCardNumber();
        }
        AccountStatisticsInfo accountStatisticsInfo =
                accountStatisticsInfoSelfMapper.selectByIdAndType(
                        accountManagementInfo.getAccountManagementId(),
                        TransactionTypeCodeEnum.AGGREGATION.getCode());

        inCollectionDTO.setCardNumber(cardNumber);
        inCollectionDTO.setBalance(accountStatisticsInfo.getBalance());
        inCollectionDTO.setDueAmount(accountManagementInfo.getTotalDueAmount());
        inCollectionDTO.setCustomerNumber(accountManagementInfo.getCustomerId());
        CustomerAuthorizationInfo customerAuthorizationInfo =
                customerAuthorizationInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(),
                        accountManagementInfo.getCustomerId());
        if (ObjectUtils.isNotEmpty(customerAuthorizationInfo)){
            inCollectionDTO.setCustomerName(customerAuthorizationInfo.getChineseName());
            inCollectionDTO.setIdType(customerAuthorizationInfo.getIdType());
            inCollectionDTO.setIdNumber(customerAuthorizationInfo.getIdNumber());
        }
        logger.info("Calling systemTableService.findBySystemId: systemId={}", "0000");
        SystemTableDTO paramSystemTable = systemTableService.findBySystemId("0000");
        logger.info("Called systemTableService.findBySystemId successfully");
        inCollectionDTO.setInCollectionDate(DateHelper.format2Ymd(paramSystemTable.getToday()));
        inCollectionDTO.setOutCollectionDate(DateHelper.format2Ymd(paramSystemTable.getToday()));

        return inCollectionDTO;
    }

    /**
     * 根据客户号、账户产品编号、机构号查询管理账户信息
     *
     * @param customerId         客户号
     * @param productNumber      账户产品编号
     * @param organizationNumber 机构号
     * @return AccountManagementInfoDTO
     */
    @Override
    public AccountManagementInfoDTO selectByCusIdProNumAndOrg(
            String customerId, String productNumber, String organizationNumber) {
        AccountManagementInfo accountManagementInfo =
                accountManagementInfoSelfMapper.selectByCusIdProNumAndOrg(
                        customerId, productNumber, organizationNumber);
        return BeanMapping.copy(accountManagementInfo, AccountManagementInfoDTO.class);
    }

    /**
     * 根据客户号、账户产品编号、机构号查询管理账户信息
     *
     * @param customerId         客户号
     * @param productNumber      账户产品编号
     * @param organizationNumber 机构号
     * @return List<AccountManagementInfoDTO>
     * <p>
     * 和上面的selectByCusIdProNumAndOrg 查询的一样，因为开双币卡上面sql查询到两条数据只用了一个实体接收，导致程序报错
     */
    @Override
    public List<AccountManagementInfoDTO> selectByCusIdProNumAndOrgList(String customerId, String productNumber, String organizationNumber) {
        List<AccountManagementInfo> accountManagementInfos =
                accountManagementInfoSelfMapper.selectByCusIdProNumAndOrgList(
                        customerId, productNumber, organizationNumber);
        return BeanMapping.copyList(accountManagementInfos, AccountManagementInfoDTO.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {AnyTxnCustAccountLockException.class,AnyTxnAccountingException.class})
    public void modifyAccountManagementInfoCms(AccountManagementInfoDTO accountManagementInfoDto) {
        AccountManagementInfo accountManagementInfo = new AccountManagementInfo();
        if (null != accountManagementInfoDto) {
            CustomerBasicInfo customerBasicInfo = customerBasicInfoSelfMapper.selectByOrgAndCustId(accountManagementInfoDto.getOrganizationNumber(), accountManagementInfoDto.getCustomerId());
            //承德银行bug——账户延滞状态大于2时，无法签约约定还款。
            //去掉这个判断

            //            if (!ObjectUtils.isEmpty(customerBasicInfo)){
//                int fiveTypeIndicator = Integer.parseInt(accountManagementInfoDto.getFiveTypeIndicator());
//                int loanFiveTypeIndicator = Integer.parseInt(customerBasicInfo.getLoanFiveTypeIndicator());
//                if (fiveTypeIndicator > loanFiveTypeIndicator){
//                    log.error("账户的五级分类不得高于个人贷款的五级分类");
//                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR, AccountingRepDetailEnum.FLC);
//                }
//            }
            logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", accountManagementInfoDto.getOrganizationNumber());
            OrganizationInfoResDTO orgConfig = organizationInfoService.findOrganizationInfo(accountManagementInfoDto.getOrganizationNumber());
            logger.info("Called organizationInfoService.findOrganizationInfo successfully");
            if (null == orgConfig) {
                logger.error("Failed to query organization parameter table by organization number: {}", accountManagementInfoDto.getOrganizationNumber());
                //throw new AnyTXNBusRuntimeException("500", "根据机构号查询机构参数表失败");
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.GMP);
            }
            if(!StringUtils.equals("C",accountManagementInfoDto.getLiability())){
                List<CustomerAddressInfo> addressList = customerAddressInfoSelfMapper.selectByCusId(accountManagementInfoDto.getOrganizationNumber(),accountManagementInfoDto.getCustomerId());
                addressList = Optional.ofNullable(addressList).orElseGet(Collections::emptyList).stream().filter(e -> StringUtils.equals(e.getType(), accountManagementInfoDto.getStatementAddressType())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(addressList)) {
                    logger.error("Customer address type does not exist: customerId={}, addressType={}", accountManagementInfoDto.getCustomerId(), accountManagementInfoDto.getStatementAddressType());
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.NULL,"Address does not exist");
                }
            }

            // 适配cbs模块 设置统计账户账面的cbs 关户日期
            AccountStatisticsInfo accountStatisticsInfo = null;
            List<AccountStatisticsInfo> statisticsInfos = statisticsInfoMapper.selectListByAccountNumber(accountManagementInfoDto.getOrganizationNumber(),
                            accountManagementInfoDto.getAccountManagementId());
            if (!CollectionUtils.isEmpty(statisticsInfos)){
                AccountStatisticsInfo statisticsInfo = statisticsInfos.stream().filter(s -> "00000".equals(s.getTransactionTypeCode())).findFirst().orElse(null);
                if (Objects.nonNull(statisticsInfo)){
                    accountStatisticsInfo = statisticsInfo;
                }
            }

            //新增临时自扣参数检验
//            checkTempPaymentType(accountManagementInfoDto,orgConfig);
            // 客户对账控制信息 2020-05-13 zcli
            logger.info("Calling custReconciliationControlService.getControl: customerId={}, organizationNumber={}", accountManagementInfoDto.getCustomerId(), accountManagementInfoDto.getOrganizationNumber());
            CustReconciliationControlDTO controlDTO = custReconciliationControlService.getControl(accountManagementInfoDto.getCustomerId(), accountManagementInfoDto.getOrganizationNumber());
            logger.info("Called custReconciliationControlService.getControl successfully");

            //查询客户下所有管理账户
            List<AccountManagementInfo> managementInfos = accountManagementInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(),accountManagementInfoDto.getCustomerId());
            List<AccountManagementInfo> otherManges = managementInfos.stream().filter(x -> !Objects.equals(accountManagementInfoDto.getAccountManagementId(), x.getAccountManagementId())).collect(Collectors.toList());
            Optional<AccountManagementInfo> first = otherManges.stream().filter(a -> StringUtils.isNotEmpty(a.getFiveTypeIndicator())).sorted(Comparator.comparing(AccountManagementInfo::getFiveTypeIndicator, Comparator.reverseOrder())).findFirst();
            String newFiveTypeIndicator = accountManagementInfoDto.getFiveTypeIndicator();
            if(first.isPresent()){
                newFiveTypeIndicator = first.get().getFiveTypeIndicator();
            }
            for (AccountManagementInfo otherMange : otherManges) {
                AccountManagementInfo managementInfo = new AccountManagementInfo();
                managementInfo.setAccountManagementId(otherMange.getAccountManagementId());
                managementInfo.setFiveTypeIndicator(newFiveTypeIndicator);
                managementInfo.setVersionNumber(otherMange.getVersionNumber());
                accountManagementInfoMapper.updateByPrimaryKeySelective(managementInfo);
            }
            accountManagementInfoDto.setFiveTypeIndicator(newFiveTypeIndicator);
            BeanMapping.copy(accountManagementInfoDto, accountManagementInfo);
            AccountManagementInfo oldAccountManageInfo =
                    accountManagementInfoMapper.selectByPrimaryKey(accountManagementInfo.getAccountManagementId());
            //如果有修改标志,需要记录标志代号维护历史
            if (null != oldAccountManageInfo) {
                String blockCode = StringUtils.isBlank(oldAccountManageInfo.getBlockCode()) ?
                        StringUtils.EMPTY : oldAccountManageInfo.getBlockCode();

                if (!blockCode.equals(accountManagementInfoDto.getBlockCode())) {

                    //封锁码修改规则
                    if (null != accountManagementInfoDto.getBlockCode() && !"".equals(accountManagementInfoDto.getBlockCode())) {
                        logger.info("Calling productInfoService.findProductInfo: organizationNumber={}, productNumber={}, currency={}", oldAccountManageInfo.getOrganizationNumber(), oldAccountManageInfo.getProductNumber(), oldAccountManageInfo.getCurrency());
                        ProductInfoResDTO productInfoOld =
                                productInfoService.findProductInfo(oldAccountManageInfo.getOrganizationNumber(),
                                        oldAccountManageInfo.getProductNumber(),
                                        oldAccountManageInfo.getCurrency()).get(0);
                        logger.info("Called productInfoService.findProductInfo successfully");

                        if (null == productInfoOld) {
                            logger.error("Failed to query account product parameter table by old organization number, product number, currency: orgNum={}, productNum={}, currency={}", oldAccountManageInfo.getOrganizationNumber(), oldAccountManageInfo.getProductNumber(), oldAccountManageInfo.getCurrency());
                            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.Q_OPC);
                        }
                        if (null == productInfoOld.getAccountBlockCodeTableId()) {
                            logger.error("Account block code parameter table ID is null in old account product: productNumber={}", oldAccountManageInfo.getProductNumber());
                            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_NOT_NULL, AccountingRepDetailEnum.U_AB_AP);
                        }

                        BlockCodeAccountResDTO accountBlockCodeTableIdOld = new BlockCodeAccountResDTO();
                        if (StringUtils.isNotBlank(oldAccountManageInfo.getBlockCode())) {
                            logger.info("Calling blockCodeAccountService.findBlockCodeAccount: organizationNumber={}, tableId={}, blockCode={}", oldAccountManageInfo.getOrganizationNumber(), productInfoOld.getAccountBlockCodeTableId(), oldAccountManageInfo.getBlockCode());
                            accountBlockCodeTableIdOld = blockCodeAccountService.findBlockCodeAccount(oldAccountManageInfo.getOrganizationNumber(),
                                    productInfoOld.getAccountBlockCodeTableId(),
                                    oldAccountManageInfo.getBlockCode());
                            logger.info("Called blockCodeAccountService.findBlockCodeAccount successfully");
                        } else {
                            logger.info("Calling blockCodeAccountService.findBlockCodeAccount: organizationNumber={}, tableId={}, blockCode=null", oldAccountManageInfo.getOrganizationNumber(), productInfoOld.getAccountBlockCodeTableId());
                            accountBlockCodeTableIdOld = blockCodeAccountService.findBlockCodeAccount(oldAccountManageInfo.getOrganizationNumber(),
                                    productInfoOld.getAccountBlockCodeTableId(),
                                    null);
                            logger.info("Called blockCodeAccountService.findBlockCodeAccount successfully");
                        }

                        if (null == accountBlockCodeTableIdOld) {
                            logger.error("Failed to query account block code parameter table by organization number, table ID, old block code: orgNum={}, tableId={}, blockCode={}", oldAccountManageInfo.getOrganizationNumber(), productInfoOld.getAccountBlockCodeTableId(), oldAccountManageInfo.getBlockCode());
                            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.Q_OBB);
                        }
                        if (null == accountBlockCodeTableIdOld.getPriority()) {
                            logger.error("Invalid block code: blockCode={}", oldAccountManageInfo.getBlockCode());
                            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_NOT_NULL, AccountingRepDetailEnum.IBC);
                        }
                        Integer priorityOld = accountBlockCodeTableIdOld.getPriority();

                        logger.info("Calling productInfoService.findProductInfo: organizationNumber={}, productNumber={}, currency={}", accountManagementInfoDto.getOrganizationNumber(), accountManagementInfoDto.getProductNumber(), accountManagementInfoDto.getCurrency());
                        ProductInfoResDTO productInfoNew = productInfoService.findProductInfo(accountManagementInfoDto.getOrganizationNumber(),
                                accountManagementInfoDto.getProductNumber(),
                                accountManagementInfoDto.getCurrency()).get(0);
                        logger.info("Called productInfoService.findProductInfo successfully");

                        if (null == productInfoNew) {
                            logger.error("Failed to query account product parameter table by new organization number, product number, currency: orgNum={}, productNum={}, currency={}", accountManagementInfoDto.getOrganizationNumber(), accountManagementInfoDto.getProductNumber(), accountManagementInfoDto.getCurrency());
                            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.Q_OPC);
                        }
                        if (null == productInfoNew.getAccountBlockCodeTableId()) {
                            logger.error("Account block code parameter table ID is null in new account product: productNumber={}", accountManagementInfoDto.getProductNumber());
                            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_NOT_NULL, AccountingRepDetailEnum.U_AB_AP2);
                        }

                        BlockCodeAccountResDTO accountBlockCodeTableIdNew = new BlockCodeAccountResDTO();
                        if (StringUtils.isNotBlank(accountManagementInfoDto.getBlockCode())) {
                            accountBlockCodeTableIdNew = blockCodeAccountService.findBlockCodeAccount(
                                    accountManagementInfoDto.getOrganizationNumber(),
                                    productInfoNew.getAccountBlockCodeTableId(),
                                    accountManagementInfoDto.getBlockCode());
                        } else {
                            accountBlockCodeTableIdNew = blockCodeAccountService.findBlockCodeAccount(
                                    accountManagementInfoDto.getOrganizationNumber(),
                                    productInfoNew.getAccountBlockCodeTableId(),
                                    null);
                        }

                        if (null == accountBlockCodeTableIdNew) {
                            logger.error("Failed to query account block code parameter table by organization number, table ID, new block code: orgNum={}, tableId={}, blockCode={}", accountManagementInfoDto.getOrganizationNumber(), productInfoNew.getAccountBlockCodeTableId(), accountManagementInfoDto.getBlockCode());
                            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.Q_OBB2);
                        }
                        if (null == accountBlockCodeTableIdNew.getPriority()) {
                            logger.error("Invalid block code: blockCode={}", accountManagementInfoDto.getBlockCode());
                            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_NOT_NULL, AccountingRepDetailEnum.IBC);
                        }
                        Integer priorityNew = Integer.valueOf(accountBlockCodeTableIdNew.getPriority());
                        boolean priorityFlag = priorityNew > priorityOld;
                        if (!priorityFlag) {
                            logger.error("New block code priority is lower than original block code: newPriority={}, oldPriority={}", priorityNew, priorityOld);
                            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.PNB_O);
                        }
                    }

                    //更新账户封锁码字段
                    accountManagementInfo.setPreviousBlockCode(oldAccountManageInfo.getBlockCode());
                    accountManagementInfo.setPreviousBlockCodeSetDate(oldAccountManageInfo.getBlockCodeSetDate());
                    accountManagementInfo.setBlockCode(accountManagementInfoDto.getBlockCode());

                    // 封锁码设置日期的取值处理逻辑一样
                    LocalDate blockCodeSetDate = custReconciliationControlService.getBillingDate(controlDTO, orgConfig.getAccruedThruDay(), orgConfig.getToday(), orgConfig.getNextProcessingDay());

                    // 封锁码的设置日期取入账日期
                    accountManagementInfo.setBlockCodeSetDate(blockCodeSetDate);
                    if (!org.springframework.util.StringUtils.isEmpty(accountManagementInfoDto.getBlockCode())
                            && accountStatusOfERSW.contains(accountManagementInfoDto.getBlockCode())
                            && Objects.nonNull(accountStatisticsInfo)) {
                        // 统计账户
                        setCbsCloseDate(blockCodeSetDate, accountStatisticsInfo);
                    }

                    accountManagementInfo.setUpdateTime(LocalDateTime.now());
                    accountManagementInfo.setUpdateBy(LoginUserUtils.getLoginUserName());
                    accountManagementInfo.setVersionNumber(1L);

                    BlockCodeMaintenanceLog blockCodeMaintenanceLog = new BlockCodeMaintenanceLog();
                    blockCodeMaintenanceLog.setLogId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                    blockCodeMaintenanceLog.setKey(oldAccountManageInfo.getAccountManagementId());
                    blockCodeMaintenanceLog.setKeyType(CardBusinessConstant.KEY_TYPE_A);
                    blockCodeMaintenanceLog.setBranchNumber(oldAccountManageInfo.getOrganizationNumber());
                    blockCodeMaintenanceLog.setBlockCodeBefore(oldAccountManageInfo.getBlockCode());
                    blockCodeMaintenanceLog.setBlockCodeDateBefore(oldAccountManageInfo.getBlockCodeSetDate());
                    blockCodeMaintenanceLog.setPreviousBlockCodeBefore(oldAccountManageInfo.getPreviousBlockCode());
                    blockCodeMaintenanceLog.setPreviousBlockDateBefore(oldAccountManageInfo.getPreviousBlockCodeSetDate());
                    blockCodeMaintenanceLog.setPreBlockStopDateBefore(oldAccountManageInfo.getPreviousBlockCodeSetDate());
                    //更新后
                    blockCodeMaintenanceLog.setBlockCodeAfter(accountManagementInfo.getBlockCode());
                    blockCodeMaintenanceLog.setBlockCodeDateAfter(accountManagementInfo.getBlockCodeSetDate());
                    blockCodeMaintenanceLog.setPreviousBlockCodeAfter(accountManagementInfo.getPreviousBlockCode());
                    blockCodeMaintenanceLog.setPreBlockDateAfter(accountManagementInfo.getPreviousBlockCodeSetDate());
                    blockCodeMaintenanceLog.setPreBlockStopDateAfter(null);
                    blockCodeMaintenanceLog.setCreateTime(LocalDateTime.now());
                    blockCodeMaintenanceLog.setUpdateTime(LocalDateTime.now());
                    blockCodeMaintenanceLog.setUpdateBy(LoginUserUtils.getLoginUserName());
                    blockCodeMaintenanceLog.setVersionNumber(1);

                    blockCodeMaintenanceLogMapper.insertSelective(blockCodeMaintenanceLog);
                }
                //增加账单日修改的触发逻辑
                boolean flag = accountManagementInfoDto.getCycleDay().equals(oldAccountManageInfo.getCycleDay());
                if (!flag) {
                    if (accountManagementInfoDto.getCycleDay() <= 0 || accountManagementInfoDto.getCycleDay() >= 29) {
                        logger.error("Billing day must be between 1-28: cycleDay={}", accountManagementInfoDto.getCycleDay());
                        throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.BD_BJ);
                    }
                    if (null == oldAccountManageInfo.getLastStatementDate()
                            || LocalDate.parse("0001-01-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")).compareTo(oldAccountManageInfo.getLastStatementDate()) == 0) {
                        logger.error("Cannot modify billing day without previous statement: accountId={}", accountManagementInfoDto.getAccountManagementId());
                        throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.MB_PB);
                    }
                    if (null != oldAccountManageInfo.getBlockCode() && !"".equals(oldAccountManageInfo.getBlockCode())) {
                        logger.error("Cannot modify billing day due to account block code: accountId={}, blockCode={}", accountManagementInfoDto.getAccountManagementId(), oldAccountManageInfo.getBlockCode());
                        throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_TYPE_FAULT, AccountingRepDetailEnum.DAB_BD);
                    }
                    if (oldAccountManageInfo.getCycleDue() >= 2) {
                        logger.error("Cannot modify billing day for overdue account: accountId={}, cycleDue={}", accountManagementInfoDto.getAccountManagementId(), oldAccountManageInfo.getCycleDue());
                        throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_VALUE_FAULT, AccountingRepDetailEnum.CU_AC);
                    }
                    if (null == accountManagementInfoDto.getOrganizationNumber()) {
                        logger.error("Organization number cannot be null");
                        throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL, AccountingRepDetailEnum.ON_E);
                    }
                    if (null == accountManagementInfoDto.getAccountManagementId()) {
                        logger.error("Account management ID cannot be null");
                        throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL, AccountingRepDetailEnum.MI_E);
                    }
                    List<InstallOrder> installOrders = installOrderSelfMapper.selectByOrgNumAndAcctManageId(accountManagementInfoDto.getOrganizationNumber(),
                            accountManagementInfoDto.getAccountManagementId());
                    List<InstallOrderDTO> installOrderDtos = BeanMapping.copyList(installOrders, InstallOrderDTO.class);
                    if (null != installOrderDtos) {
                        for (InstallOrderDTO installOrderDto : installOrderDtos) {
                            if ("1".equals(installOrderDto.getStatus())) {
                                logger.error("Cannot modify billing day with unsettled installment orders: accountId={}, installOrderId={}", accountManagementInfoDto.getAccountManagementId(), installOrderDto.getInstallOrderId());
                                //throw new AnyTXNBusRuntimeException("500", "有分期订单尚未结清 不能修改账单日");
                                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_STATUS_FAULT, AccountingRepDetailEnum.BI_DA);
                            }
                        }
                    }
                }

            }

            //延滞状态判断
            Short cycleDue = accountManagementInfoDto.getCycleDue();
            if (cycleDue.equals(Short.parseShort("0"))) {
                if (accountManagementInfoDto.getCurrentDueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CM);
                }
                if (accountManagementInfoDto.getPastDueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CX);
                }
                if (accountManagementInfoDto.getDay30DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C30);
                }
                if (accountManagementInfoDto.getDay60DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C60);
                }
                if (accountManagementInfoDto.getDay90DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C90);
                }
                if (accountManagementInfoDto.getDay120DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C120);
                }
                if (accountManagementInfoDto.getDay150DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C150);
                }
                if (accountManagementInfoDto.getDay180DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C180);
                }
                if (accountManagementInfoDto.getDay210DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C210);
                }
                if (accountManagementInfoDto.getDay240DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C240);
                }
                if (accountManagementInfoDto.getDay270DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C270);
                }
                if (accountManagementInfoDto.getDay300DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C300);
                }
                if (accountManagementInfoDto.getDay330DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C330);
                }
                if (accountManagementInfoDto.getDay360DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C360);
                }
//                if (accountManagementInfoDto.getDay390DueAmount().compareTo(BigDecimal.ZERO)!=0) {
//                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C390);
//                }
            }
            if (cycleDue.equals(Short.parseShort("1")) && oldAccountManageInfo.getCurrentDueAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (accountManagementInfoDto.getCurrentDueAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    logger.error("Current due amount validation failed for cycle due 1: currentDueAmount={}", accountManagementInfoDto.getCurrentDueAmount());
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CM2);
                }
                if (accountManagementInfoDto.getPastDueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CX);
                }
                if (accountManagementInfoDto.getDay30DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C30);
                }
                if (accountManagementInfoDto.getDay60DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C60);
                }
                if (accountManagementInfoDto.getDay90DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C90);
                }
                if (accountManagementInfoDto.getDay120DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C120);
                }
                if (accountManagementInfoDto.getDay150DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C150);
                }
                if (accountManagementInfoDto.getDay180DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C180);
                }
                if (accountManagementInfoDto.getDay210DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C210);
                }
                if (accountManagementInfoDto.getDay240DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C240);
                }
                if (accountManagementInfoDto.getDay270DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C270);
                }
                if (accountManagementInfoDto.getDay300DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C300);
                }
                if (accountManagementInfoDto.getDay330DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C330);
                }
                if (accountManagementInfoDto.getDay360DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C360);
                }
//                if (accountManagementInfoDto.getDay390DueAmount().compareTo(BigDecimal.ZERO)!=0) {
//                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C390);
//                }
            }
            if (cycleDue.equals(Short.parseShort("2")) && oldAccountManageInfo.getPastDueAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (accountManagementInfoDto.getCurrentDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CML0);
                }
                if (accountManagementInfoDto.getPastDueAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CX_LE0);
                }
                if (accountManagementInfoDto.getDay30DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C30);
                }
                if (accountManagementInfoDto.getDay60DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C60);
                }
                if (accountManagementInfoDto.getDay90DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C90);
                }
                if (accountManagementInfoDto.getDay120DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C120);
                }
                if (accountManagementInfoDto.getDay150DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C150);
                }
                if (accountManagementInfoDto.getDay180DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C180);
                }
                if (accountManagementInfoDto.getDay210DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C210);
                }
                if (accountManagementInfoDto.getDay240DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C240);
                }
                if (accountManagementInfoDto.getDay270DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C270);
                }
                if (accountManagementInfoDto.getDay300DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C300);
                }
                if (accountManagementInfoDto.getDay330DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C330);
                }
                if (accountManagementInfoDto.getDay360DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C360);
                }
//                if (accountManagementInfoDto.getDay390DueAmount().compareTo(BigDecimal.ZERO)!=0) {
//                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C390);
//                }
            }
            if (cycleDue.equals(Short.parseShort("3")) && oldAccountManageInfo.getDay30DueAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (accountManagementInfoDto.getCurrentDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CML0);
                }
                if (accountManagementInfoDto.getPastDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CXL0);
                }
                if (accountManagementInfoDto.getDay30DueAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C30_LE0);
                }
                if (accountManagementInfoDto.getDay60DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C60);
                }
                if (accountManagementInfoDto.getDay90DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C90);
                }
                if (accountManagementInfoDto.getDay120DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C120);
                }
                if (accountManagementInfoDto.getDay150DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C150);
                }
                if (accountManagementInfoDto.getDay180DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C180);
                }
                if (accountManagementInfoDto.getDay210DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C210);
                }
                if (accountManagementInfoDto.getDay240DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C240);
                }
                if (accountManagementInfoDto.getDay270DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C270);
                }
                if (accountManagementInfoDto.getDay300DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C300);
                }
                if (accountManagementInfoDto.getDay330DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C330);
                }
                if (accountManagementInfoDto.getDay360DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C360);
                }
//                if (accountManagementInfoDto.getDay390DueAmount().compareTo(BigDecimal.ZERO)!=0) {
//                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C390);
//                }
            }
            if (cycleDue.equals(Short.parseShort("4")) && oldAccountManageInfo.getDay60DueAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (accountManagementInfoDto.getCurrentDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CML0);
                }
                if (accountManagementInfoDto.getPastDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CXL0);
                }
                if (accountManagementInfoDto.getDay30DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C30L0);
                }
                if (accountManagementInfoDto.getDay60DueAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C60_LE0);
                }
                if (accountManagementInfoDto.getDay90DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C90);
                }
                if (accountManagementInfoDto.getDay120DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C120);
                }
                if (accountManagementInfoDto.getDay150DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C150);
                }
                if (accountManagementInfoDto.getDay180DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C180);
                }
                if (accountManagementInfoDto.getDay210DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C210);
                }
                if (accountManagementInfoDto.getDay240DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C240);
                }
                if (accountManagementInfoDto.getDay270DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C270);
                }
                if (accountManagementInfoDto.getDay300DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C300);
                }
                if (accountManagementInfoDto.getDay330DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C330);
                }
                if (accountManagementInfoDto.getDay360DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C360);
                }
//                if (accountManagementInfoDto.getDay390DueAmount().compareTo(BigDecimal.ZERO)!=0) {
//                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C390);
//                }
            }
            if (cycleDue.equals(Short.parseShort("5")) && oldAccountManageInfo.getDay90DueAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (accountManagementInfoDto.getCurrentDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CML0);
                }
                if (accountManagementInfoDto.getPastDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CXL0);
                }
                if (accountManagementInfoDto.getDay30DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C30L0);
                }
                if (accountManagementInfoDto.getDay60DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C60L0);
                }
                if (accountManagementInfoDto.getDay90DueAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C90_LE0);
                }
                if (accountManagementInfoDto.getDay120DueAmount().compareTo(BigDecimal.ZERO)!= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C120);
                }
                if (accountManagementInfoDto.getDay150DueAmount().compareTo(BigDecimal.ZERO)!= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C150);
                }
                if (accountManagementInfoDto.getDay180DueAmount().compareTo(BigDecimal.ZERO)!= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C180);
                }
                if (accountManagementInfoDto.getDay210DueAmount().compareTo(BigDecimal.ZERO)!= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C210);
                }
                if (accountManagementInfoDto.getDay240DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C240);
                }
                if (accountManagementInfoDto.getDay270DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C270);
                }
                if (accountManagementInfoDto.getDay300DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C300);
                }
                if (accountManagementInfoDto.getDay330DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C330);
                }
                if (accountManagementInfoDto.getDay360DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C360);
                }
//                if (accountManagementInfoDto.getDay390DueAmount().compareTo(BigDecimal.ZERO)!=0) {
//                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C390);
//                }
            }
            if (cycleDue.equals(Short.parseShort("6")) && oldAccountManageInfo.getDay120DueAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (accountManagementInfoDto.getCurrentDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CML0);
                }
                if (accountManagementInfoDto.getPastDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CXL0);
                }
                if (accountManagementInfoDto.getDay30DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C30L0);
                }
                if (accountManagementInfoDto.getDay60DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C60L0);
                }
                if (accountManagementInfoDto.getDay90DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C90L0);
                }
                if (accountManagementInfoDto.getDay120DueAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C120_LE0);
                }
                if (accountManagementInfoDto.getDay150DueAmount().compareTo(BigDecimal.ZERO)!= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C150);
                }
                if (accountManagementInfoDto.getDay180DueAmount().compareTo(BigDecimal.ZERO)!= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C180);
                }
                if (accountManagementInfoDto.getDay210DueAmount().compareTo(BigDecimal.ZERO)!= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C210);
                }
                if (accountManagementInfoDto.getDay240DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C240);
                }
                if (accountManagementInfoDto.getDay270DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C270);
                }
                if (accountManagementInfoDto.getDay300DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C300);
                }
                if (accountManagementInfoDto.getDay330DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C330);
                }
                if (accountManagementInfoDto.getDay360DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C360);
                }
//                if (accountManagementInfoDto.getDay390DueAmount().compareTo(BigDecimal.ZERO)!=0) {
//                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C390);
//                }
            }
            if (cycleDue.equals(Short.parseShort("7")) && oldAccountManageInfo.getDay150DueAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (accountManagementInfoDto.getCurrentDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CML0);
                }
                if (accountManagementInfoDto.getPastDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CXL0);
                }
                if (accountManagementInfoDto.getDay30DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C30L0);
                }
                if (accountManagementInfoDto.getDay60DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C60L0);
                }
                if (accountManagementInfoDto.getDay90DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C90L0);
                }
                if (accountManagementInfoDto.getDay120DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C120L0);
                }
                if (accountManagementInfoDto.getDay150DueAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C150_LE0);
                }
                if (accountManagementInfoDto.getDay180DueAmount().compareTo(BigDecimal.ZERO)!= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C180);
                }
                if (accountManagementInfoDto.getDay210DueAmount().compareTo(BigDecimal.ZERO)!= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C210);
                }
                if (accountManagementInfoDto.getDay240DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C240);
                }
                if (accountManagementInfoDto.getDay270DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C270);
                }
                if (accountManagementInfoDto.getDay300DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C300);
                }
                if (accountManagementInfoDto.getDay330DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C330);
                }
                if (accountManagementInfoDto.getDay360DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C360);
                }
//                if (accountManagementInfoDto.getDay390DueAmount().compareTo(BigDecimal.ZERO)!=0) {
//                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C390);
//                }
            }
            if (cycleDue.equals(Short.parseShort("8")) && oldAccountManageInfo.getDay180DueAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (accountManagementInfoDto.getCurrentDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CML0);
                }
                if (accountManagementInfoDto.getPastDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CXL0);
                }
                if (accountManagementInfoDto.getDay30DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C30L0);
                }
                if (accountManagementInfoDto.getDay60DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C60L0);
                }
                if (accountManagementInfoDto.getDay90DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C90L0);
                }
                if (accountManagementInfoDto.getDay120DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C120L0);
                }
                if (accountManagementInfoDto.getDay150DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C150L0);
                }
                if (accountManagementInfoDto.getDay180DueAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C180_LE0);
                }
                if (accountManagementInfoDto.getDay210DueAmount().compareTo(BigDecimal.ZERO)!= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C210);
                }
                if (accountManagementInfoDto.getDay240DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C240);
                }
                if (accountManagementInfoDto.getDay270DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C270);
                }
                if (accountManagementInfoDto.getDay300DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C300);
                }
                if (accountManagementInfoDto.getDay330DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C330);
                }
                if (accountManagementInfoDto.getDay360DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C360);
                }
//                if (accountManagementInfoDto.getDay390DueAmount().compareTo(BigDecimal.ZERO)!=0) {
//                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C390);
//                }
            }
            if (cycleDue.equals(Short.parseShort("9")) && oldAccountManageInfo.getDay210DueAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (accountManagementInfoDto.getCurrentDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CML0);
                }
                if (accountManagementInfoDto.getPastDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CXL0);
                }
                if (accountManagementInfoDto.getDay30DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C30L0);
                }
                if (accountManagementInfoDto.getDay60DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C60L0);
                }
                if (accountManagementInfoDto.getDay90DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C90L0);
                }
                if (accountManagementInfoDto.getDay120DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C120L0);
                }
                if (accountManagementInfoDto.getDay150DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C150L0);
                }
                if (accountManagementInfoDto.getDay180DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C180L0);
                }
                if (accountManagementInfoDto.getDay210DueAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C210_LE0);
                }
                if (accountManagementInfoDto.getDay240DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C240);
                }
                if (accountManagementInfoDto.getDay270DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C270);
                }
                if (accountManagementInfoDto.getDay300DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C300);
                }
                if (accountManagementInfoDto.getDay330DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C330);
                }
                if (accountManagementInfoDto.getDay360DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C360);
                }
//                if (accountManagementInfoDto.getDay390DueAmount().compareTo(BigDecimal.ZERO)!=0) {
//                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C390);
//                }
            }
            if (cycleDue.equals(Short.parseShort("10")) && oldAccountManageInfo.getDay240DueAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (accountManagementInfoDto.getCurrentDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CML0);
                }
                if (accountManagementInfoDto.getPastDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CXL0);
                }
                if (accountManagementInfoDto.getDay30DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C30L0);
                }
                if (accountManagementInfoDto.getDay60DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C60L0);
                }
                if (accountManagementInfoDto.getDay90DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C90L0);
                }
                if (accountManagementInfoDto.getDay120DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C120L0);
                }
                if (accountManagementInfoDto.getDay150DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C150L0);
                }
                if (accountManagementInfoDto.getDay180DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C180L0);
                }
                if (accountManagementInfoDto.getDay210DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C210);
                }
                if (accountManagementInfoDto.getDay240DueAmount().compareTo(BigDecimal.ZERO)<=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C240_LE0);
                }
                if (accountManagementInfoDto.getDay270DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C270);
                }
                if (accountManagementInfoDto.getDay300DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C300);
                }
                if (accountManagementInfoDto.getDay330DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C330);
                }
                if (accountManagementInfoDto.getDay360DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C360);
                }
//                if (accountManagementInfoDto.getDay390DueAmount().compareTo(BigDecimal.ZERO)!=0) {
//                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C390);
//                }
            }
            if (cycleDue.equals(Short.parseShort("11")) && oldAccountManageInfo.getDay270DueAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (accountManagementInfoDto.getCurrentDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CML0);
                }
                if (accountManagementInfoDto.getPastDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CXL0);
                }
                if (accountManagementInfoDto.getDay30DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C30L0);
                }
                if (accountManagementInfoDto.getDay60DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C60L0);
                }
                if (accountManagementInfoDto.getDay90DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C90L0);
                }
                if (accountManagementInfoDto.getDay120DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C120L0);
                }
                if (accountManagementInfoDto.getDay150DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C150L0);
                }
                if (accountManagementInfoDto.getDay180DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C180L0);
                }
                if (accountManagementInfoDto.getDay210DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C210);
                }
                if (accountManagementInfoDto.getDay240DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C240);
                }
                if (accountManagementInfoDto.getDay270DueAmount().compareTo(BigDecimal.ZERO)<=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C270_LE0);
                }
                if (accountManagementInfoDto.getDay300DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C300);
                }
                if (accountManagementInfoDto.getDay330DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C330);
                }
                if (accountManagementInfoDto.getDay360DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C360);
                }
//                if (accountManagementInfoDto.getDay390DueAmount().compareTo(BigDecimal.ZERO)!=0) {
//                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C390);
//                }
            }
            if (cycleDue.equals(Short.parseShort("12")) && oldAccountManageInfo.getDay300DueAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (accountManagementInfoDto.getCurrentDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CML0);
                }
                if (accountManagementInfoDto.getPastDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CXL0);
                }
                if (accountManagementInfoDto.getDay30DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C30L0);
                }
                if (accountManagementInfoDto.getDay60DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C60L0);
                }
                if (accountManagementInfoDto.getDay90DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C90L0);
                }
                if (accountManagementInfoDto.getDay120DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C120L0);
                }
                if (accountManagementInfoDto.getDay150DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C150L0);
                }
                if (accountManagementInfoDto.getDay180DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C180L0);
                }
                if (accountManagementInfoDto.getDay210DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C210);
                }
                if (accountManagementInfoDto.getDay240DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C240);
                }
                if (accountManagementInfoDto.getDay270DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C270);
                }
                if (accountManagementInfoDto.getDay300DueAmount().compareTo(BigDecimal.ZERO)<=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C300_LE0);
                }
                if (accountManagementInfoDto.getDay330DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C330);
                }
                if (accountManagementInfoDto.getDay360DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C360);
                }
//                if (accountManagementInfoDto.getDay390DueAmount().compareTo(BigDecimal.ZERO)!=0) {
//                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C390);
//                }
            }
            if (cycleDue.equals(Short.parseShort("13")) && oldAccountManageInfo.getDay330DueAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (accountManagementInfoDto.getCurrentDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CML0);
                }
                if (accountManagementInfoDto.getPastDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CXL0);
                }
                if (accountManagementInfoDto.getDay30DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C30L0);
                }
                if (accountManagementInfoDto.getDay60DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C60L0);
                }
                if (accountManagementInfoDto.getDay90DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C90L0);
                }
                if (accountManagementInfoDto.getDay120DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C120L0);
                }
                if (accountManagementInfoDto.getDay150DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C150L0);
                }
                if (accountManagementInfoDto.getDay180DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C180L0);
                }
                if (accountManagementInfoDto.getDay210DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C210);
                }
                if (accountManagementInfoDto.getDay240DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C240);
                }
                if (accountManagementInfoDto.getDay270DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C270);
                }
                if (accountManagementInfoDto.getDay300DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C300);
                }
                if (accountManagementInfoDto.getDay330DueAmount().compareTo(BigDecimal.ZERO)<=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C330_LE0);
                }
                if (accountManagementInfoDto.getDay360DueAmount().compareTo(BigDecimal.ZERO)!=0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C360);
                }
//                if (accountManagementInfoDto.getDay390DueAmount().compareTo(BigDecimal.ZERO)!=0) {
//                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C390);
//                }
            }
            if (cycleDue.equals(Short.parseShort("14")) && oldAccountManageInfo.getDay360DueAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (accountManagementInfoDto.getCurrentDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CML0);
                }
                if (accountManagementInfoDto.getPastDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CXL0);
                }
                if (accountManagementInfoDto.getDay30DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C30L0);
                }
                if (accountManagementInfoDto.getDay60DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C60L0);
                }
                if (accountManagementInfoDto.getDay90DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C90L0);
                }
                if (accountManagementInfoDto.getDay120DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C120L0);
                }
                if (accountManagementInfoDto.getDay150DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C150L0);
                }
                if (accountManagementInfoDto.getDay180DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C180L0);
                }
                if (accountManagementInfoDto.getDay210DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C210);
                }
                if (accountManagementInfoDto.getDay240DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C240);
                }
                if (accountManagementInfoDto.getDay270DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C270);
                }
                if (accountManagementInfoDto.getDay300DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C300);
                }
                if (accountManagementInfoDto.getDay330DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C330);
                }
                if (accountManagementInfoDto.getDay360DueAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C360_LE0);
                }
//                if (accountManagementInfoDto.getDay390DueAmount().compareTo(BigDecimal.ZERO)!=0) {
//                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C390);
//                }
            }

            //延滞信息校验
//            if (oldAccountManageInfo!=null&&(oldAccountManageInfo.getDay390DueAmount().compareTo(BigDecimal.ZERO) > 0 && accountManagementInfoDto.getDay390DueAmount().compareTo(BigDecimal.ZERO) <= 0)) {
//                log.error("390天金额：如果修改前的值 > 0，则修改后的值不可小于等于0");
//                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C390_LE0);
//            }
            if (accountManagementInfoDto.getCurrentDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                logger.error("Current minimum payment amount cannot be less than 0 after modification: amount={}", accountManagementInfoDto.getCurrentDueAmount());
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.RX_L0);
            }
            if (accountManagementInfoDto.getPastDueAmount().compareTo(BigDecimal.ZERO) < 0) {
                logger.error("Past due amount cannot be less than 0 after modification: amount={}", accountManagementInfoDto.getPastDueAmount());
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.CXL0);
            }
            if (accountManagementInfoDto.getDay30DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                logger.error("30-day due amount cannot be less than 0 after modification: amount={}", accountManagementInfoDto.getDay30DueAmount());
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C30L0);
            }
            if (accountManagementInfoDto.getDay60DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                logger.error("60-day due amount cannot be less than 0 after modification: amount={}", accountManagementInfoDto.getDay60DueAmount());
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C60L0);
            }
            if (accountManagementInfoDto.getDay90DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                logger.error("90-day due amount cannot be less than 0 after modification: amount={}", accountManagementInfoDto.getDay90DueAmount());
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C90L0);
            }
            if (accountManagementInfoDto.getDay120DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                logger.error("120-day due amount cannot be less than 0 after modification: amount={}", accountManagementInfoDto.getDay120DueAmount());
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C120L0);
            }
            if (accountManagementInfoDto.getDay150DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                logger.error("150-day due amount cannot be less than 0 after modification: amount={}", accountManagementInfoDto.getDay150DueAmount());
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C150L0);
            }
            if (accountManagementInfoDto.getDay180DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                logger.error("180-day due amount cannot be less than 0 after modification: amount={}", accountManagementInfoDto.getDay180DueAmount());
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C180L0);
            }
            if (accountManagementInfoDto.getDay210DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                logger.error("210-day due amount cannot be less than 0 after modification: amount={}", accountManagementInfoDto.getDay210DueAmount());
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C210L0);
            }
            if (accountManagementInfoDto.getDay240DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                logger.error("240-day due amount cannot be less than 0 after modification: amount={}", accountManagementInfoDto.getDay240DueAmount());
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C240L0);
            }
            if (accountManagementInfoDto.getDay270DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                logger.error("270-day due amount cannot be less than 0 after modification: amount={}", accountManagementInfoDto.getDay270DueAmount());
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C270L0);
            }
            if (accountManagementInfoDto.getDay300DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                logger.error("300-day due amount cannot be less than 0 after modification: amount={}", accountManagementInfoDto.getDay300DueAmount());
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C300L0);
            }
            if (accountManagementInfoDto.getDay330DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                logger.error("330-day due amount cannot be less than 0 after modification: amount={}", accountManagementInfoDto.getDay330DueAmount());
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C330L0);
            }
            if (accountManagementInfoDto.getDay360DueAmount().compareTo(BigDecimal.ZERO) < 0) {
                logger.error("360-day due amount cannot be less than 0 after modification: amount={}", accountManagementInfoDto.getDay360DueAmount());
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.C360L0);
            }
            //更新延滞信息
            accountManagementInfo.setCurrentDueAmount(accountManagementInfoDto.getCurrentDueAmount());
            accountManagementInfo.setPastDueAmount(accountManagementInfoDto.getPastDueAmount());
            accountManagementInfo.setDay30DueAmount(accountManagementInfoDto.getDay30DueAmount());
            accountManagementInfo.setDay60DueAmount(accountManagementInfoDto.getDay60DueAmount());
            accountManagementInfo.setDay90DueAmount(accountManagementInfoDto.getDay90DueAmount());
            accountManagementInfo.setDay120DueAmount(accountManagementInfoDto.getDay120DueAmount());
            accountManagementInfo.setDay150DueAmount(accountManagementInfoDto.getDay150DueAmount());
            accountManagementInfo.setDay180DueAmount(accountManagementInfoDto.getDay180DueAmount());
            accountManagementInfo.setDay210DueAmount(accountManagementInfoDto.getDay210DueAmount());
            accountManagementInfo.setDay240DueAmount(accountManagementInfoDto.getDay240DueAmount());
            accountManagementInfo.setDay270DueAmount(accountManagementInfoDto.getDay270DueAmount());
            accountManagementInfo.setDay300DueAmount(accountManagementInfoDto.getDay300DueAmount());
            accountManagementInfo.setDay330DueAmount(accountManagementInfoDto.getDay330DueAmount());
            accountManagementInfo.setDay360DueAmount(accountManagementInfoDto.getDay360DueAmount());
//            accountManagementInfo.setDay390DueAmount(accountManagementInfoDto.getDay390DueAmount());

            //账户管理信息表中的最小还款额（TOTAL_DUE_AMOUNT)计算
            BigDecimal totalDueAmount = accountManagementInfoDto.getCurrentDueAmount().add(accountManagementInfoDto.getPastDueAmount())
                    .add(accountManagementInfoDto.getDay30DueAmount())
                    .add(accountManagementInfoDto.getDay60DueAmount())
                    .add(accountManagementInfoDto.getDay90DueAmount())
                    .add(accountManagementInfoDto.getDay120DueAmount())
                    .add(accountManagementInfoDto.getDay150DueAmount())
                    .add(accountManagementInfoDto.getDay180DueAmount())
                    .add(accountManagementInfoDto.getDay210DueAmount())
                    .add(accountManagementInfoDto.getDay240DueAmount())
                    .add(accountManagementInfoDto.getDay270DueAmount())
                    .add(accountManagementInfoDto.getDay300DueAmount())
                    .add(accountManagementInfoDto.getDay330DueAmount())
                    .add(accountManagementInfoDto.getDay360DueAmount());

            accountManagementInfo.setTotalDueAmount(totalDueAmount);
            //交易类型00000
            String transactionTypeCode = "00000";
            AccountStatisticsInfo info = accountStatisticsInfoSelfMapper.selectByIdAndTypeAndOrgan(accountManagementInfo.getAccountManagementId(), transactionTypeCode, accountManagementInfo.getOrganizationNumber());
            if (null == info) {
                logger.error("Account statistics info not found: accountManagementId={}, transactionTypeCode={}", accountManagementInfo.getAccountManagementId(), transactionTypeCode);
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.ST_E);
            }
            if (totalDueAmount.compareTo(info.getBalance()) > 0 && info.getBalance().compareTo(BigDecimal.ZERO) > 0
                    && totalDueAmount.compareTo(info.getBalance()) > 0) {
                logger.error("Total due amount exceeds account balance: totalDueAmount={}, balance={}", totalDueAmount, info.getBalance());
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_COL_VALUE_ILLEGAL, AccountingRepDetailEnum.AF_AD);
            }


            //添加业务日志
            MaintenanceLogDTO maintenanceLog = new MaintenanceLogDTO();
            maintenanceLog.setOperationTimestamp(LocalDateTime.now());
            maintenanceLog.setPrimaryKeyValue(accountManagementInfo.getAccountManagementId());
            maintenanceLog.setOperationType(MaintenanceConstant.OPERATION_U);
            maintenanceLog.setTransactionDataType(MaintenanceConstant.DATA_A);
            maintenanceLog.setOperatorId(LoginUserUtils.getLoginUserName());
            logger.info("Calling maintenanceLogService.add: primaryKeyValue={}", accountManagementInfo.getAccountManagementId());
            maintenanceLogService.add(maintenanceLog,accountManagementInfo,oldAccountManageInfo, MaintenanceConstant.ACCOUNT_MANAGEMENT_INFO);
            logger.info("Called maintenanceLogService.add successfully");

            chargeOffInfoService.financeStatusChangeByBlockCode(accountManagementInfo,oldAccountManageInfo);

            int result = accountManagementInfoMapper.updateByPrimaryKeySelective(accountManagementInfo);
            //判断是否生成结转gl和glams
            if (result== 1){
                chargeOffInfoService.absGlGenerator(accountManagementInfo,oldAccountManageInfo);
            }
            if (result == 0) {
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_UPDATE_FAIL);
            }

            //超长免息期追加，更新所有交易账户中的实际出账单日、实际免息结息日
            modifyAccountBalanceCycleDay(accountManagementInfo.getAccountManagementId(), accountManagementInfo.getOrganizationNumber(), accountManagementInfoDto.getCycleDay());
            // 更新客户对账控制表中的乐观锁计数字段 2020-05-13 zcli
            if (null != controlDTO) {
                logger.info("Calling custReconciliationControlService.commitLock: customerId={}", controlDTO.getCustomerId());
                custReconciliationControlService.commitLock(controlDTO);
                logger.info("Called custReconciliationControlService.commitLock successfully");
            }

/*            if(flag){

                List<CardAuthorizationInfo> cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByOrgNumberAndCustomerId("0001", oldAccountManageInfo.getCustomerId());

                CustomerAuthorizationInfo customerAuthorizationInfo = customerAuthorizationInfoSelfMapper.selectByCustomerId(oldAccountManageInfo.getCustomerId());

                LocalDate blockCodeSetDate = custReconciliationControlService.getBillingDate(controlDTO,orgConfig.getAccruedThruDay(),orgConfig.getToday(),orgConfig.getNextProcessingDay());

                String blckCode = null;
                String blckDate = null;
                String badCode = null;
                DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                if (null != accountManagementInfoDto.getBlockCode() && !"".equals(accountManagementInfoDto.getBlockCode())) {
                    blckCode = accountManagementInfoDto.getBlockCode();
                    blckDate = blockCodeSetDate.format(fmt);
                    badCode = "1";

                }else{
                    badCode = "0";
                }
                String s = pushSyncPointEcIf(accountManagementInfoDto.getCustomerId(), customerAuthorizationInfo.getIdNumber(),customerAuthorizationInfo.getIdType(), customerAuthorizationInfo.getIdExpireDate().format(fmt), customerAuthorizationInfo.getChineseName());
                logger.info("Called sync points Ecif data service: response={}", s);
                String s1 = pushSyncCustCard(cardAuthorizationInfos.get(0).getCardNumber(), accountManagementInfoDto.getAccountStatus(), accountManagementInfoDto.getCustomerId(), accountManagementInfoDto.getProductNumber().substring(2, 6),blckCode,blckDate);
                logger.info("Called sync user card data service: response={}", s1);
                String s2 = pushSyncPtsCust(accountManagementInfoDto.getCustomerId(), customerBasicInfo.getBirthDate().format(fmt), String.valueOf(accountManagementInfoDto.getCycleDay()), customerAuthorizationInfo.getMobilePhone(), customerAuthorizationInfo.getIdNumber(),customerAuthorizationInfo.getIdType(), customerAuthorizationInfo.getIdExpireDate().format(fmt), customerAuthorizationInfo.getChineseName(),badCode);
                logger.info("Called sync points customer data service: response={}", s2);
            }*/
            //TODO:封锁码联动操作逻辑
            if (!"C".equals(accountManagementInfo.getLiability())){
                doBlockHandle(accountManagementInfoDto, accountManagementInfo, orgConfig, oldAccountManageInfo);
            }
        }
    }

/*    public String pushSyncPointEcIf(String customerId, String appIdNumber, String appIdType, String appIdExpireDate, String name) {
        String jsonString = JSON.toJSONString(organizePointEcIf(customerId, appIdNumber, appIdType, appIdExpireDate, name));
        return postSisRequest(PtsEcifUri, "/sync/syncPtsEcif", jsonString);
    }

    public String pushSyncPtsCust(String customerId, String birth, String billDay, String mobilePhone, String appIdNumber, String appIdType, String appIdExpireDate, String name,String badCode) {
        String jsonString = JSON.toJSONString(organizePointCust(customerId, birth, billDay, mobilePhone, appIdNumber, appIdType, appIdExpireDate, name,badCode));
        return postSisRequest(PtsCustUri, "/sync/syncPtsCust", jsonString);
    }

    public String pushSyncCustCard(String cardNo, String status, String customerId, String productNumber,String blckCode,String blckDate) {
        String jsonString = JSON.toJSONString(organizeCustCard(cardNo, status, customerId, productNumber,blckCode,blckDate));
        return postSisRequest(CustCardUri, "/sync/syncCustCard", jsonString);
    }*/

    public String postSisRequest(String uri, String path, String content) {
        String result = "";
        try {
            result = HttpClientUtils.post(uri + path, content);
        } catch (IOException e) {
            logger.error(e.getMessage());
        }

        return result;
    }

    //TODO POC使用
  /*  public CustCard organizeCustCard(String cardNo, String status, String customerId, String productNumber,String blckCode,String blckDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String now = formatter.format(java.sql.Date.from(LocalDate.now().atStartOfDay(ZoneOffset.ofHours(8)).toInstant()));
        CustCard custCard = new CustCard();
        custCard.setTenantId(1);
        custCard.setCardNo(cardNo);
        custCard.setBlckCode(blckCode);
        custCard.setCardStts(status);
        custCard.setBlckDate(blckDate);
        custCard.setActvDate(now);
        custCard.setCustNo(customerId);
        custCard.setEcifNo(customerId);
        custCard.setCustOrg(OrgNumberUtils.getOrg());
        custCard.setRltnShip("P");
        custCard.setProdLvl3(productNumber);
        custCard.setBlckRsn("B");
        custCard.setGiftCode("1");
        custCard.setFrmtId("1");
        custCard.setMarkCode("111");
        custCard.setCardLevel("1");
        return custCard;
    }

    public PointCust organizePointCust(String customerId, String birth, String billDay, String mobilePhone, String appIdNumber, String appIdType, String appIdExpireDate, String name,String badCode) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String now = formatter.format(java.sql.Date.from(LocalDate.now().atStartOfDay(ZoneOffset.ofHours(8)).toInstant()));
        LimitCustCreditInfo limitCustCreditInfo = custCreditInfoMapper.selectByCustomerIdAndLimitTypeCode(customerId, "MA01", OrgNumberUtils.getOrg());
        PointCust pointCust = new PointCust();
        pointCust.setTenantId(1);
        pointCust.setCustNo(customerId);
        pointCust.setCustOrg(OrgNumberUtils.getOrg());
        pointCust.setBrthDate(birth);
        pointCust.setBillDay(billDay);
        pointCust.setPhoneNo(mobilePhone);
        pointCust.setCertNo2(appIdNumber);
        pointCust.setCertType2(appIdType);
        pointCust.setCertExpDate2(appIdExpireDate);
        pointCust.setBadCode(badCode);
        pointCust.setVipCode("Y");
        pointCust.setEcifNo(customerId);
        pointCust.setStarLvl("5");
        pointCust.setLastBillCycle(now);
        pointCust.setCertName2(name);
        pointCust.setCrlimit(limitCustCreditInfo.getFixLimitAmount());
        return pointCust;
    }

    public PointEcIf organizePointEcIf(String customerId, String appIdNumber, String appIdType, String appIdExpireDate, String name) {
        PointEcIf pointEcIf = new PointEcIf();
        pointEcIf.setTenantId(1);
        pointEcIf.setEcifNo(customerId);
        pointEcIf.setCertNo1(appIdNumber);
        pointEcIf.setCertType1(appIdType);
        pointEcIf.setCertExpDate1(appIdExpireDate);
        pointEcIf.setCertName1(name);
        return pointEcIf;
    }*/

    @Override
    public CustomerAuthorizationInfoDTO findCustomerByAccountManagementId(String accountManagementId) {
        AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(accountManagementId);
        if (accountManagementInfo == null) {
            logger.error("No data found by account number: accountManagementId={}", accountManagementId);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.NO_ACC);
        }
        CustomerAuthorizationInfo customerAuthorizationInfo = customerAuthorizationInfoSelfMapper.selectByOrgNumberAndCustomerId(accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId());
        if (customerAuthorizationInfo == null) {
            logger.error("No customer authorization data found by account number: accountManagementId={}", accountManagementId);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.NO_ACC);
        }
        CustomerAuthorizationInfoDTO customerAuthorizationInfoDTO = BeanMapping.copy(customerAuthorizationInfo, CustomerAuthorizationInfoDTO.class);
        return customerAuthorizationInfoDTO;
    }


    @Override
    public List<AccountManagementInfoDTO> findsListByCustomerId(String customerId, String relationshipIndicator, String cardNumber) {
        logger.info("Query all supplementary card management account info by customer ID: customerId={}", customerId);
        //todo 主附卡标志常量设置为过期了 暂时先写死
        if (!"S".equals(relationshipIndicator)) {
            logger.error("Card number main/supplementary indicator is invalid: cardNumber={}, relationshipIndicator={}", cardNumber, relationshipIndicator);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_ERR, AccountingRepDetailEnum.SE_ATT);
        }
        List<AccountManagementInfo> accountManagementInfos =
                accountManagementInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(),customerId);
        return BeanMapping.copyList(accountManagementInfos, AccountManagementInfoDTO.class);
    }

    @Override
    public List<AccountManagementInfoDTO> findByOrgNumAndCorpCusId(String organizationNumber, String corporateCustomerId) {
        logger.info("Query account management info by organization number and corporate customer ID: orgNumber={}, corpCustomerId={}", organizationNumber, corporateCustomerId);
        List<AccountManagementInfo> accountManagementInfos = accountManagementInfoSelfMapper.selectByOrgAndCorpCusId(organizationNumber, corporateCustomerId);
        return BeanMapping.copyList(accountManagementInfos, AccountManagementInfoDTO.class);
    }

    @Override
    public AccountLimitInfoDTO getAccountLimitInfoByCardNumber(String orgNumber, String cardNumber) {
        if (StringUtils.isBlank(cardNumber)) {
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL, AccountingRepDetailEnum.CE);
        }
        logger.info("Query account management info by organization number and card number: orgNumber={}, cardNumber={}", orgNumber, cardNumber);
        //获取卡对应的产品编号
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(cardNumber, orgNumber);
        if (Objects.isNull(cardAuthorizationInfo)) {
            logger.error("Failed to query card authorization info by organization number and card number: orgNumber={}, cardNumber={}", orgNumber, cardNumber);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.CR_AU);
        }
        //根据产品编号查询卡产品信息
        CardProductInfoResDTO cardProductInfoResDTO = cardProductInfoService.findByOrgAndProductNum(orgNumber, cardAuthorizationInfo.getProductNumber());
        String accountCustomerId = LiabilityEnum.CORPORATE.getCode().equals(cardAuthorizationInfo.getLiability()) ? cardAuthorizationInfo.getCorporateCustomerId() : cardAuthorizationInfo.getPrimaryCustomerId();
        //根据卡产品信息查询账户管理信息
        List<AccountManagementInfo> accountManagementInfoList = accountManagementInfoSelfMapper.selectByCustomerId(orgNumber, accountCustomerId);
        AccountLimitInfoDTO accountLimitInfoDTO = new AccountLimitInfoDTO();
        AccountManagementInfo accountManagementInfo;
        if (CollectionUtils.isNotEmpty(accountManagementInfoList)) {
            //比较产品编号，找到对应的卡产品的账户管理信息
            List<AccountManagementInfo> subManagementInfoList = accountManagementInfoList.stream()
                    .filter(a -> StringUtils.isNotBlank(cardProductInfoResDTO.getAccountProductNumber())
                                    && Objects.equals(cardProductInfoResDTO.getAccountProductNumber(), a.getProductNumber()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(subManagementInfoList)) {
                accountManagementInfo = subManagementInfoList.get(0);
                //由于一期不考虑多币种卡，所以只会有一个匹配
                accountLimitInfoDTO.setCycleDay(accountManagementInfo.getCycleDay());
                accountLimitInfoDTO.setCurrentBonusPoints(accountManagementInfo.getLastStmtBonusPoints());
                accountLimitInfoDTO.setCurrency(accountManagementInfo.getCurrency());
                List<AccountStatementInfo> asiList=accountStatementInfoSelfMapper.selectByAccountManagementId(accountManagementInfo.getAccountManagementId());
                if (CollectionUtils.isNotEmpty(asiList)){
                    LocalDate paymentDueDate=asiList.stream().max(Comparator.comparing(AccountStatementInfo::getPaymentDueDate)).get().getPaymentDueDate();
                    accountLimitInfoDTO.setPaymentDueDate(paymentDueDate);
                }
            } else {
                logger.error("Failed to query account management info by organization number and primary customer ID: orgNumber={}, primaryCustomerId={}", orgNumber, cardAuthorizationInfo.getPrimaryCustomerId());
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.CR_AU);
            }
        } else {
            logger.error("Failed to query account management info by organization number and primary customer ID: orgNumber={}, primaryCustomerId={}", orgNumber, cardAuthorizationInfo.getPrimaryCustomerId());
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.CR_AU);
        }
        //根据卡号查询额度信息
        List<CustomerLimitInfoDTO> customerLimitTotalForAllCurrency = customerLimitInfoService.getCustomerLimitList(
                accountCustomerId, orgNumber);
        //过滤币种
        List<CustomerLimitInfoDTO> customerLimitTotal = customerLimitTotalForAllCurrency.stream().filter(c -> c.getLimitTypeCurrency().equals(accountManagementInfo.getCurrency())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(customerLimitTotal)) {
            //查询SC99类型的额度
            List<CustomerLimitInfoDTO> customerLimitInfoDTOList = customerLimitTotal.stream()
                    .filter(c -> StringUtils.equals(Constants.LIMIT_TYPE_SC99, c.getLimitTypeCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(customerLimitInfoDTOList)) {
                logger.error("Card lacks SC99 type limit information: primaryCustomerId={}, cardNumber={}", cardAuthorizationInfo.getPrimaryCustomerId(), cardNumber);
                return accountLimitInfoDTO;
            }
            BigDecimal customerLimitAvailableAmount = customerLimitInfoDTOList.get(0).getLimitAvailableAmount();
            //SC99扣除VA可用额度（VA总额度减去VA已用额度），如果扣除结果小于0则取0
            List<CustomerLimitInfoDTO> vaLimitInfoDTOList = customerLimitTotal.stream()
                    .filter(c -> StringUtils.equals(Constants.LIMIT_TYPE_SA00, c.getLimitTypeCode()) && StringUtils.equals(Constants.ACCOUNT_PRODUCT_VA, c.getAccountProductCode()))
                    .collect(Collectors.toList());
            //查询VA冻结额度，暂时用账产品代替卡产品
            List<CardVirtualAccountInfo> cardVirtualAccountInfos = cardVirtualAccountInfoSelfMapper.selectValidByCustomerIdAndCardProductNumber(cardAuthorizationInfo.getPrimaryCustomerId(), cardAuthorizationInfo.getProductNumber());
            CardVirtualAccountInfo cardVirtualAccountInfo = cardVirtualAccountInfos.stream()
                    .filter(c -> StringUtils.equals(c.getAccountType(), VaTaTypeEnum.VA.getCode()) && StringUtils.equals(c.getAccountType(), VaTaTypeEnum.VA.getCode()))
                    .findFirst().orElse(null);
            BigDecimal vaFrozenAmount = BigDecimal.ZERO;
            if (!Objects.isNull(cardVirtualAccountInfo)) {
                TokenVirtualDTO tokenVirtualDTO = new TokenVirtualDTO();
                tokenVirtualDTO.setSearchType("A");
                tokenVirtualDTO.setOrganizationNumber("101");
                tokenVirtualDTO.setSearchNumber(cardVirtualAccountInfo.getAccountManagementId());
                TokenVirtualDTO tokenVirtualDTORes = tokenVirtualInfoService.queryAcctInfo(tokenVirtualDTO);
                vaFrozenAmount = Objects.isNull(tokenVirtualDTORes) ? vaFrozenAmount : tokenVirtualDTORes.getFrozenAmount();
            }
            if (CollectionUtils.isNotEmpty(vaLimitInfoDTOList)) {
                customerLimitAvailableAmount = customerLimitAvailableAmount.subtract(vaLimitInfoDTOList.get(0).getLimitAvailableAmount().abs().subtract(vaFrozenAmount)).max(BigDecimal.ZERO);
            }
            //坤海的产品只有SC99的额度
            List<IFProductInfo> ifProductInfos = ifProductInfoMapper.selectAll();
            List<String> ifProductNumberList = ifProductInfos.stream().map(IFProductInfo::getProductNumber).collect(Collectors.toList());
            if (ifProductNumberList.contains(cardAuthorizationInfo.getProductNumber())) {
                accountLimitInfoDTO.setCreditLimit(customerLimitInfoDTOList.get(0).getLimitEffectAmount());
                accountLimitInfoDTO.setUsedLimit(customerLimitInfoDTOList.get(0).getLimitUsedAmount());
                accountLimitInfoDTO.setAvailableLimit(accountLimitInfoDTO.getCreditLimit().subtract(accountLimitInfoDTO.getUsedLimit()));
                return accountLimitInfoDTO;
            }
            //查询SC01类型的额度(公司卡使用SC04)
            List<CustomerLimitInfoDTO> subLimitInfoDTOList = customerLimitTotal.stream()
                    .filter(c -> (CorporateIndicatorEnum.CORPORATE.getCode().equals(cardAuthorizationInfo.getCorporateIndicator()) ? StringUtils.equals(Constants.LIMIT_TYPE_SC04, c.getLimitTypeCode()) : StringUtils.equals(Constants.LIMIT_TYPE_SC01, c.getLimitTypeCode()))
                            && StringUtils.equals(cardProductInfoResDTO.getAccountProductNumber(), c.getAccountProductCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(subLimitInfoDTOList)) {
                accountLimitInfoDTO.setCreditLimit(subLimitInfoDTOList.get(0).getLimitEffectAmount());
                accountLimitInfoDTO.setUsedLimit(subLimitInfoDTOList.get(0).getLimitUsedAmount());
                accountLimitInfoDTO.setAvailableLimit(accountLimitInfoDTO.getCreditLimit().subtract(accountLimitInfoDTO.getUsedLimit()));
                accountLimitInfoDTO.setAvailableLimit(accountLimitInfoDTO.getAvailableLimit().min(customerLimitAvailableAmount));
                //查询溢缴款并纳入计算，计算公式为账户层溢缴款+min(账户层可用，客户层可用)
                List<CustomerLimitInfoDTO> overPaymentLimitInfoDTOList = customerLimitTotal.stream()
                        .filter(c -> (CorporateIndicatorEnum.CORPORATE.getCode().equals(cardAuthorizationInfo.getCorporateIndicator()) ? StringUtils.equals(Constants.LIMIT_TYPE_SAC0, c.getLimitTypeCode()) : StringUtils.equals(Constants.LIMIT_TYPE_SA00, c.getLimitTypeCode()))
                                && StringUtils.equals(cardProductInfoResDTO.getAccountProductNumber(), c.getAccountProductCode()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(overPaymentLimitInfoDTOList)) {
                    accountLimitInfoDTO.setUsedLimit(accountLimitInfoDTO.getUsedLimit().add(overPaymentLimitInfoDTOList.get(0).getLimitUsedAmount()));
                    accountLimitInfoDTO.setAvailableLimit(accountLimitInfoDTO.getAvailableLimit().add(overPaymentLimitInfoDTOList.get(0).getLimitAvailableAmount()));
                }
            } else {
                logger.error("Card lacks account limit information: primaryCustomerId={}, cardNumber={}", cardAuthorizationInfo.getPrimaryCustomerId(), cardNumber);
            }
        } else {
            logger.error("Failed to query customer limit info by organization number and card number: orgNumber={}, cardNumber={}", orgNumber, cardNumber);
        }
        return accountLimitInfoDTO;
    }


    /**
     * 此处只校验临时自扣参数
     * @param accountManagementInfoDto
     * @param orgConfig
     */
    /*private void checkTempPaymentType(AccountManagementInfoDTO accountManagementInfoDto,OrganizationInfoResDTO orgConfig) {
        if(Objects.isNull(accountManagementInfoDto.getTempPaymentType())){
            //临时自扣类型默认0
            accountManagementInfoDto.setTempPaymentType("0");
        }
        //临时自扣操作日期,如果'临时自扣类型'不为0，则该字段为必选项，否则，为非必选项
        if(!"0".equals(accountManagementInfoDto.getTempPaymentType())){
            if(Objects.isNull(accountManagementInfoDto.getTempPaymentDate())){
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL, AccountingRepDetailEnum.EN_OP);
            }
            //根据机构号、管理账户id、上一账单日，读取账单账户表
            AccountStatementInfo accountStatementInfo = accountStatementInfoSelfMapper.selectByAccountManagementIdAndDate(accountManagementInfoDto.getAccountManagementId(),accountManagementInfoDto.getLastStatementDate());
            if(Objects.nonNull(accountStatementInfo)){
                //如果选择的日期>=账单账户表中的违约金收取日（LATE_CHARGE_DATE），报错误提示：最后还款日开始，不允许进行补自扣处理
                if(!accountManagementInfoDto.getTempPaymentDate().isBefore(accountStatementInfo.getLateChargeDate())){
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_ERR, AccountingRepDetailEnum.DE_LA);
                }
            }
            //如果选择的日期<机构参数表中的next_processing_day，报错误提示：不允许输入历史日期
            if(accountManagementInfoDto.getTempPaymentDate().isBefore(orgConfig.getNextProcessingDay())){
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_ERR, AccountingRepDetailEnum.HI_AL);
            }
            //如果AUTO_PAYMENT_TYPE不等于0，并且，（选择的日期=AUTO_PAYMENT_FIRST_DATE，or，AUTO_PAYMENT_SECOND_DATE），报错误提示：已存在约定自扣日期
            if(!"0".equals(accountManagementInfoDto.getAutoPaymentType())
                    && (accountManagementInfoDto.getTempPaymentDate().isEqual(accountManagementInfoDto.getAutoPaymentFirstDate()) || accountManagementInfoDto.getTempPaymentDate().isEqual(accountManagementInfoDto.getAutoPaymentSecondDate()))){
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_ERR, AccountingRepDetailEnum.AG_SE);
            }
        }

        //如果'临时自扣类型'不为0，临时自扣方式为必选项
        if(!"0".equals(accountManagementInfoDto.getTempPaymentType())){
            if(Objects.isNull(accountManagementInfoDto.getTempPaymentMethod())){
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL, AccountingRepDetailEnum.EN_DE);
            }
        }

        //如果'临时自扣类型'为1或9，则临时自扣本行借记账户号为必输项，输入时检查规则同现在的'约定扣款借记账户号'字段一致
        if("1".equals(accountManagementInfoDto.getTempPaymentType()) || "9".equals(accountManagementInfoDto.getTempPaymentType())){
            if(Objects.isNull(accountManagementInfoDto.getTempPaymentDebitAcctNumber())){
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL, AccountingRepDetailEnum.IN_TE);
            }
        }
        //如果'临时自扣类型'为1或9，则临时自扣本行银行号为必输项，输入时检查规则同现在的'约定扣款借方银行号'字段一致
        if("1".equals(accountManagementInfoDto.getTempPaymentType()) || "9".equals(accountManagementInfoDto.getTempPaymentType())){
            if(Objects.isNull(accountManagementInfoDto.getTempPaymentDebitBankNumber())){
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL, AccountingRepDetailEnum.IN_WI);
            }
        }
        //如果'临时自扣类型'为2，则临时自扣他行借记账户号为必输项，输入时检查规则同现在的'约定扣款借记账户号'字段一致
        if("2".equals(accountManagementInfoDto.getTempPaymentType())){
            if(Objects.isNull(accountManagementInfoDto.getTempPaymentOtherAcctNumber())){
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL, AccountingRepDetailEnum.IN_DE);
            }
        }
        //如果'临时自扣类型'为2，则临时自扣他行银行号为必输项，输入时检查规则同现在的'约定扣款借方银行号'字段一致
        if("2".equals(accountManagementInfoDto.getTempPaymentType())){
            if(Objects.isNull(accountManagementInfoDto.getTempPaymentOtherBankNumber())){
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL, AccountingRepDetailEnum.IN_BA);
            }
        }
    }*/
    private void doBlockHandle(AccountManagementInfoDTO accountManagementInfoDto, AccountManagementInfo accountManagementInfo, OrganizationInfoResDTO orgConfig, AccountManagementInfo oldAccountManageInfo) {
        logger.info("Block code linkage modification");
        String updFlag=null;
        if (!StringUtils.equals(oldAccountManageInfo.getBlockCode(),accountManagementInfoDto.getBlockCode())){
            if("1".equals(orgConfig.getEscalationInUse())){
                String esc_auth_action=null;
                int esc_priority=0;
                int cust_priority=0;
                List<ParmBlockCodeAccount> parmBlockCodeAccountList=new ArrayList<>();
                List<AccountManagementInfo> accountManagementInfoList=accountManagementInfoSelfMapper.selectByCustomerIdAndOrgNo(accountManagementInfo.getCustomerId(),accountManagementInfo.getOrganizationNumber());
                CustomerAuthorizationInfo customerAuthorizationInfo=customerAuthorizationInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(),accountManagementInfo.getCustomerId());
                CustomerAuthorizationInfo customerAuthorizationInfoOld=new CustomerAuthorizationInfo();
                BeanMapping.copy(customerAuthorizationInfo,customerAuthorizationInfoOld);
                if(CollectionUtils.isNotEmpty(accountManagementInfoList)) {
                    for (AccountManagementInfo managementInfo : accountManagementInfoList) {
                        //获取账户产品参数id
                        ParmProductInfo parmProductInfo= parmProductInfoSelfMapper.selectByProdNumber(managementInfo.getProductNumber(), OrgNumberUtils.getOrg()).get(0);
                        ParmBlockCodeAccount parmBlockCodeAccount= parmBlockCodeAccountSelfMapper.selectParmBlockCodeAccount(managementInfo.getOrganizationNumber(),parmProductInfo.getAccountBlockCodeTableId(),managementInfo.getBlockCode());
                        if(parmBlockCodeAccount!=null&&"1".equals(parmBlockCodeAccount.getEscalationIndicator())){
                            //暂借优先级字段使用
                            parmBlockCodeAccount.setPriority(getEscalatedValue(parmBlockCodeAccount.getAuthorizationIndicator()));
                            parmBlockCodeAccountList.add(parmBlockCodeAccount);
                        }
                    }
                }
                if(parmBlockCodeAccountList.size()>0){
                    //获取优先级，计算出esc_priority，
                    //        parmBlockCodeAccountList.sort((x, y) -> Integer.compare(Integer.parseInt(y.getAuthorizationIndicator()), Integer.parseInt(x.getAuthorizationIndicator())));
                    parmBlockCodeAccountList.sort(Comparator.comparing(ParmBlockCodeAccount::getPriority).reversed()); // 倒序
//                    ParmBlockCodeAccount max = parmBlockCodeAccountList.stream().max(Comparator.comparing(ParmBlockCodeAccount::getPriority)).get();
                    ParmBlockCodeAccount max = parmBlockCodeAccountList.get(0);
                    esc_auth_action= max.getAuthorizationIndicator();
                    esc_priority=max.getPriority();
                }
                if(customerAuthorizationInfo.getEscalatedAuthorizationAction()!=null){
                    //获取优先级
                    cust_priority= getEscalatedValue(customerAuthorizationInfo.getEscalatedAuthorizationAction());
                }
                if(esc_priority>cust_priority){
                    customerAuthorizationInfo.setEscalatedAuthorizationAction(esc_auth_action);
                    updFlag="1";
                }else if(esc_priority==0&&cust_priority!=0){
                    customerAuthorizationInfo.setEscalatedAuthorizationAction(null);
                    updFlag="1";

                }else if(esc_priority<cust_priority){
                    customerAuthorizationInfo.setEscalatedAuthorizationAction(esc_auth_action);
                    updFlag="1";
                }
                //记录卡户人维护日志（maintenance_log）
                //添加业务日志
                if(updFlag!=null) {
                    //更新客户授权信息
                    customerAuthorizationInfoMapper.updateByPrimaryKeySelective(customerAuthorizationInfo);
                    MaintenanceLogDTO maintenanceLog = new MaintenanceLogDTO();
                    maintenanceLog.setOperationTimestamp(LocalDateTime.now());
                    maintenanceLog.setPrimaryKeyValue(accountManagementInfo.getAccountManagementId());
                    maintenanceLog.setOperationType(MaintenanceConstant.OPERATION_U);
                    maintenanceLog.setTransactionDataType(MaintenanceConstant.DATA_P);
                    maintenanceLog.setOriginalValue(customerAuthorizationInfoOld.getEscalatedAuthorizationAction());
                    maintenanceLog.setUpdatedValue(customerAuthorizationInfo.getEscalatedAuthorizationAction());
                    maintenanceLog.setOperatorId(LoginUserUtils.getLoginUserName());
                    maintenanceLogService.add(maintenanceLog, customerAuthorizationInfo, customerAuthorizationInfoOld, MaintenanceConstant.CUSTOMER_AUTHORIZATION_INFO);
                }
            }
        }
    }
    //联动属性获取组装key、value
    private int  getEscalatedValue(String key){
        Map<String,Integer> map=new HashMap();
        map.put("0",0);
        map.put("1",90);
        map.put("2",70);
        map.put("3",80);
        map.put("4",60);
        map.put("5",0);
        map.put("6",0);
        map.put("7",0);
        map.put("8",0);
        map.put("9",0);
        return map.get(key);
    }
    @Override
    public List<AccountManagementInfoDTO> findByCorpManagement(String organizationNumber, String corporateCustomerId, String searchType, String searchNumber) {
        List<AccountManagementInfoDTO> accountManagementInfoDTOS = new ArrayList<>();
        List<CardAuthorizationInfo> cardAuthorizationInfos = null;
        logger.info("Query card authorization info by organization number and corporate customer ID: orgNumber={}, corpCustomerId={}, searchType={}, searchNumber={}", organizationNumber, corporateCustomerId, searchType, searchNumber);
        if(Arrays.asList("CNR","LNR","CEN").contains(searchType)){
            if("CNR".equals(searchType)){ //卡号
                CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(searchNumber, OrgNumberUtils.getOrg());
                if (null==cardAuthorizationInfo){
                    logger.info("Card number does not exist in card authorization info table: cardNumber={}", searchNumber);
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST);
                }
                if (!corporateCustomerId.equals(cardAuthorizationInfo.getCorporateCustomerId())){
                    return new ArrayList<AccountManagementInfoDTO>();
                }
                if (LiabilityEnum.CORPORATE.getCode().equals(cardAuthorizationInfo.getLiability())){
                    //公司清偿账户展示公司账户
                    List<AccountManagementInfo> accountManagementInfos = accountManagementInfoSelfMapper.selectByOrgAndCorpCusId(organizationNumber, corporateCustomerId);
                    if (CollectionUtils.isNotEmpty(accountManagementInfos)){
                        accountManagementInfoDTOS = BeanMapping.copyList(accountManagementInfos,AccountManagementInfoDTO.class);
                    }
                    return accountManagementInfoDTOS;
                }
                accountManagementInfoDTOS = findListBySearchTypeAndNum("C", "0", searchNumber, null);
            }else if("CEN".equals(searchType)){//emboss name
                List<CardBasicInfo> basicInfos = cardBasicInfoSelfMapper.selectByEmbossNameLike(searchNumber);
               if (CollectionUtils.isEmpty(basicInfos)){
                   logger.info("Card basic info query result is empty for emboss name: embossName={}", searchNumber);
                   throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST);
               }
               List<String> cardNumbers = basicInfos.stream().map(CardBasicInfo::getCardNumber).collect(Collectors.toList());
               cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByCardNumbers(cardNumbers);
                if (CollectionUtils.isEmpty(cardAuthorizationInfos)){
                  return new ArrayList<AccountManagementInfoDTO>();
                }
                //根据客户号查询所有管理账户
                cardAuthorizationInfos = cardAuthorizationInfos.stream().filter(f->StringUtils.isNotEmpty(f.getCorporateCustomerId())&&f.getCorporateCustomerId().equals(corporateCustomerId) && StringUtils.equalsAny(f.getStatus(),"1","2")).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(cardAuthorizationInfos)){
                   logger.info("No active or blocked card authorization info for emboss name: embossName={}", searchNumber);
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST);
                }
                if(LiabilityEnum.CORPORATE.getCode().equals(cardAuthorizationInfos.get(0).getLiability())){
                    //公司清偿
                    List<AccountManagementInfo> accountManagementInfos = accountManagementInfoSelfMapper.selectByOrgAndCorpCusId(organizationNumber, corporateCustomerId);
                    if (CollectionUtils.isNotEmpty(accountManagementInfos)){
                        accountManagementInfoDTOS = BeanMapping.copyList(accountManagementInfos,AccountManagementInfoDTO.class);
                    }
                    return accountManagementInfoDTOS;
                }
                String cardNumber = cardAuthorizationInfos.get(0).getCardNumber();
                accountManagementInfoDTOS =findListBySearchTypeAndNum("C", "0", cardNumber, null);
            }
            else if("LNR".equals(searchType)){//Loyalty number 积分号
                List<CardAuthorizationInfo> authorizationInfoList = cardAuthorizationInfoSelfMapper.selectByLoyaltyNumberAndOrganizationNumber(OrgNumberUtils.getOrg(),searchNumber);
                if (CollectionUtils.isEmpty(authorizationInfoList)) {
                    throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST);
                }
                List<CardAuthorizationInfo> mainCardAuthorizationInfoList = authorizationInfoList.stream().filter(t -> StringUtils.equalsAny(t.getStatus(),"1","2") && t.getCorporateCustomerId().equals(corporateCustomerId)).collect(Collectors.toList());
                if (org.springframework.util.CollectionUtils.isEmpty(mainCardAuthorizationInfoList)){
                    return new ArrayList<AccountManagementInfoDTO>();
                }
                if (LiabilityEnum.CORPORATE.getCode().equals(mainCardAuthorizationInfoList.get(0).getLiability())){
                    //公司清偿账户展示公司账户
                    List<AccountManagementInfo> accountManagementInfos = accountManagementInfoSelfMapper.selectByOrgAndCorpCusId(organizationNumber, corporateCustomerId);
                    if (CollectionUtils.isNotEmpty(accountManagementInfos)){
                        accountManagementInfoDTOS = BeanMapping.copyList(accountManagementInfos,AccountManagementInfoDTO.class);
                    }
                    return accountManagementInfoDTOS;
                }
                String cardNumber = mainCardAuthorizationInfoList.get(0).getCardNumber();
                accountManagementInfoDTOS =findListBySearchTypeAndNum("C", "0", cardNumber, null);
            }
        }else{
            List<AccountManagementInfo> accountManagementInfos = accountManagementInfoSelfMapper.selectByOrgAndCorpCusId(organizationNumber, corporateCustomerId);
            if (CollectionUtils.isNotEmpty(accountManagementInfos)){
                accountManagementInfoDTOS = BeanMapping.copyList(accountManagementInfos,AccountManagementInfoDTO.class);
            }

        }
        return accountManagementInfoDTOS;
    }

    /**
     * 给cbsCloseDate赋值账户封锁码设置日期
     *
     * @param cbsCloseDate LocalDate
     * @param statisticsInfo AccountStatisticsInfo
     */
    private void setCbsCloseDate(LocalDate cbsCloseDate,
                                 AccountStatisticsInfo statisticsInfo){
        statisticsInfo.setCbsCloseDate(cbsCloseDate);
        statisticsInfoMapper.updateByPrimaryKeySelective(statisticsInfo);
    }
}
