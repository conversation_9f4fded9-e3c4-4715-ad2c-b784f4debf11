package com.anytech.anytxn.account.job.autopayment.step;

import com.anytech.anytxn.account.base.domain.bo.AutoPaymentBO;
import com.anytech.anytxn.account.base.service.IAccountManageInfoService;
import com.anytech.anytxn.business.base.card.domain.dto.AutoEbitSignUpInforDTO;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2019-09-02
 **/
@Slf4j
public class AutoPayProcessor implements ItemProcessor<AutoEbitSignUpInforDTO, AutoPaymentBO> {
    private static final Logger logger = LoggerFactory.getLogger(AutoPayProcessor.class);
    @Autowired
    private IAccountManageInfoService accountManageInfoService;

    @Override
    public AutoPaymentBO process(AutoEbitSignUpInforDTO autoEbitSignUpInfor) {
        logger.info("Processing auto sign-up data, id={}", autoEbitSignUpInfor.getId());
        return accountManageInfoService.batchProcess4AutoPayment(autoEbitSignUpInfor);
    }
}
