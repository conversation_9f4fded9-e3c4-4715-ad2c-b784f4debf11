package com.anytech.anytxn.installment.batch.job.generatecash.step;

import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.installment.model.InstallRecord;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.database.JdbcPagingItemReader;
import org.springframework.batch.item.database.Order;
import org.springframework.batch.item.database.PagingQueryProvider;
import org.springframework.batch.item.database.support.SqlPagingQueryProviderFactoryBean;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2020-02-09 14:47
 **/
public class GenerateCashReader extends JdbcPagingItemReader<InstallRecord> {

    private static final Logger logger = LoggerFactory.getLogger(GenerateCashReader.class);

    public GenerateCashReader(DataSource dataSource)
    {
        super();
        this.setRowMapper((new BeanPropertyRowMapper<>(InstallRecord.class)));
        this.setQueryProvider(oraclePagingQueryProvider(dataSource));
    }

    private PagingQueryProvider oraclePagingQueryProvider(DataSource dataSource) {
        SqlPagingQueryProviderFactoryBean providerFactoryBean = new SqlPagingQueryProviderFactoryBean();
        String sql = " FUNCTION_CODE = 'C' " +
                    " and INSTALL_TYPE = 'C' "  +
                    " and TRANSACTION_IND = 'S' " +
                    " and ORGANIZATION_NUMBER = '" + OrgNumberUtils.getOrg() + "'" +
                    " and FUND_PROCESS_FLAG = '0'";
        providerFactoryBean.setSelectClause(
                        " ID, ORIGIN_ID ,  FUNCTION_CODE ,  ORGANIZATION_NUMBER ,  ACCOUNT_MANAGEMENT_ID ,  CARD_NUMBER , "
                        + "PRODUCT_CODE ,  INSTALL_TYPE ,  ORIGIN_TRANSACTION_ID ,  TRANSACTION_DATE ,  ACQUIRE_REFERENCE_NO , "
                        + "TRANSFER_CARD ,  TRANSFER_BANK ,  TRANSFER_BRANCH ,  TRANSFER_BANK_NAME ,  CUSTOMER_TELEPHONE ,"
                        + "APPLY_ID_TYPE ,  APPLY_ID_NO ,  CARD_EXPIRATION_DATE ,  INSTALL_TERM, INSTALL_CCY ,"
                        + "INSTALL_AMOUNT ,  INSTALL_PRICE_FLAG ,  INSTALL_TOTAL_FEE ,  INSTALL_FEE_RATE ,  INSTALL_DERATE_METHOD ,"
                        + "INSTALL_DERATE_VALUE ,  LIMIT_CODE ,  MERCHANT_ID, MCC ,  TRANSACTION_DESC ,  AUTHORIZATION_CODE ,"
                        + "GLOBAL_FLOW_NUMBER ,  FUND_PROCESS_FLAG ,  TRANSACTION_IND ,  FAIL_CODE ,  FAIL_REASON ,"
                        + "UPDATE_BY ,  CREATE_TIME ,  UPDATE_TIME ,  VERSION_NUMBER ,  FUND_PAYMENT_FAIL_REASON , AUTH_TRANSMISION_TIME ,"
                        + "TRANSFER_CARD_HOLDER_NAME ,  CUSTOMER_ID , STATEMENT_DATE, CUSTOMER_REGION "
        );
        providerFactoryBean.setFromClause("INSTALL_RECORD");
        providerFactoryBean.setWhereClause(sql);
        Map<String, Order> sortKey = new HashMap<>(10);
        sortKey.put("ID", Order.ASCENDING);
        providerFactoryBean.setSortKeys(sortKey);
        providerFactoryBean.setDataSource(dataSource);
        try {
            return providerFactoryBean.getObject();
        } catch (Exception e) {
            logger.error("Failed to create PagingQueryProvider: exception={}", e.getMessage(), e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_SELECT_DATABASE_FAULT,e);
        }
    }
}
