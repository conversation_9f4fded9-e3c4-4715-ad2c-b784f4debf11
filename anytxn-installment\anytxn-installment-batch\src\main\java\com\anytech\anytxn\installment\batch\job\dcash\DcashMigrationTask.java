package com.anytech.anytxn.installment.batch.job.dcash;

import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.dao.installment.model.InstallPlan;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.installment.base.domain.dto.InstallmentInterestDTO;
import com.anytech.anytxn.installment.service.interest.Rule78Interest;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallProductInfo;
import com.anytech.anytxn.parameter.base.installment.domain.model.ParmInstallInterestInfo;
import com.anytech.anytxn.parameter.installment.mapper.InstallProductInfoSelfMapper;
import com.anytech.anytxn.parameter.installment.mapper.ParmInstallInterestInfoMapper;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.io.FileUtils;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DcashMigrationTask implements Tasklet {

    private static final Logger logger = LoggerFactory.getLogger(DcashMigrationTask.class);

    @Resource
    private InstallOrderSelfMapper installOrderSelfMapper;


    @Resource
    private InstallOrderMapper installOrderMapper;

    @Resource
    private InstallProductInfoSelfMapper installProductInfoSelfMapper;


    @Resource
    private NamedParameterJdbcTemplate bizJdbcTemplate;


    @Value("#{jobParameters['job.updated']}")
    private Boolean updated;


    @Resource
    private AnytxnFilePathConfig dcashFileFormatPathConfig;



    @Resource
    private ParmInstallInterestInfoMapper parmInstallInterestInfoMapper;

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {

        List<InstallPlan> installPlans = new ArrayList<>(1000);
        List<InstallOrder> installOrderArrayList = new ArrayList<>(1000);

        List<String> stringList  = new ArrayList<>(1000);


        createFile(dcashFileFormatPathConfig);


        List<InstallOrder> installOrders = installOrderSelfMapper.selectDcashOrders();

        for (InstallOrder installOrder : installOrders) {

            InstallProductInfo installProductInfo = installProductInfoSelfMapper.selectByIndex(installOrder.getOrganizationNumber(), installOrder.getProductCode());


            ParmInstallInterestInfo interestInfo = parmInstallInterestInfoMapper.selectByTableIdAndOrgNum(installProductInfo.getInstalmentInterestParmTableId(), installOrder.getOrganizationNumber());


            InstallmentInterestDTO installmentInterestDTO = new InstallmentInterestDTO();
            installmentInterestDTO.setInterestAmount(installOrder.getInstallmentAmount()
                    .subtract(getInstallmentAmount(installOrder, interestInfo.getAnnualInterestRate())));
            installmentInterestDTO.setTerm(installOrder.getTerm());

            new Rule78Interest().getInterestResult(installmentInterestDTO);

            logger.info("Order ID: {}, interest: {}", installOrder.getOrderId(), installmentInterestDTO.getMonthlyInterestAmount());


            for (int i = 0; i < installOrder.getTerm(); i++) {
                InstallPlan installPlan = new InstallPlan();
                installPlan.setOrderId(installOrder.getOrderId());
                installPlan.setTerm(i + 1);
                installPlan.setAmortizeInterest(installmentInterestDTO.getMonthlyInterestAmount().get(i));

                installPlans.add(installPlan);
            }



            if (isUpdated()){
                InstallOrder updateOrder = new InstallOrder();
                updateOrder.setOrderId(installOrder.getOrderId());
                updateOrder.setInstallmentTotalInterest(installmentInterestDTO.getInterestAmount());
                installOrderArrayList.add(updateOrder);

            }else {
                buildInstallOrderLineInfo(installOrder, installPlans, stringList, interestInfo);
            }

            if (installPlans.size() > 500){
                updateData(installPlans,installOrderArrayList, stringList);

                installPlans.clear();
                stringList.clear();
                installOrderArrayList.clear();
            }

        }

        return RepeatStatus.FINISHED;
    }


    private Boolean isUpdated(){
        return updated != null && updated;
    }

    private void buildInstallOrderLineInfo(InstallOrder installOrder,
                                           List<InstallPlan> installPlans,
                                           List<String> stringList,
                                           ParmInstallInterestInfo interestInfo) {


        InstallOrder selectByPrimaryKey = installOrderMapper.selectByPrimaryKey(installOrder.getOrderId());

        for (InstallPlan installPlan : installPlans) {

            StringBuilder stringBuilder = new StringBuilder();

            stringBuilder.append(installOrder.getOrderId()).append(",");
            stringBuilder.append(selectByPrimaryKey.getProductCode()).append(",");
            stringBuilder.append(selectByPrimaryKey.getCardNumber()).append(",");
            stringBuilder.append(installOrder.getInstallmentAmount()).append(",");

            stringBuilder.append(interestInfo.getAnnualInterestRate()).append(",");

            stringBuilder.append(installOrder.getInstallmentTotalInterest()).append(",");

            stringBuilder.append(installPlan.getTerm()).append(",");
            stringBuilder.append(installPlan.getAmortizeInterest());

            stringList.add(stringBuilder.toString());
        }

    }






    private void createFile(AnytxnFilePathConfig dcashFileFormatPathConfig) throws IOException {

        String fileName = dcashFileFormatPathConfig.getCommonPath() + File.separator + "dcashFile.txt";

        File file = new File(fileName);

        if (file.exists()){
            file.delete();
        }

        file.getParentFile().mkdirs();
        file.createNewFile();
    }

    private void updateData(List<InstallPlan> installPlans,
                            List<InstallOrder> installOrderArrayList, List<String> stringList) throws IOException {

        if (isUpdated()){
            if (CollectionUtils.isNotEmpty(installPlans)){
                StringBuilder sql = new StringBuilder();
                sql.append(
                        "update install_plan set AMORTIZE_INTEREST = :amortizeInterest where ORDER_ID = :orderId and TERM = :term "
                );

                bizJdbcTemplate.batchUpdate(sql.toString(), SqlParameterSourceUtils.createBatch(installPlans.toArray()));
            }



            if (CollectionUtils.isNotEmpty(installOrderArrayList)){
                StringBuilder sql = new StringBuilder();
                sql.append(
                        "update install_order set INSTALLMENT_TOTAL_INTEREST = :installmentTotalInterest where ORDER_ID = :orderId  "
                );

                bizJdbcTemplate.batchUpdate(sql.toString(), SqlParameterSourceUtils.createBatch(installOrderArrayList.toArray()));
            }
        }else {
            String fileName = dcashFileFormatPathConfig.getCommonPath() + "/dcashFile.txt";
            File file = new File(fileName);

            FileUtils.writeLines(file,"UTF-8", stringList,"\r\n",true);

        }

    }


    /**
     * 获取分期实际的金额不含利息

     *  X * (1 + 年化利率*分期期数/1200) = 分期金额

     */
    public BigDecimal getInstallmentAmount(InstallOrder installOrder,BigDecimal rate){

        BigDecimal bigDecimal = new BigDecimal("1200");

        return installOrder.getInstallmentAmount().multiply(bigDecimal)
                .divide(bigDecimal.add(
                        new BigDecimal(installOrder.getTerm()).multiply(rate)
                ), BigDecimal.ROUND_HALF_UP);

    }




}
