package com.anytech.anytxn.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.account.base.service.IAutoPaymentLogService;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.business.base.account.domain.dto.AutoPaymentLogDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AutoPaymentLogSearchKeyDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2019-11-16
 */
@RestController
@Tag(name = "约定扣款流水接口")
public class AutoPaymentLogController extends BizBaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(AutoPaymentLogController.class);
    
    @Autowired
    private IAutoPaymentLogService autoPaymentLogService;

    @Operation(summary = "根据搜索条件分页查询约定扣款流水")
    @PostMapping(path = "/account/autoPayment")
    AnyTxnHttpResponse<PageResultDTO<AutoPaymentLogDTO>> findAll(
            @RequestBody(required = false) AutoPaymentLogSearchKeyDTO searchKey){
        logger.info("Find auto payment log by search key request received: searchKey={}", searchKey);
        
        PageResultDTO<AutoPaymentLogDTO> result = autoPaymentLogService.findByCardNum(searchKey);
        
        logger.info("Find auto payment log by search key completed successfully: resultSize={}", result.getList().size());
        return AnyTxnHttpResponse.success(result);
    }

    @Operation(summary = "根据约定扣款id查询约定扣款流水明细")
    @GetMapping("/account/autoPayment/autoPaymentId/{autoPaymentId}")
    @Parameter(name = "autoPaymentId", required = true, description = "约定扣款id")
    AnyTxnHttpResponse<AutoPaymentLogDTO> getAutoPaymentById(
            @PathVariable("autoPaymentId") String autoPaymentId) {
        logger.info("Get auto payment by id request received: autoPaymentId={}", autoPaymentId);
        
        AutoPaymentLogDTO autoPaymentLogDTO = autoPaymentLogService.selectById(autoPaymentId);
        
        logger.info("Get auto payment by id completed successfully");
        return AnyTxnHttpResponse.success(autoPaymentLogDTO);
    }
}
