package com.anytech.anytxn.account.controller;

import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.account.base.service.ICustomerDebtService;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.business.base.account.domain.dto.CustomerDebtsDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;


/**
 * 客户欠款
 * <AUTHOR>
 * @date 2020-10-21
 **/
@RestController
public class CustomerDebtController extends BizBaseController {

    private static final Logger logger = LoggerFactory.getLogger(CustomerDebtController.class);

    @Autowired
    private ICustomerDebtService customerDebtService;

    /**
     * 根据客户号/证件号码/EcIf号查询客户欠款信息
     */
    @Operation(summary = "根据查询类型(客户号:C  证件号:I   EcIf号:E)查询客户欠款信息")
    @GetMapping("/account/customerDebts/{searchType}/{number}")
    AnyTxnHttpResponse<CustomerDebtsDTO> getCustomerDebtBySearchCondition(@PathVariable("searchType") String searchType,
                                                                          @PathVariable("number") String number) {
        logger.info("Get customer debt by search condition request received: searchType={}, number={}", searchType, number);
        
        CustomerDebtsDTO customerDebtsDTO = customerDebtService.getCustomerDebtBySearchCondition(searchType, number);
        
        logger.info("Get customer debt by search condition completed successfully");
        return AnyTxnHttpResponse.success(customerDebtsDTO);
    }
}
