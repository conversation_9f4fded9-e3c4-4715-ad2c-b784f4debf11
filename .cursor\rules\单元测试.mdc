---
description: 
globs: 
alwaysApply: false
---
# Java 单元测试规范与最佳实践

## 1. 目的与价值

单元测试是确保代码质量的关键实践，它能：

- **提高代码质量**：通过测试驱动，编写更简洁、更解耦的代码
- **防止回归**：确保新增功能不会破坏现有功能
- **提供文档**：测试案例是最好的代码文档，展示代码预期行为
- **促进重构**：有测试保障，可以更自信地重构代码
- **加速开发**：减少手动测试时间，更早发现问题

## 2. 基本原则

### 2.1 测试优先 (TDD)

- **先写测试，后写实现**：先定义期望行为，再编写实现代码
- **小步迭代**：编写一个测试，实现功能，重构，然后进入下一个循环
- **覆盖率要求**：
  - 整体代码覆盖率目标：≥80%
  - 核心业务逻辑：力争100%覆盖
  - 新增代码：不应降低现有覆盖率

### 2.2 自动化与持续集成

- **CI流程集成**：所有单元测试必须集成到CI/CD流程中
- **快速失败**：测试失败应立即中断构建
- **测试报告**：自动生成覆盖率和测试结果报告
- **定期执行**：除提交触发外，应有定时执行机制

### 2.3 可读性与维护性

- **清晰的测试意图**：测试名称和结构应明确表达测试意图
- **测试结构**：遵循AAA模式（Arrange-Act-Assert）
- **避免测试间依赖**：每个测试应该独立，不依赖其他测试的执行结果
- **适度抽象**：测试代码中可以适当抽象，但不要过度

### 2.4 快速与隔离

- **执行速度**：单元测试套件应在秒级完成
- **资源隔离**：避免依赖以下外部资源：
  - 数据库
  - 网络服务
  - 文件系统
  - 其他外部系统
- **测试替身**：使用Mock、Stub等测试替身隔离外部依赖

## 3. 测试结构与组织

### 3.1 测试类命名与组织

- **命名规则**：`被测类名 + Test`
  - 例：`OrderService` → `OrderServiceTest`
- **包结构**：测试类与被测类包结构保持一致
  - 例：`com.anytxn.service.OrderService` → `com.anytxn.service.OrderServiceTest`
- **存放位置**：统一放在`src/test/java`目录下

### 3.2 测试类基本结构示例

```java
@ExtendWith(MockitoExtension.class)
class OrderServiceTest {
    // 字段区
    @InjectMocks
    private OrderService orderService;

    @Mock
    private OrderRepository orderRepository;
    
    @Mock
    private PaymentGateway paymentGateway;
    
    // 测试数据
    private static final String VALID_ORDER_ID = "ORD-001";
    private static final BigDecimal VALID_AMOUNT = new BigDecimal("100.50");
    
    // 测试前准备
    @BeforeEach
    void setUp() {
        // 初始化测试数据或配置
    }
    
    // 正常路径测试
    @Test
    void placeOrder_withValidOrderData_shouldCreateOrderSuccessfully() {
        // Arrange - 准备测试数据和环境
        OrderRequest request = new OrderRequest(VALID_AMOUNT, "Product A");
        Order expectedOrder = new Order(VALID_ORDER_ID, VALID_AMOUNT);
        when(orderRepository.save(any(Order.class))).thenReturn(expectedOrder);
        
        // Act - 执行被测方法
        OrderResult result = orderService.placeOrder(request);
        
        // Assert - 验证结果
        assertNotNull(result);
        assertEquals(VALID_ORDER_ID, result.getOrderId());
        assertEquals(OrderStatus.CREATED, result.getStatus());
        verify(orderRepository).save(any(Order.class));
    }
    
    // 异常路径测试
    @Test
    void placeOrder_withNullRequest_shouldThrowException() {
        // Arrange - 准备测试数据和环境
        
        // Act & Assert - 执行并验证异常
        assertThrows(IllegalArgumentException.class, () -> {
            orderService.placeOrder(null);
        });
        
        // 验证异常情况下的交互
        verify(orderRepository, never()).save(any());
    }
    
    // 边界值测试
    @Test
    void placeOrder_withZeroAmount_shouldThrowException() {
        // ...测试代码
    }
}
```

### 3.3 测试方法命名

遵循：`方法名_测试条件_期望结果`格式，例如：

- `placeOrder_withValidData_shouldSucceed`
- `getUser_whenUserNotFound_shouldReturnEmpty`
- `calculateTotal_withNegativePrice_shouldThrowException`

## 4. 断言与验证

### 4.1 断言类型与选择

使用JUnit 5的断言方法：

```java
// 相等性断言
assertEquals(expected, actual, "Optional failure message");
assertNotEquals(unexpected, actual);

// 布尔断言
assertTrue(condition, "Should be true");
assertFalse(condition);

// 空值断言
assertNull(result);
assertNotNull(result);

// 集合断言
assertIterableEquals(expectedList, actualList);
assertTrue(actualList.contains(expectedItem));

// 异常断言
Exception exception = assertThrows(IllegalArgumentException.class, 
    () -> service.doSomething(invalidArg));
assertEquals("Expected message", exception.getMessage());

// 超时断言
assertTimeout(Duration.ofMillis(100), () -> {
    // 可能超时的操作
});

// 组合断言
assertAll(
    () -> assertEquals(expected1, actual1),
    () -> assertTrue(condition),
    () -> assertNotNull(result)
);
```

### 4.2 断言最佳实践

- 每个测试至少包含一个断言
- 多个相关断言可以使用`assertAll`组合
- 断言失败消息应明确指出预期与实际结果
- 对于复杂对象比较，可考虑：
  - 重写`equals()`和`hashCode()`
  - 使用AssertJ等工具库提供的流式API
  - 自定义Matcher

## 5. Mock与依赖隔离

### 5.1 Mockito基础用法

```java
// 创建Mock对象
@Mock
private PaymentGateway paymentGateway;

// 或手动创建
PaymentGateway paymentGateway = Mockito.mock(PaymentGateway.class);

// 注入Mock对象
@InjectMocks
private OrderService orderService;

// 配置Mock行为
when(paymentGateway.processPayment(any(PaymentRequest.class)))
    .thenReturn(PaymentResult.success());

// 配置连续调用行为
when(generator.nextId())
    .thenReturn("ID1")
    .thenReturn("ID2");
    
// 配置抛出异常
when(paymentGateway.processPayment(any()))
    .thenThrow(new PaymentException("Payment failed"));
    
// 验证交互 - 调用次数
verify(paymentGateway).processPayment(any());  // 默认times(1)
verify(paymentGateway, times(2)).processPayment(any());
verify(paymentGateway, never()).refund(any());

// 验证交互 - 参数捕获
ArgumentCaptor<PaymentRequest> captor = ArgumentCaptor.forClass(PaymentRequest.class);
verify(paymentGateway).processPayment(captor.capture());
PaymentRequest capturedRequest = captor.getValue();
assertEquals(VALID_AMOUNT, capturedRequest.getAmount());
```

### 5.2 Mock最佳实践

- **只Mock直接依赖**：不要Mock被测对象，只Mock其依赖
- **避免过度验证**：只验证与测试结果相关的交互
- **明确定义行为**：明确定义Mock对象的所有必要行为
- **使用合适的匹配器**：使用`any()`、`eq()`等匹配器增强灵活性
- **验证顺序**（必要时）：
  ```java
  InOrder inOrder = inOrder(paymentGateway, orderRepository);
  inOrder.verify(paymentGateway).processPayment(any());
  inOrder.verify(orderRepository).save(any());
  ```

### 5.3 其他测试替身

- **Spy**：部分Mock，保留原始方法
  ```java
  @Spy
  private OrderValidator validator;
  
  doReturn(true).when(validator).isValid(any());
  ```

- **Stub**：简单固定返回值的对象

- **Fake**：轻量级实现，如内存数据库
  ```java
  // 使用内存H2替代真实数据库
  @TestConfiguration
  class TestConfig {
      @Bean
      public DataSource dataSource() {
          return new EmbeddedDatabaseBuilder()
              .setType(EmbeddedDatabaseType.H2)
              .build();
      }
  }
  ```

## 6. 测试场景覆盖

### 6.1 必须覆盖的场景

每个业务方法至少包含以下测试：

1. **正常路径（Happy Path）**
   - 使用有效输入，验证正确输出
   - 验证与依赖的正确交互

2. **异常路径（Exception Path）**
   - 无效输入处理
   - 异常抛出与传播
   - 外部依赖异常处理

3. **边界条件（Boundary Cases）**
   - 空值（null）处理
   - 空集合、空字符串
   - 极限值（最大值、最小值、零值）
   - 特殊字符、格式

### 6.2 复杂场景测试

- **并发场景**：使用`CountDownLatch`等工具测试并发
- **事务场景**：测试事务提交和回滚
- **权限控制**：测试不同权限下的行为
- **状态转换**：测试状态机逻辑

## 7. 测试数据管理

### 7.1 测试数据原则

- **自包含**：测试应包含所有需要的测试数据
- **明确意图**：测试数据应反映测试目的
- **可读性**：使用有意义的变量名和值
- **避免硬编码**：使用常量定义测试数据

### 7.2 测试数据工厂

创建测试数据工厂简化测试数据准备：

```java
public class TestOrderFactory {
    public static Order createValidOrder() {
        return Order.builder()
            .id("ORD-001")
            .amount(new BigDecimal("100.50"))
            .status(OrderStatus.CREATED)
            .items(Arrays.asList(createOrderItem()))
            .build();
    }
    
    public static OrderItem createOrderItem() {
        return OrderItem.builder()
            .id("ITEM-001")
            .name("Test Product")
            .price(new BigDecimal("100.50"))
            .quantity(1)
            .build();
    }
    
    // 其他工厂方法...
}
```

### 7.3 参数化测试

使用JUnit 5的参数化测试减少代码重复：

```java
@ParameterizedTest
@CsvSource({
    "100.00, STANDARD, 5.00",
    "100.00, EXPRESS, 15.00",
    "500.00, STANDARD, 0.00",
    "500.00, EXPRESS, 10.00"
})
void calculateShippingFee_withDifferentOrdersAndMethods_shouldReturnCorrectFee(
        BigDecimal orderAmount, 
        ShippingMethod method, 
        BigDecimal expectedFee) {
    
    // Arrange
    Order order = TestOrderFactory.createOrderWithAmount(orderAmount);
    
    // Act
    BigDecimal fee = shippingService.calculateFee(order, method);
    
    // Assert
    assertEquals(expectedFee, fee);
}
```

## 8. 高级测试技术

### 8.1 Spring Boot测试

使用Spring Boot测试工具进行更高级别的测试：

```java
// 服务层测试
@ExtendWith(SpringExtension.class)
@SpringBootTest
class OrderServiceIntegrationTest {
    @Autowired
    private OrderService orderService;
    
    @MockBean
    private PaymentGateway paymentGateway;
    
    @Test
    void placeOrder_withValidData_shouldSucceed() {
        // 测试代码...
    }
}

// 控制器测试
@WebMvcTest(OrderController.class)
class OrderControllerTest {
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private OrderService orderService;
    
    @Test
    void getOrder_shouldReturnOrder() throws Exception {
        when(orderService.getOrder(anyString()))
            .thenReturn(Optional.of(TestOrderFactory.createValidOrder()));
            
        mockMvc.perform(get("/orders/ORD-001"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.id").value("ORD-001"));
    }
}
```

### 8.2 测试安全性功能

```java
@WebMvcTest(OrderController.class)
@WithMockUser(roles = "USER")
class OrderControllerSecurityTest {
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private OrderService orderService;
    
    @Test
    void getOrder_withUserRole_shouldSucceed() throws Exception {
        // 测试用户角色成功访问
    }
    
    @Test
    @WithMockUser(roles = "ADMIN")
    void deleteOrder_withAdminRole_shouldSucceed() throws Exception {
        // 测试管理员角色成功访问
    }
    
    @Test
    @WithAnonymousUser
    void getOrder_withAnonymousUser_shouldReturnUnauthorized() throws Exception {
        // 测试匿名用户访问失败
    }
}
```

### 8.3 性能测试

```java
@Test
void calculateStatistics_withLargeDataset_shouldCompleteWithinTimeLimit() {
    // 准备大数据集
    List<Transaction> largeTransactionList = generateLargeTransactionList(10000);
    
    // 执行并验证时间
    assertTimeout(Duration.ofMillis(100), () -> {
        statisticsService.calculateStatistics(largeTransactionList);
    });
}
```

## 9. 测试驱动开发实践

### 9.1 TDD流程

1. **编写失败测试**：先写一个测试，定义期望功能
2. **让测试通过**：编写最简代码使测试通过
3. **重构代码**：在测试通过的基础上改进代码质量
4. **重复以上步骤**

### 9.2 TDD示例

```java
// 步骤1：编写失败测试
@Test
void calculateDiscount_withVipCustomer_shouldApplyVipDiscount() {
    // Arrange
    Customer vipCustomer = new Customer("John", CustomerType.VIP);
    Order order = new Order(vipCustomer, new BigDecimal("100.00"));
    
    // Act
    BigDecimal finalPrice = discountService.calculateDiscountedPrice(order);
    
    // Assert
    assertEquals(new BigDecimal("80.00"), finalPrice); // 期望VIP有20%折扣
}

// 步骤2：最简实现让测试通过
public BigDecimal calculateDiscountedPrice(Order order) {
    if (order.getCustomer().getType() == CustomerType.VIP) {
        return order.getAmount().multiply(new BigDecimal("0.8"));
    }
    return order.getAmount();
}

// 步骤3：添加更多测试扩展功能
@Test
void calculateDiscount_withRegularCustomer_shouldApplyNoDiscount() {
    // ...
}

@Test
void calculateDiscount_withVipCustomerAndPromotionDay_shouldApplyHigherDiscount() {
    // ...
}

// 步骤4：重构实现
public BigDecimal calculateDiscountedPrice(Order order) {
    BigDecimal discount = getCustomerTypeDiscount(order.getCustomer().getType());
    if (promotionService.isPromotionDay()) {
        discount = discount.add(promotionService.getExtraDiscount());
    }
    return order.getAmount().multiply(BigDecimal.ONE.subtract(discount));
}

private BigDecimal getCustomerTypeDiscount(CustomerType type) {
    switch (type) {
        case VIP: return new BigDecimal("0.2");
        case REGULAR: return BigDecimal.ZERO;
        default: return BigDecimal.ZERO;
    }
}
```

## 10. 本项目特有规范

### 10.1 认证授权测试规范

在授权相关测试中，应特别注意：

- **权限验证**：测试不同角色对API的访问权限
- **Token处理**：测试Token生成、验证、过期等场景
- **安全上下文**：模拟安全上下文进行测试

```java
// 示例：权限测试
@Test
@WithMockUser(roles = "ADMIN")
void updatePermission_withAdminRole_shouldSucceed() {
    // 测试管理员可以更新权限
}

@Test
@WithMockUser(roles = "USER")
void updatePermission_withUserRole_shouldThrowAccessDeniedException() {
    // 测试普通用户不能更新权限
}
```

### 10.2 项目常用测试工具类

```java
// 使用项目中的TestUtil工具类
@Test
void authenticateUser_withValidCredentials_shouldReturnToken() {
    // Arrange
    UserCredentials credentials = TestUtil.createValidCredentials();
    
    // Act
    AuthToken token = authService.authenticate(credentials);
    
    // Assert
    assertNotNull(token);
    assertTrue(TestUtil.isValidToken(token.getValue()));
}
```

## 11. 常见问题与解决方案

### 11.1 测试数据库依赖

使用以下方式解决数据库依赖：

- **内存数据库**：使用H2等内存数据库
- **TestContainers**：使用Docker容器运行测试数据库
- **DBUnit**：管理测试数据集

### 11.2 异步代码测试

```java
@Test
void processOrderAsync_shouldCompleteAndSendNotification() throws Exception {
    // Arrange
    CountDownLatch latch = new CountDownLatch(1);
    doAnswer(invocation -> {
        latch.countDown();
        return null;
    }).when(notificationService).sendNotification(any());
    
    // Act
    orderService.processOrderAsync("ORD-001");
    
    // Assert
    assertTrue(latch.await(1, TimeUnit.SECONDS), "Notification was not sent");
    verify(notificationService).sendNotification(any());
}
```

### 11.3 静态方法/工具类测试

使用PowerMockito或重构设计测试静态方法：

```java
// 重构设计以便测试
public interface TimeProvider {
    Instant now();
}

public class RealTimeProvider implements TimeProvider {
    @Override
    public Instant now() {
        return Instant.now();
    }
}

// 测试中
@Mock
private TimeProvider timeProvider;

@Test
void calculateExpiry_shouldAddValidityPeriod() {
    // Arrange
    Instant fixedTime = Instant.parse("2023-01-01T00:00:00Z");
    when(timeProvider.now()).thenReturn(fixedTime);
    
    // Act
    Instant expiry = tokenService.calculateExpiry(Duration.ofDays(1));
    
    // Assert
    assertEquals(fixedTime.plus(Duration.ofDays(1)), expiry);
}
```

## 12. 集成测试最佳实践

### 12.1 集成测试策略

#### 测试金字塔原则
- **单元测试 (70%)**：测试单个组件的功能
- **集成测试 (20%)**：测试组件间的交互
- **端到端测试 (10%)**：测试完整的用户场景

#### 集成测试分类
1. **模块内集成测试**：测试同一模块内不同类的交互
2. **模块间集成测试**：测试不同模块之间的接口
3. **系统集成测试**：测试与外部系统的集成
4. **数据库集成测试**：测试数据持久化逻辑

### 12.2 Spring Boot 集成测试基础

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.jpa.hibernate.ddl-auto=create-drop"
})
class UserIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private UserRepository userRepository;
    
    @Test
    void shouldCreateUser_whenValidRequestProvided() {
        // Given
        CreateUserRequest request = new CreateUserRequest("张三", "<EMAIL>");
        
        // When
        ResponseEntity<UserResponse> response = restTemplate.postForEntity(
            "/api/users", request, UserResponse.class);
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody().getName()).isEqualTo("张三");
        
        // 验证数据库状态
        Optional<User> savedUser = userRepository.findByEmail("<EMAIL>");
        assertThat(savedUser).isPresent();
    }
}
```

### 12.3 测试切片注解的使用

根据测试范围选择合适的测试切片：

```java
// Web 层集成测试
@WebMvcTest(UserController.class)
class UserControllerIntegrationTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private UserService userService;
    
    @Test
    void shouldReturnUserList_whenGetUsersEndpointCalled() throws Exception {
        // Given
        List<User> users = Arrays.asList(createTestUser());
        when(userService.findAll()).thenReturn(users);
        
        // When & Then
        mockMvc.perform(get("/api/users"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$", hasSize(1)))
            .andExpect(jsonPath("$[0].name", is("张三")));
    }
}

// 数据层集成测试
@DataJpaTest
class UserRepositoryIntegrationTest {
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Autowired
    private UserRepository userRepository;
    
    @Test
    void shouldFindUserByEmail_whenEmailExists() {
        // Given
        User user = createTestUser();
        entityManager.persistAndFlush(user);
        
        // When
        Optional<User> found = userRepository.findByEmail(user.getEmail());
        
        // Then
        assertThat(found).isPresent();
        assertThat(found.get().getName()).isEqualTo(user.getName());
    }
}
```

### 12.4 数据库集成测试

#### TestContainers 进行真实数据库测试

```java
@SpringBootTest
@Testcontainers
class DatabaseIntegrationTest {
    
    @Container
    static MySQLContainer<?> mysql = new MySQLContainer<>("mysql:8.0")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");
    
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", mysql::getJdbcUrl);
        registry.add("spring.datasource.username", mysql::getUsername);
        registry.add("spring.datasource.password", mysql::getPassword);
    }
    
    @Autowired
    private UserRepository userRepository;
    
    @Test
    void shouldPersistUser_withComplexData() {
        // Given
        User user = User.builder()
            .name("张三")
            .email("<EMAIL>")
            .profile(Profile.builder()
                .address("北京市朝阳区")
                .phone("13800138000")
                .build())
            .build();
        
        // When
        User saved = userRepository.save(user);
        
        // Then
        assertThat(saved.getId()).isNotNull();
        
        User found = userRepository.findById(saved.getId()).orElseThrow();
        assertThat(found.getProfile().getAddress()).isEqualTo("北京市朝阳区");
    }
}
```

#### 事务测试

```java
@SpringBootTest
@Transactional
@Rollback
class TransactionIntegrationTest {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserRepository userRepository;
    
    @Test
    void shouldRollbackTransaction_whenExceptionOccurs() {
        // Given
        long initialCount = userRepository.count();
        
        // When & Then
        assertThrows(RuntimeException.class, () -> {
            userService.createUsersWithError(Arrays.asList(
                new CreateUserRequest("用户1", "<EMAIL>"),
                new CreateUserRequest("用户2", "<EMAIL>"),
                new CreateUserRequest("", "invalid") // 触发异常
            ));
        });
        
        // 验证事务回滚
        assertThat(userRepository.count()).isEqualTo(initialCount);
    }
}
```

### 12.5 外部服务集成测试

#### 使用 WireMock 模拟外部服务

```java
@SpringBootTest
@ExtendWith(WireMockExtension.class)
class ExternalServiceIntegrationTest {
    
    @RegisterExtension
    static WireMockExtension wireMock = WireMockExtension.newInstance()
        .options(wireMockConfig().port(8089))
        .build();
    
    @Autowired
    private PaymentService paymentService;
    
    @BeforeEach
    void setUp() {
        // 配置外部服务的基础URL
        ReflectionTestUtils.setField(paymentService, "paymentGatewayUrl", 
            "http://localhost:8089");
    }
    
    @Test
    void shouldProcessPayment_whenExternalServiceReturnsSuccess() {
        // Given
        wireMock.stubFor(post(urlEqualTo("/api/payment"))
            .withRequestBody(containing("amount"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("Content-Type", "application/json")
                .withBody("{\"status\":\"success\",\"transactionId\":\"txn123\"}")));
        
        PaymentRequest request = new PaymentRequest(100, "USD");
        
        // When
        PaymentResult result = paymentService.processPayment(request);
        
        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getTransactionId()).isEqualTo("txn123");
        
        // 验证外部服务调用
        wireMock.verify(postRequestedFor(urlEqualTo("/api/payment"))
            .withRequestBody(containing("\"amount\":100")));
    }
}
```

### 12.6 消息队列集成测试

#### 使用嵌入式消息队列

```java
@SpringBootTest
@EmbeddedKafka(partitions = 1, topics = {"user-events"})
class MessageQueueIntegrationTest {
    
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;
    
    @Autowired
    private UserEventListener userEventListener;
    
    @Test
    void shouldProcessUserEvent_whenMessageReceived() throws InterruptedException {
        // Given
        String userEvent = "{\"userId\":123,\"eventType\":\"USER_CREATED\"}";
        
        // When
        kafkaTemplate.send("user-events", userEvent);
        
        // Then
        // 等待消息处理完成
        Thread.sleep(1000);
        
        // 验证事件处理结果
        verify(userEventListener, timeout(2000)).handleUserCreated(any(UserCreatedEvent.class));
    }
}
```

### 12.7 缓存集成测试

```java
@SpringBootTest
@TestPropertySource(properties = {
    "spring.redis.host=localhost",
    "spring.redis.port=6379",
    "spring.cache.type=redis"
})
class CacheIntegrationTest {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Test
    void shouldCacheUser_whenUserIsRetrieved() {
        // Given
        Long userId = 1L;
        
        // When - 第一次调用
        User user1 = userService.getUser(userId);
        User user2 = userService.getUser(userId); // 第二次调用应该从缓存获取
        
        // Then
        assertThat(user1).isEqualTo(user2);
        
        // 验证缓存中存在数据
        String cacheKey = "user::" + userId;
        Object cachedUser = redisTemplate.opsForValue().get(cacheKey);
        assertThat(cachedUser).isNotNull();
    }
}
```

### 12.8 安全集成测试

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase
class SecurityIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void shouldDenyAccess_whenNoAuthenticationProvided() {
        // When
        ResponseEntity<String> response = restTemplate.getForEntity("/api/admin/users", String.class);
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
    }
    
    @Test
    @WithMockUser(roles = "ADMIN")
    void shouldAllowAccess_whenValidAdminUser() {
        // When
        ResponseEntity<String> response = restTemplate.getForEntity("/api/admin/users", String.class);
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
    }
}
```

### 12.9 性能集成测试

```java
@SpringBootTest
class PerformanceIntegrationTest {
    
    @Autowired
    private UserService userService;
    
    @Test
    @Timeout(value = 5, unit = TimeUnit.SECONDS)
    void shouldCompleteUserCreation_withinTimeLimit() {
        // Given
        List<CreateUserRequest> requests = IntStream.range(0, 1000)
            .mapToObj(i -> new CreateUserRequest("用户" + i, "user" + i + "@test.com"))
            .collect(Collectors.toList());
        
        // When
        long startTime = System.currentTimeMillis();
        List<User> users = userService.batchCreateUsers(requests);
        long endTime = System.currentTimeMillis();
        
        // Then
        assertThat(users).hasSize(1000);
        assertThat(endTime - startTime).isLessThan(5000); // 应在5秒内完成
    }
}
```

### 12.10 测试环境准备和清理

#### 数据库状态管理

```java
@SpringBootTest
@Sql(scripts = "/test-data.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
@Sql(scripts = "/cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
class DataDrivenIntegrationTest {
    
    @Autowired
    private UserRepository userRepository;
    
    @Test
    void shouldFindTestUsers_whenTestDataIsLoaded() {
        // Given - 测试数据通过 @Sql 注解加载
        
        // When
        List<User> users = userRepository.findAll();
        
        // Then
        assertThat(users).isNotEmpty();
        assertThat(users.stream().map(User::getName))
            .contains("测试用户1", "测试用户2");
    }
}
```

#### 测试配置文件
创建 `application-integration.yml` 用于集成测试：

```yaml
spring:
  profiles:
    active: integration
  datasource:
    url: jdbc:h2:mem:integrationdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop
```

### 12.11 持续集成中的集成测试

#### Maven 配置

```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-failsafe-plugin</artifactId>
    <configuration>
        <includes>
            <include>**/*IntegrationTest.java</include>
            <include>**/*IT.java</include>
        </includes>
        <systemPropertyVariables>
            <spring.profiles.active>integration</spring.profiles.active>
        </systemPropertyVariables>
    </configuration>
    <executions>
        <execution>
            <goals>
                <goal>integration-test</goal>
                <goal>verify</goal>
            </goals>
        </execution>
    </executions>
</plugin>
```

#### 分阶段测试执行

```bash
# 只运行单元测试
mvn test

# 运行集成测试
mvn verify -Dskip.unit.tests=true

# 运行所有测试
mvn verify

# 运行特定的集成测试
mvn failsafe:integration-test -Dit.test=UserIntegrationTest
```

## 13. 测试代码质量提升

### 13.1 测试代码可读性

良好的测试代码应当：
- 测试方法名称清楚描述测试场景和预期结果
- 使用有意义的变量名和常量
- 测试代码比生产代码更易读
- 避免使用魔法数字，使用常量替代

```java
// 不推荐
@Test
void test1() {
    UserService s = new UserService();
    assertEquals(200, s.calc(100, 0.5, 1, true));
}

// 推荐
@Test
void calculateFinalPrice_withVipDiscountAndPromotion_shouldApplyBothDiscounts() {
    // 准备测试数据
    UserService userService = new UserService();
    double basePrice = 100.0;
    double vipDiscountRate = 0.5;
    int promotionId = 1;
    boolean usePoints = true;
    
    // 预期结果
    double expectedFinalPrice = 200.0;
    
    // 执行测试
    double actualPrice = userService.calculatePrice(basePrice, vipDiscountRate, 
                                                 promotionId, usePoints);
    
    // 验证结果
    assertEquals(expectedFinalPrice, actualPrice);
}
```

### 13.2 测试独立性

- 每个测试应该独立运行，不依赖其他测试的结果
- 测试之间不应该有执行顺序依赖
- 使用 `@BeforeEach` 和 `@AfterEach` 进行测试环境准备和清理

```java
@BeforeEach
void setUp() {
    // 每个测试前的准备工作
    testData = createTestData();
    mockitoReset(mockService);
}

@AfterEach
void tearDown() {
    // 每个测试后的清理工作
    cleanupTestData();
}
```

### 13.3 高质量的测试数据管理

#### 测试数据原则
- **真实性**：测试数据应该接近真实业务数据
- **最小化**：只创建测试所需的最少数据
- **隔离性**：测试数据之间互不影响
- **可维护性**：测试数据易于理解和修改

#### 测试数据构建器模式

```java
public class UserTestDataBuilder {
    private String name = "defaultName";
    private String email = "<EMAIL>";
    private Integer age = 25;
    
    public UserTestDataBuilder withName(String name) {
        this.name = name;
        return this;
    }
    
    public UserTestDataBuilder withEmail(String email) {
        this.email = email;
        return this;
    }
    
    public User build() {
        return new User(name, email, age);
    }
    
    public static UserTestDataBuilder aUser() {
        return new UserTestDataBuilder();
    }
}

// 使用示例
@Test
void validateUser_withValidData_shouldPassValidation() {
    // Given
    User testUser = UserTestDataBuilder.aUser()
        .withName("张三")
        .withEmail("<EMAIL>")
        .build();
    
    // When
    boolean isValid = userValidator.validate(testUser);
    
    // Then
    assertTrue(isValid);
}
```

### 13.4 断言最佳实践

#### 使用 AssertJ 进行流畅断言

```java
// 引入依赖
// <dependency>
//     <groupId>org.assertj</groupId>
//     <artifactId>assertj-core</artifactId>
//     <scope>test</scope>
// </dependency>

// 集合断言
assertThat(userList)
    .hasSize(3)
    .extracting(User::getName)
    .containsExactly("张三", "李四", "王五");

// 对象断言
assertThat(user)
    .hasFieldOrPropertyWithValue("name", "张三")
    .hasFieldOrPropertyWithValue("age", 30)
    .satisfies(u -> {
        assertThat(u.getEmail()).endsWith("@test.com");
        assertThat(u.isActive()).isTrue();
    });

// 异常断言
assertThatThrownBy(() -> userService.createUser(null))
    .isInstanceOf(IllegalArgumentException.class)
    .hasMessageContaining("用户名不能为空");
```

#### 自定义断言类

```java
public class UserAssert extends AbstractAssert<UserAssert, User> {
    
    public UserAssert(User user) {
        super(user, UserAssert.class);
    }
    
    public static UserAssert assertThat(User actual) {
        return new UserAssert(actual);
    }
    
    public UserAssert hasValidEmail() {
        isNotNull();
        if (!actual.getEmail().contains("@")) {
            failWithMessage("Expected user to have valid email but was <%s>", actual.getEmail());
        }
        return this;
    }
    
    public UserAssert isAdult() {
        isNotNull();
        if (actual.getAge() < 18) {
            failWithMessage("Expected user to be adult but age was <%s>", actual.getAge());
        }
        return this;
    }
}

// 使用示例
@Test
void createUser_withValidData_shouldCreateValidUser() {
    // Given
    CreateUserRequest request = new CreateUserRequest("张三", "<EMAIL>", 20);
    
    // When
    User user = userService.createUser(request);
    
    // Then
    UserAssert.assertThat(user)
        .hasValidEmail()
        .isAdult();
}
```

### 13.5 参数化测试

使用 JUnit 5 参数化测试减少重复代码，提高测试覆盖率：

```java
@ParameterizedTest
@ValueSource(strings = {"", " ", "  "})
void validateName_whenNameIsBlank_shouldReturnFalse(String name) {
    // When
    boolean isValid = validator.validateName(name);
    
    // Then
    assertFalse(isValid);
}

@ParameterizedTest
@CsvSource({
    "1, 10, 11",
    "2, 20, 22", 
    "3, 30, 33"
})
void calculateTotal_withDifferentValues_shouldReturnCorrectSum(
        int quantity, double price, double expected) {
    // When
    BigDecimal result = calculator.calculateTotal(quantity, BigDecimal.valueOf(price));
    
    // Then
    assertThat(result).isEqualTo(BigDecimal.valueOf(expected));
}

@ParameterizedTest
@MethodSource("provideDates")
void isValidBusinessDate_withVariousDates_shouldReturnExpectedResult(
        LocalDate date, boolean expected) {
    // When
    boolean result = dateValidator.isValidBusinessDate(date);
    
    // Then
    assertThat(result).isEqualTo(expected);
}

private static Stream<Arguments> provideDates() {
    return Stream.of(
        Arguments.of(LocalDate.of(2024, 1, 1), false), // 元旦
        Arguments.of(LocalDate.of(2024, 1, 2), true),  // 工作日
        Arguments.of(LocalDate.of(2024, 1, 6), false)  // 周六
    );
}
```

### 13.6 Mock 对象最佳实践

#### Mock 对象的合理使用
- 只 Mock 必要的依赖，避免过度 Mock
- Mock 对象应该模拟真实依赖的行为
- 明确定义所有必要的行为

```java
@ExtendWith(MockitoExtension.class)
class PaymentServiceTest {
    
    @Mock
    private PaymentGateway paymentGateway;
    
    @Mock
    private NotificationService notificationService;
    
    @InjectMocks
    private PaymentService paymentService;
    
    @Test
    void processPayment_whenValidRequest_shouldSucceed() {
        // Given
        PaymentRequest request = new PaymentRequest(100, "USD");
        when(paymentGateway.charge(any())).thenReturn(PaymentResult.success("txn123"));
        
        // When
        PaymentResponse response = paymentService.processPayment(request);
        
        // Then
        assertThat(response.isSuccess()).isTrue();
        verify(notificationService).sendSuccessNotification(eq("txn123"));
        verify(paymentGateway, times(1)).charge(any());
        verifyNoMoreInteractions(notificationService);
    }
}
```

#### 验证 Mock 交互与参数捕获

```java
@Test
void sendWelcomeEmail_whenUserRegistered_shouldSendCorrectEmail() {
    // Given
    User user = createTestUser();
    
    // When
    userService.sendWelcomeEmail(user);
    
    // Then - 验证方法调用
    verify(emailService).sendEmail(
        eq(user.getEmail()),
        eq("欢迎注册"),
        argThat(content -> content.contains(user.getName()))
    );
    
    // 参数捕获
    ArgumentCaptor<String> contentCaptor = ArgumentCaptor.forClass(String.class);
    verify(emailService).sendEmail(eq(user.getEmail()), eq("欢迎注册"), contentCaptor.capture());
    String emailContent = contentCaptor.getValue();
    
    assertThat(emailContent)
        .contains(user.getName())
        .contains("感谢您的注册");
}
```

### 13.7 异常和边界情况测试

全面的异常和边界条件测试对于构建健壮的系统至关重要：

```java
@Test
void calculateDiscount_withNegativeAmount_shouldThrowException() {
    // Given
    double negativeAmount = -100.0;
    
    // When & Then
    assertThrows(IllegalArgumentException.class, () -> {
        discountService.calculateDiscount(negativeAmount);
    });
}

@ParameterizedTest
@ValueSource(ints = {0, 1, 99, 100, 101, Integer.MAX_VALUE})
void validateQuantity_atBoundaries_shouldBehaveCorrectly(int quantity) {
    if (quantity <= 0 || quantity > 100) {
        assertFalse(validator.isValidQuantity(quantity));
    } else {
        assertTrue(validator.isValidQuantity(quantity));
    }
}

@Test
void processOrder_whenNetworkFails_shouldRetryAndThenFail() {
    // Given
    when(paymentGateway.processPayment(any()))
        .thenThrow(new NetworkException("Connection timeout"))
        .thenThrow(new NetworkException("Connection timeout"))
        .thenThrow(new NetworkException("Connection timeout"));
    
    // When & Then
    assertThatThrownBy(() -> orderService.processOrder(createTestOrder()))
        .isInstanceOf(ServiceException.class)
        .hasMessageContaining("支付服务不可用");
    
    verify(paymentGateway, times(3)).processPayment(any());
    verify(notificationService).sendFailureNotification(any());
}
```

### 13.8 测试环境配置

#### 测试配置文件
创建专用的测试配置文件 `application-test.yml`：

```yaml
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: password
    driver-class-name: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop

logging:
  level:
    org.hibernate.SQL: DEBUG
    com.anytech: DEBUG
```

#### 测试切片注解选择

```java
// Web层测试 - 只加载Controller和相关组件
@WebMvcTest(UserController.class)
class UserControllerTest {
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private UserService userService;
}

// 数据层测试 - 只加载JPA相关组件
@DataJpaTest
class UserRepositoryTest {
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private TestEntityManager entityManager;
}
```

### 13.9 测试代码重构

#### 消除测试代码重复

```java
// 测试基类
public abstract class BaseServiceTest {
    
    protected User createTestUser(String name, String email) {
        return User.builder()
            .name(name)
            .email(email)
            .createdAt(LocalDateTime.now())
            .build();
    }
    
    protected void assertUserEquals(User expected, User actual) {
        assertThat(actual)
            .usingRecursiveComparison()
            .ignoringFields("id", "createdAt", "updatedAt")
            .isEqualTo(expected);
    }
}

// 具体测试类继承基类
class UserServiceTest extends BaseServiceTest {
    // 测试方法可以使用基类的工具方法
    @Test
    void findByName_withExistingName_shouldReturnUser() {
        // Given
        User expectedUser = createTestUser("张三", "<EMAIL>");
        
        // When & Then
        // ...
    }
}
```

#### 测试工具类

```java
public class TestDataFactory {
    
    public static User createUser() {
        return createUser("默认用户", "<EMAIL>");
    }
    
    public static User createUser(String name, String email) {
        return User.builder()
            .name(name)
            .email(email)
            .age(25)
            .build();
    }
    
    public static List<User> createUsers(int count) {
        return IntStream.range(0, count)
            .mapToObj(i -> createUser("用户" + i, "user" + i + "@test.com"))
            .collect(Collectors.toList());
    }
}
```

### 13.10 测试报告和度量

#### 生成详细的测试报告

在 `pom.xml` 中配置测试报告插件：

```xml
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <executions>
        <execution>
            <goals>
                <goal>prepare-agent</goal>
            </goals>
        </execution>
        <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
                <goal>report</goal>
            </goals>
        </execution>
        <execution>
            <id>check</id>
            <goals>
                <goal>check</goal>
            </goals>
            <configuration>
                <rules>
                    <rule>
                        <element>BUNDLE</element>
                        <limits>
                            <limit>
                                <counter>INSTRUCTION</counter>
                                <value>COVEREDRATIO</value>
                                <minimum>0.80</minimum>
                            </limit>
                        </limits>
                    </rule>
                </rules>
            </configuration>
        </execution>
    </executions>
</plugin>
```

#### 测试度量标准
- **覆盖率**：代码覆盖率应达到80%以上
- **测试速度**：单元测试应在毫秒级完成
- **测试稳定性**：测试成功率应达到99%以上
- **测试维护性**：测试代码与生产代码比例应控制在1:1到1:2之间

```java
// 覆盖率不足检查示例
@Test
void verifyCodeCoverageOfCriticalMethod() {
    // 检查关键方法是否有足够的测试覆盖
    // 此测试仅为示例，实际覆盖率由Jacoco等工具衡量
    assertTrue(hasSufficientTestsFor("processPayment"));
}
```

## 14. Spring Boot项目单元测试规范

### 14.1 测试文件组织与命名约定

#### 测试文件组织
- 测试类应放在 `src/test/java` 目录下
- 测试类包结构应与被测试类的包结构保持一致
- 测试资源文件应放在 `src/test/resources` 目录下

```
src/
├── main/
│   ├── java/
│   │   └── com/anytech/authorization/
│   │       ├── controller/
│   │       ├── service/
│   │       └── repository/
│   └── resources/
└── test/
    ├── java/
    │   └── com/anytech/authorization/
    │       ├── controller/
    │       ├── service/
    │       └── repository/
    └── resources/
        ├── application-test.yml
        ├── test-data.sql
        └── cleanup.sql
```

#### 测试命名约定
- 测试类命名格式：`{被测试类名}Test`
- 测试方法命名格式：`should{期望结果}_when{条件}`

```java
// 被测试类：src/main/java/com/anytech/authorization/service/UserService.java
// 测试类：src/test/java/com/anytech/authorization/service/UserServiceTest.java

@Test
void shouldReturnUser_whenValidIdProvided() {
    // 测试逻辑
}

@Test
void shouldThrowException_whenUserNotFound() {
    // 测试逻辑
}
```

### 14.2 Spring Boot测试类型与注解

#### 测试类型分类
1. **单元测试**：测试单个组件的功能，通常使用`@ExtendWith(MockitoExtension.class)`
2. **切片测试**：测试特定层的功能，如`@WebMvcTest`、`@DataJpaTest`等
3. **集成测试**：测试多个组件的交互，通常使用`@SpringBootTest`

#### Spring Boot测试注解
```java
// 完整应用程序上下文测试
@SpringBootTest
class UserServiceIntegrationTest {
    @Autowired
    private UserService userService;
}

// Web层测试 - 只加载Controller和相关组件
@WebMvcTest(UserController.class)
class UserControllerTest {
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private UserService userService;
}

// 数据层测试 - 只加载JPA相关组件
@DataJpaTest
class UserRepositoryTest {
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private TestEntityManager entityManager;
}

// JSON序列化测试
@JsonTest
class UserDtoTest {
    @Autowired
    private JacksonTester<UserDto> json;
}

// JDBC测试
@JdbcTest
class JdbcUserRepositoryTest {
    @Autowired
    private JdbcTemplate jdbcTemplate;
}
```

### 14.3 必需的测试依赖

确保在 `pom.xml` 中包含以下测试依赖：

```xml
<!-- Spring Boot Test -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>

<!-- TestContainers -->
<dependency>
    <groupId>org.testcontainers</groupId>
    <artifactId>junit-jupiter</artifactId>
    <scope>test</scope>
</dependency>

<!-- TestContainers MySQL 依赖 (如果使用MySQL) -->
<dependency>
    <groupId>org.testcontainers</groupId>
    <artifactId>mysql</artifactId>
    <scope>test</scope>
</dependency>

<!-- 流畅断言库 -->
<dependency>
    <groupId>org.assertj</groupId>
    <artifactId>assertj-core</artifactId>
    <scope>test</scope>
</dependency>

<!-- Rest Assured (可选，用于API测试) -->
<dependency>
    <groupId>io.rest-assured</groupId>
    <artifactId>rest-assured</artifactId>
    <scope>test</scope>
</dependency>
```

### 14.4 Spring Boot测试最佳实践

#### 1. 严格遵循AAA模式
每个测试方法应遵循Arrange-Act-Assert模式：

```java
@Test
void shouldCalculateDiscount_whenCustomerIsVip() {
    // Arrange - 准备测试数据
    Customer vipCustomer = new Customer("张三", CustomerType.VIP);
    Order order = new Order(vipCustomer, new BigDecimal("100.00"));
    
    // Act - 执行被测试的方法
    BigDecimal discountedPrice = discountService.calculateDiscountedPrice(order);
    
    // Assert - 验证结果
    assertThat(discountedPrice).isEqualTo(new BigDecimal("80.00"));
}
```

#### 2. 使用测试属性文件

为测试创建专用的配置文件 `src/test/resources/application-test.yml`：

```yaml
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: password
    driver-class-name: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop
```

在测试类中指定使用测试配置文件：

```java
@SpringBootTest
@ActiveProfiles("test")
class UserServiceIntegrationTest {
    // 测试内容
}
```

#### 3. 使用适当的Mock策略

- **纯单元测试**：使用Mockito创建隔离的测试环境
- **Spring Bean测试**：使用`@MockBean`替换Spring上下文中的Bean

```java
// 纯单元测试
@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    @Mock
    private UserRepository userRepository;
    
    @InjectMocks
    private UserService userService;
}

// Spring Bean测试
@SpringBootTest
class UserServiceWithMockBeanTest {
    @MockBean
    private UserRepository userRepository;
    
    @Autowired
    private UserService userService;
}
```

#### 4. 使用Spring测试工具类

- 使用`TestRestTemplate`测试REST接口
- 使用`MockMvc`测试MVC控制器
- 使用`TestEntityManager`测试JPA操作

```java
// 使用TestRestTemplate
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class UserControllerIntegrationTest {
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void shouldReturnUser_whenGetUserById() {
        ResponseEntity<UserDto> response = restTemplate.getForEntity(
            "/api/users/1", UserDto.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getName()).isEqualTo("张三");
    }
}

// 使用MockMvc
@WebMvcTest(UserController.class)
class UserControllerTest {
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private UserService userService;
    
    @Test
    void shouldCreateUser_whenValidDataProvided() throws Exception {
        // Given
        UserDto userDto = new UserDto(null, "张三", "<EMAIL>");
        UserDto savedUser = new UserDto(1L, "张三", "<EMAIL>");
        
        when(userService.createUser(any(UserDto.class))).thenReturn(savedUser);
        
        // When & Then
        mockMvc.perform(post("/api/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(new ObjectMapper().writeValueAsString(userDto)))
            .andExpect(status().isCreated())
            .andExpect(jsonPath("$.id").value(1))
            .andExpect(jsonPath("$.name").value("张三"))
            .andExpect(jsonPath("$.email").value("<EMAIL>"));
        
        // 验证服务调用
        verify(userService).createUser(any(UserDto.class));
    }
    
    @Test
    void shouldReturnBadRequest_whenInvalidDataProvided() throws Exception {
        // Given
        UserDto invalidUser = new UserDto(null, "", "invalid-email");
        
        // When & Then
        mockMvc.perform(post("/api/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(new ObjectMapper().writeValueAsString(invalidUser)))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.errors").isNotEmpty());
        
        // 验证服务未调用
        verify(userService, never()).createUser(any(UserDto.class));
    }
}
```

#### 请求参数校验测试

```java
@WebMvcTest(UserController.class)
class UserControllerValidationTest {
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private UserService userService;
    
    @Test
    void shouldValidateRequestParameters() throws Exception {
        // 超出范围的分页参数
        mockMvc.perform(get("/api/users")
                .param("page", "-1")
                .param("size", "1000"))
            .andExpect(status().isBadRequest());
        
        // 有效的分页参数
        mockMvc.perform(get("/api/users")
                .param("page", "0")
                .param("size", "10"))
            .andExpect(status().isOk());
    }
    
    @Test
    void shouldValidatePathVariables() throws Exception {
        // 无效的路径变量
        mockMvc.perform(get("/api/users/invalid-id"))
            .andExpect(status().isBadRequest());
        
        // 有效的路径变量
        when(userService.getUserById(1L)).thenReturn(new UserDto(1L, "张三", "<EMAIL>"));
        mockMvc.perform(get("/api/users/1"))
            .andExpect(status().isOk());
    }
}
```

### 14.10 服务层测试最佳实践

#### 业务逻辑测试

```java
@ExtendWith(MockitoExtension.class)
class OrderServiceTest {
    @Mock
    private OrderRepository orderRepository;
    
    @Mock
    private PaymentService paymentService;
    
    @Mock
    private NotificationService notificationService;
    
    @InjectMocks
    private OrderService orderService;
    
    @Test
    void shouldPlaceOrder_whenAllConditionsMet() {
        // Given
        OrderRequest request = new OrderRequest("产品A", 2, new BigDecimal("100.00"));
        Order savedOrder = Order.builder()
            .id(1L)
            .productName("产品A")
            .quantity(2)
            .amount(new BigDecimal("200.00"))
            .status(OrderStatus.CREATED)
            .build();
        
        when(orderRepository.save(any(Order.class))).thenReturn(savedOrder);
        when(paymentService.processPayment(any(PaymentRequest.class))).thenReturn(true);
        
        // When
        OrderResult result = orderService.placeOrder(request);
        
        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getOrderId()).isEqualTo(1L);
        
        // 验证调用
        verify(orderRepository).save(any(Order.class));
        verify(paymentService).processPayment(any(PaymentRequest.class));
        verify(notificationService).sendOrderConfirmation(eq(1L));
    }
    
    @Test
    void shouldFailOrder_whenPaymentFails() {
        // Given
        OrderRequest request = new OrderRequest("产品A", 2, new BigDecimal("100.00"));
        Order savedOrder = Order.builder()
            .id(1L)
            .productName("产品A")
            .quantity(2)
            .amount(new BigDecimal("200.00"))
            .status(OrderStatus.CREATED)
            .build();
        
        when(orderRepository.save(any(Order.class))).thenReturn(savedOrder);
        when(paymentService.processPayment(any(PaymentRequest.class))).thenReturn(false);
        
        // When
        OrderResult result = orderService.placeOrder(request);
        
        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getErrorMessage()).contains("支付失败");
        
        // 验证调用
        verify(orderRepository).save(any(Order.class));
        verify(orderRepository).updateStatus(eq(1L), eq(OrderStatus.PAYMENT_FAILED));
        verify(paymentService).processPayment(any(PaymentRequest.class));
        verify(notificationService, never()).sendOrderConfirmation(any());
    }
}
```

#### 事务测试

```java
@SpringBootTest
@Transactional
class OrderServiceTransactionTest {
    @Autowired
    private OrderService orderService;
    
    @Autowired
    private OrderRepository orderRepository;
    
    @Test
    void shouldRollbackTransaction_whenExceptionOccurs() {
        // Given
        long initialCount = orderRepository.count();
        OrderRequest request = new OrderRequest("", 0, new BigDecimal("-100.00")); // 无效数据
        
        // When & Then
        assertThrows(ValidationException.class, () -> {
            orderService.placeOrder(request);
        });
        
        // 验证事务回滚
        assertThat(orderRepository.count()).isEqualTo(initialCount);
    }
}
```

### 14.11 存储库层测试最佳实践

#### JPA查询测试

```java
@DataJpaTest
class UserRepositoryTest {
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Test
    void shouldFindUsersByRoleAndStatus() {
        // Given
        User user1 = User.builder()
            .name("张三")
            .email("<EMAIL>")
            .role(Role.USER)
            .status(UserStatus.ACTIVE)
            .build();
        
        User user2 = User.builder()
            .name("李四")
            .email("<EMAIL>")
            .role(Role.ADMIN)
            .status(UserStatus.ACTIVE)
            .build();
        
        User user3 = User.builder()
            .name("王五")
            .email("<EMAIL>")
            .role(Role.USER)
            .status(UserStatus.INACTIVE)
            .build();
        
        entityManager.persist(user1);
        entityManager.persist(user2);
        entityManager.persist(user3);
        entityManager.flush();
        
        // When
        List<User> activeUsers = userRepository.findByRoleAndStatus(Role.USER, UserStatus.ACTIVE);
        
        // Then
        assertThat(activeUsers).hasSize(1);
        assertThat(activeUsers.get(0).getName()).isEqualTo("张三");
    }
    
    @Test
    void shouldFindUsersByPartialName() {
        // Given
        User user1 = User.builder().name("张三丰").email("<EMAIL>").build();
        User user2 = User.builder().name("张无忌").email("<EMAIL>").build();
        User user3 = User.builder().name("李四").email("<EMAIL>").build();
        
        entityManager.persist(user1);
        entityManager.persist(user2);
        entityManager.persist(user3);
        entityManager.flush();
        
        // When
        List<User> usersWithZhang = userRepository.findByNameContaining("张");
        
        // Then
        assertThat(usersWithZhang).hasSize(2);
        assertThat(usersWithZhang).extracting(User::getName)
            .containsExactlyInAnyOrder("张三丰", "张无忌");
    }
}
```

### 14.12 数据库测试

#### 1. 使用内存数据库进行测试

```java
@DataJpaTest
class UserRepositoryTest {
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Test
    void shouldFindUserByEmail() {
        // Given
        User user = User.builder()
            .name("张三")
            .email("<EMAIL>")
            .build();
        entityManager.persist(user);
        entityManager.flush();
        
        // When
        Optional<User> found = userRepository.findByEmail("<EMAIL>");
        
        // Then
        assertThat(found).isPresent();
        assertThat(found.get().getName()).isEqualTo("张三");
    }
}
```

#### 2. 使用SQL脚本初始化和清理测试数据

```java
@DataJpaTest
@Sql(scripts = "/test-data.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
@Sql(scripts = "/cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
class UserRepositoryWithSqlTest {
    @Autowired
    private UserRepository userRepository;
    
    @Test
    void shouldFindAllUsers() {
        // When
        List<User> users = userRepository.findAll();
        
        // Then
        assertThat(users).hasSize(2);
        assertThat(users).extracting(User::getName)
            .containsExactly("张三", "李四");
    }
}
```

#### 3. 使用TestContainers进行真实数据库测试

```java
@SpringBootTest
@Testcontainers
class RealDatabaseTest {
    @Container
    static MySQLContainer<?> mySQLContainer = new MySQLContainer<>("mysql:8.0")
        .withDatabaseName("testdb")
        .withUsername("test")
        .withPassword("test");
        
    @DynamicPropertySource
    static void setProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", mySQLContainer::getJdbcUrl);
        registry.add("spring.datasource.username", mySQLContainer::getUsername);
        registry.add("spring.datasource.password", mySQLContainer::getPassword);
    }
    
    @Autowired
    private UserRepository userRepository;
    
    @Test
    void shouldSaveAndRetrieveUser() {
        // Given
        User user = User.builder()
            .name("张三")
            .email("<EMAIL>")
            .build();
        
        // When
        User savedUser = userRepository.save(user);
        
        // Then
        assertThat(savedUser.getId()).isNotNull();
        
        User retrievedUser = userRepository.findById(savedUser.getId()).orElseThrow();
        assertThat(retrievedUser.getName()).isEqualTo("张三");
    }
}
```

### 14.13 测试覆盖率要求

遵循以下测试覆盖率指标：
- 新功能代码测试覆盖率应达到 **80%** 以上
- 关键业务逻辑测试覆盖率应达到 **90%** 以上
- 边界条件和异常路径必须测试全面

配置JaCoCo生成测试覆盖率报告：

```xml
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <executions>
        <execution>
            <goals>
                <goal>prepare-agent</goal>
            </goals>
        </execution>
        <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
                <goal>report</goal>
            </goals>
        </execution>
        <execution>
            <id>check</id>
            <goals>
                <goal>check</goal>
            </goals>
            <configuration>
                <rules>
                    <rule>
                        <element>BUNDLE</element>
                        <limits>
                            <limit>
                                <counter>INSTRUCTION</counter>
                                <value>COVEREDRATIO</value>
                                <minimum>0.80</minimum>
                            </limit>
                        </limits>
                    </rule>
                </rules>
            </configuration>
        </execution>
    </executions>
</plugin>
```

### 14.14 持续集成测试

#### Maven测试命令

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=UserServiceTest

# 运行测试并生成覆盖率报告
mvn clean test jacoco:report

# 运行集成测试
mvn failsafe:integration-test
```

#### 测试分类

使用JUnit 5的`@Tag`注解对测试进行分类：

```java
@Test
@Tag("unit")
void shouldProcessPayment_whenValidInput() {
    // 单元测试
}

@Test
@Tag("integration")
void shouldProcessFullPaymentFlow() {
    // 集成测试
}

@Test
@Tag("slow")
void shouldHandleLargeDataset() {
    // 耗时测试
}
```

在Maven中配置运行特定标签的测试：

```xml
<plugin>
    <artifactId>maven-surefire-plugin</artifactId>
    <configuration>
        <groups>unit | integration</groups>
        <excludedGroups>slow</excludedGroups>
    </configuration>
</plugin>
```

### 14.15 安全测试

#### 使用Spring Security测试支持

```java
@WebMvcTest(UserController.class)
class UserControllerSecurityTest {
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private UserService userService;
    
    @Test
    @WithMockUser(roles = "USER")
    void shouldAllowAccess_whenUserHasUserRole() throws Exception {
        mockMvc.perform(get("/api/users/profile"))
            .andExpect(status().isOk());
    }
    
    @Test
    @WithAnonymousUser
    void shouldDenyAccess_whenUserIsAnonymous() throws Exception {
        mockMvc.perform(get("/api/users/profile"))
            .andExpect(status().isUnauthorized());
    }
    
    @Test
    @WithMockUser(roles = "ADMIN")
    void shouldAllowAccess_whenUserHasAdminRole() throws Exception {
        mockMvc.perform(get("/api/admin/users"))
            .andExpect(status().isOk());
    }
}
```

#### 使用JWT Token测试

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class JwtAuthenticationTest {
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private JwtTokenProvider tokenProvider;
    
    @Test
    void shouldAllowAccess_whenValidTokenProvided() {
        // 创建有效Token
        String token = tokenProvider.createToken("user1", Collections.singletonList("ROLE_USER"));
        
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        
        // 发送请求
        ResponseEntity<String> response = restTemplate.exchange(
            "/api/users/profile", HttpMethod.GET, entity, String.class);
        
        // 验证结果
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
    }
}
```

### 14.16 控制器测试最佳实践

#### REST API测试

```java
@WebMvcTest(UserController.class)
class UserControllerTest {
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private UserService userService;
    
    @Test
    void shouldCreateUser_whenValidDataProvided() throws Exception {
        // Given
        UserDto userDto = new UserDto(null, "张三", "<EMAIL>");
        UserDto savedUser = new UserDto(1L, "张三", "<EMAIL>");
        
        when(userService.createUser(any(UserDto.class))).thenReturn(savedUser);
        
        // When & Then
        mockMvc.perform(post("/api/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(new ObjectMapper().writeValueAsString(userDto)))
            .andExpect(status().isCreated())
            .andExpect(jsonPath("$.id").value(1))
            .andExpect(jsonPath("$.name").value("张三"))
            .andExpect(jsonPath("$.email").value("<EMAIL>"));
        
        // 验证服务调用
        verify(userService).createUser(any(UserDto.class));
    }
    
    @Test
    void shouldReturnBadRequest_whenInvalidDataProvided() throws Exception {
        // Given
        UserDto invalidUser = new UserDto(null, "", "invalid-email");
        
        // When & Then
        mockMvc.perform(post("/api/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(new ObjectMapper().writeValueAsString(invalidUser)))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.errors").isNotEmpty());
        
        // 验证服务未调用
        verify(userService, never()).createUser(any(UserDto.class));
    }
}
```









