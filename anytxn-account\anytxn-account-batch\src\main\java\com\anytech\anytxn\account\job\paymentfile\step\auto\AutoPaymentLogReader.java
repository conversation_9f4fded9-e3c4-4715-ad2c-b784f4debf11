package com.anytech.anytxn.account.job.paymentfile.step.auto;

import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.business.dao.account.model.AutoPaymentLog;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.database.JdbcPagingItemReader;
import org.springframework.batch.item.database.Order;
import org.springframework.batch.item.database.PagingQueryProvider;
import org.springframework.batch.item.database.support.SqlPagingQueryProviderFactoryBean;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import javax.sql.DataSource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/7
 */
public class AutoPaymentLogReader extends JdbcPagingItemReader<AutoPaymentLog> {

    private static final Logger logger = LoggerFactory.getLogger(AutoPaymentLogReader.class);

    public AutoPaymentLogReader(DataSource dataSource) {
        super();
        this.setRowMapper(new BeanPropertyRowMapper<>(AutoPaymentLog.class));
        this.setQueryProvider(pagingQueryProvider(dataSource));
    }

    private PagingQueryProvider pagingQueryProvider(DataSource dataSource) {
        SqlPagingQueryProviderFactoryBean providerFactoryBean = new SqlPagingQueryProviderFactoryBean();
        providerFactoryBean.setSelectClause("AUTO_PAYMENT_ID,ACCOUNT_MANAGEMENT_ID,ORIGINAL_PAYMENT_AMOUNT,   ORGANIZATION_NUMBER, AUTO_PAYMENT_DEBIT_BANK_NUMBER,EXCHANGE_RATE,EXCHANGE_PURCHASING_INDICATOR,PARTITION_KEY, UPDATE_TIME,VERSION_NUMBER, UPDATE_BY, AUTO_PAYMENT_OTHER_ACCT_NUMBER," +
                "AUTO_PAYMENT_OTHER_BRANCH,AUTO_PAYMENT_OTHER_BANK_NUMBER,CUSTOMER_ID,   AUTO_PAYMENT_BRANCH_NUMBER, CREATE_TIME,AUTO_PAYMENT_DEBIT_ACCT_NUMBER," +
                "FINAL_PAYMENT_AMOUNT,ORIGINAL_CURRENCY,TRN_DATE,CARD_NBR,ID_TYPE,ID_NUMBER,ACCT_TYPE,CUSTOMER_NAME");
        providerFactoryBean.setFromClause("AUTO_PAYMENT_LOG");

        // 只查今日未处理
        providerFactoryBean.setWhereClause("ORGANIZATION_NUMBER = " + OrgNumberUtils.getOrg() + " and OUT_FILE = '0'");
        Map<String, Order> sortKey = new HashMap<>(4);
        sortKey.put("AUTO_PAYMENT_ID", Order.DESCENDING);
        providerFactoryBean.setSortKeys(sortKey);
        providerFactoryBean.setDataSource(dataSource);
        try {
            return  providerFactoryBean.getObject();
        } catch (Exception e) {
            logger.error("PagingQueryProvider exception", e);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR, AccountingRepDetailEnum.NULL, "PagingQueryProvider");
        }
    }

    private String getNow(){
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime last = now.minusDays(1);
        last = LocalDateTime.of(last.getYear(), last.getMonth(), last.getDayOfMonth(), 0, 0, 0);
        logger.info("Processing deduction file date: {}", last);

        return last.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

}
