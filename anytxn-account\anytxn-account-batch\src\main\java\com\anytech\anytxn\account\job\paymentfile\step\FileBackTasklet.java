package com.anytech.anytxn.account.job.paymentfile.step;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 文件备份
 * <AUTHOR>
 * @date 2021/1/8
 */
public class FileBackTasklet implements Tasklet {

    private static final Logger logger = LoggerFactory.getLogger(FileBackTasklet.class);

    public FileBackTasklet(String formPath, String toPath) {
        this.formPath = formPath;
        this.toPath = toPath;
    }

    private String formPath;

    private String toPath;

    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) throws Exception {
        if (StringUtils.isNotEmpty(formPath) && StringUtils.isNotEmpty(toPath)) {
            // 目标文件
            File fromFile = new File(formPath);

            try {
                // 文件复制
                if (fromFile.exists() && fromFile.isFile()) {
                    FileUtils.copyFile(fromFile, new File(getToPath(toPath)));
                // 文件夹复制
                } else if (fromFile.isDirectory()){
                    FileUtils.copyDirectory(fromFile, new File(getToPath(toPath)));
                }
            } catch (IOException e) {
                logger.error("File backup failed: {}", e.getMessage(), e);
            }

            logger.info("File backup completed, source path: {}, backup path: {}", formPath, toPath);
        }

        return RepeatStatus.FINISHED;
    }

    private String getToPath(String toPath) {
        return toPath + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    }
}
