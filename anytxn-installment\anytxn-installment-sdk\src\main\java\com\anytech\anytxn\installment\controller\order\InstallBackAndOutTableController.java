package com.anytech.anytxn.installment.controller.order;

import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.service.IInstallOrderProcessService;
import com.anytech.anytxn.parameter.base.common.service.system.ISystemTableService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SystemTableDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2019-12-04
 */
@RestController
@Api(tags = "ABS申请功能 回退/出表/终止")
public class InstallBackAndOutTableController extends BizBaseController {

    private static final Logger logger = LoggerFactory.getLogger(InstallBackAndOutTableController.class);

    @Autowired
    private IInstallOrderProcessService installOrderProcessService;
    @Autowired
    private ISystemTableService systemTableService;

    @ApiOperation(value = "回退", notes = "回退")
    @GetMapping("/install/back")
    public AnyTxnHttpResponse<Boolean> back(@RequestParam(value = "orderId", required = false) String orderId,
                                            @RequestParam(value = "absType", required = false) String absType,
                                            @RequestParam(value = "accountManagementId", required = false) String accountManagementId) {

        logger.info("Starting install back operation: orderId={}, absType={}, accountManagementId={}", orderId, absType, accountManagementId);
        SystemTableDTO systemTableResDTO = systemTableService.findBySystemId("0000");
        String batchMode = systemTableResDTO.getBatchMode();
        if (!"0".equals(batchMode)) {
            logger.error("Batch mode not supported for back operation: batchMode={}", batchMode);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_BATCH_NOT_SUPPORT_BACK_FAULT);
        }
        installOrderProcessService.back(orderId, accountManagementId, absType);
        logger.info("Install back operation completed successfully: orderId={}", orderId);
        return AnyTxnHttpResponse.success();
    }

    @ApiOperation(value = "出表", notes = "出表")
    @GetMapping("/install/outTable")
    public AnyTxnHttpResponse<Boolean> outTable(@RequestParam(value = "orderId", required = false) String orderId,
                                                @RequestParam(value = "absType", required = false) String absType,
                                                @RequestParam(value = "accountManagementId", required = false) String accountManagementId) {

        logger.info("Starting install out table operation: orderId={}, absType={}, accountManagementId={}", orderId, absType, accountManagementId);
        SystemTableDTO systemTableResDTO = systemTableService.findBySystemId("0000");
        String batchMode = systemTableResDTO.getBatchMode();
        if (!"0".equals(batchMode)) {
            logger.error("Batch mode not supported for out table operation: batchMode={}", batchMode);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_BATCH_NOT_SUPPORT_OUT_TABLE_FAULT);
        }
        installOrderProcessService.outTable(orderId, accountManagementId, absType);
        logger.info("Install out table operation completed successfully: orderId={}", orderId);
        return AnyTxnHttpResponse.success();
    }

    @ApiOperation(value = "封包", notes = "封包")
    @GetMapping(value = {"/install/fengbao"})
    public AnyTxnHttpResponse<PageResultDTO<InstallOrderDTO>> fengBao(
            @RequestParam(value = "absProductCode", required = false)
                    String absProductCode,
            @RequestParam(value = "date", required = false)
                    String date,
            @RequestParam(value = "orderId", required = false) String orderId,
            @RequestParam(value = "absType") String absType,
            @RequestParam(value = "accountManagementId", required = false) String accountManagementId) {

        logger.info("Starting install feng bao operation: orderId={}, absType={}, accountManagementId={}, absProductCode={}, date={}",
                   orderId, absType, accountManagementId, absProductCode, date);
        SystemTableDTO systemTableResDTO = systemTableService.findBySystemId("0000");
        String batchMode = systemTableResDTO.getBatchMode();
        if (!"0".equals(batchMode)) {
            logger.error("Batch mode not supported for feng bao operation: batchMode={}", batchMode);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_BATCH_NOT_SUPPORT_PACKET_FAULT);
        }
        installOrderProcessService.fengBao(orderId, accountManagementId, absProductCode, date, absType, "1");
        logger.info("Install feng bao operation completed successfully: orderId={}", orderId);
        return AnyTxnHttpResponse.success();
    }

    @ApiOperation(value = "终止", notes = "终止")
    @GetMapping(value = {"/install/end/accountManagementId/{accountManagementId}"})
    public AnyTxnHttpResponse<Boolean> end(@PathVariable(value = "accountManagementId") String accountManagementId) {
        logger.info("Starting install end operation: accountManagementId={}", accountManagementId);
        SystemTableDTO systemTableResDTO = systemTableService.findBySystemId("0000");
        String batchMode = systemTableResDTO.getBatchMode();
        if (!"0".equals(batchMode)) {
            logger.error("Batch mode not supported for end operation: batchMode={}", batchMode);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_BATCH_NOT_SUPPORT_TERMINATE_FAULT);
        }
        installOrderProcessService.end(accountManagementId);
        logger.info("Install end operation completed successfully: accountManagementId={}", accountManagementId);
        return AnyTxnHttpResponse.success();
    }

}
