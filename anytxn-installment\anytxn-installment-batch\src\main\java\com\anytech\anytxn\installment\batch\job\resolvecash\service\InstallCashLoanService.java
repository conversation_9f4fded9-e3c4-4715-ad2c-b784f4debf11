package com.anytech.anytxn.installment.batch.job.resolvecash.service;

import com.anytech.anytxn.common.core.enums.TransactionSourceEnum;
import com.anytech.anytxn.common.sequence.manager.IdGeneratorManager;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.enums.AuthMatchIndicatorEnum;
import com.anytech.anytxn.installment.base.enums.FundProcessFlagEnum;
import com.anytech.anytxn.installment.base.enums.InstallmentIndicatorEnum;
import com.anytech.anytxn.installment.base.enums.PayResultEnum;
import com.anytech.anytxn.installment.base.enums.PostMethodEnum;
import com.anytech.anytxn.installment.base.enums.RepostFromSuspendEnum;
import com.anytech.anytxn.installment.base.utils.InstallmentCreateFileUtil;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.dao.installment.mapper.InstallRecordSelfMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallSettlementLogSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallRecord;
import com.anytech.anytxn.business.dao.installment.model.InstallSettlementLog;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallRecordDTO;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.installment.base.constants.InstallmentConstant;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.batch.job.utils.InstallmentBatchCreateFileUtil;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallAccountingTransParmResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallAccountingTransParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.transaction.base.service.ISettlementLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.channels.FileChannel;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-07-02 17:52
 * 现金分期放款文件
 **/
@Service
public class InstallCashLoanService {
    private Logger logger = LoggerFactory.getLogger(InstallCashLoanService.class);
    @Autowired
    private InstallRecordSelfMapper installRecordSelfMapper;
    @Autowired
    private InstallSettlementLogSelfMapper installSettlementLogSelfMapper;
    @Autowired
    private ISettlementLogService settlementLogService;
    @Autowired
    private InstallmentCreateFileUtil installmentCreateFileUtil;
    @Autowired
    private IInstallProductInfoService installProductInfoService;
    @Autowired
    private IInstallAccountingTransParmService installAccountingTransParmService;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Value("${anytxn.batch.installCash.max-insert:500}")
    private int maxInsert;
    @Value("${anytxn.batch.installCash.max-update:500}")
    private int maxUpdate;

    @Autowired
    @Qualifier("cashFilePathConfig")
    AnytxnFilePathConfig cashFilePathConfig;

    /**
     * 生成现金分期放款文件
     **/
    public void generateCashInstalLoan(List<InstallRecordDTO> list) throws IOException {
        //封账参数
        ArrayList<String> recordList = new ArrayList<>();
        for (InstallRecordDTO installRecord : list) {
            StringBuilder sb = new StringBuilder();
            installRecord.setFundProcessFlag(FundProcessFlagEnum.ALREADY_APPLIED.getCode());
            sb.append(installRecord.getCardNumber()).append("|")
                    .append(installRecord.getAccountManagementId()).append("|")
                    .append(" ").append("|")
                    .append(installRecord.getTransactionDate()).append("|")
                    .append(installRecord.getInstallAmount()).append("|")
                    .append(installRecord.getInstallTerm()).append("|")
                    .append(installRecord.getAuthorizationCode()).append("|")
                    .append(installRecord.getGlobalFlowNumber()).append("|")
                    .append(installRecord.getTransferCard()).append("|")
                    .append(installRecord.getTransferBank()).append("|")
                    .append(installRecord.getTransferBankName()).append("|")
                    .append(installRecord.getCustomerTelephone()).append("|")
                    .append(installRecord.getTransferCardHolderName()).append("|")
                    .append(installRecord.getInstallType()).append("|")
                    .append(installRecord.getApplyIdNo()).append("|")
                    .append(installRecord.getTransferBranch()).append("|")
                    .append(installRecord.getApplyIdType()).append("|")
                    .append(installRecord.getId()).append("|")
                    .append(installRecord.getMcc()).append("|")
                    .append(installRecord.getCustomerRegion()).append("|")
                    .append(installRecord.getOrganizationNumber()).append("|")
                    .append(installRecord.getMerchantId())
                    .append("\r\n");
            recordList.add(sb.toString());
        }
        FileOutputStream fos = null;
        String name = "cashInstallmentdrawdown";
        File file = InstallmentBatchCreateFileUtil.newFile(cashFilePathConfig.getCommonPath(), name);
        //创建输出流
        fos = new FileOutputStream(file);
        try {
            //从流中获取通道
            FileChannel channel = fos.getChannel();
            //提供一个缓存区
            ByteBuffer buffer = ByteBuffer.allocate(1024 * 2048);
            //往缓存去写数据
            for (String str : recordList) {
                buffer.put(str.getBytes(StandardCharsets.UTF_8));
            }
            //翻转缓存区
            buffer.flip();
            //把缓冲区写到同道中
            channel.write(buffer);
            //更改资金处理标志
            List<InstallRecord> installRecords = BeanMapping.copyList(list, InstallRecord.class);
            installRecordSelfMapper.updateFundProAndDescBach(installRecords);
            logger.info("Generated cash installment loan file: {}", file.getPath());
        } catch (Exception e) {
            logger.error("Failed to generate cash installment loan file: exception={}", e.getMessage(), e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_GENERATE_CASH_INST_FAIL_FAULT, e);
        } finally {
            //关闭
            fos.close();
        }
    }

    /**
     * 处理现金分期放款文件
     *
     * @param list
     */
    @Transactional(rollbackFor = Exception.class)
    public void resolveCashInstallLoan(List<? extends InstallRecord> list) {
        try {
            ArrayList<InstallRecord> successList = new ArrayList<>();
            ArrayList<InstallRecord> failList = new ArrayList<>();
            for (InstallRecord record : list) {
                InstallRecord installRecord = new InstallRecord();
                if (Objects.equals(PayResultEnum.SUCCESS.getCode(), record.getFailCode())) {
                    installRecord.setId(record.getId());
                    installRecord.setFundProcessFlag(FundProcessFlagEnum.SUCCESSFUL_PAYMENT.getCode());
                    successList.add(installRecord);
                }
                if (Objects.equals(PayResultEnum.FAIL.getCode(), record.getFailCode())) {
                    installRecord.setFundPaymentFailReason(record.getFailReason());
                    installRecord.setFundProcessFlag(FundProcessFlagEnum.FAIL_PAYMENT.getCode());
                    installRecord.setId(record.getId());
                    failList.add(installRecord);
                }
            }
            List<InstallSettlementLog> installSetLogList = packageInstallSettlementLog((List<InstallRecord>) list);
            if(!CollectionUtils.isEmpty(installSetLogList)){
                //批量插入，防止单个sql过大设置阈值控制
                if (installSetLogList.size() > maxInsert) {
                    List<List<InstallSettlementLog>> insertLists = fixedGrouping(installSetLogList, maxInsert);
                    for (List<InstallSettlementLog> records : insertLists) {
                        int i = installSettlementLogSelfMapper.insertBatchInstallSettlementLog(records);
                        if (i != records.size()) {
                            logger.error("Install settlement log actual insert count does not match expected count, actual: {}, expected: {}", i, records.size());
                            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_SETTLE_LOG_INSERT_FAULT);
                        }
                    }
                } else {
                    int i = installSettlementLogSelfMapper.insertBatchInstallSettlementLog(installSetLogList);
                    if (i != installSetLogList.size()) {
                        logger.error("Install settlement log actual insert count does not match expected count, actual: {}, expected: {}", i, installSetLogList.size());
                        throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_SETTLE_LOG_INSERT_FAULT);
                    }
                }
            }

            //ArrayList<SettlementLogDTO> setLogList = packageSettlementLog(successList);
            if (!successList.isEmpty()) {
                //批量更新，防止单个sql过长，乐观锁控制更新准确性
                if (successList.size() > maxUpdate) {
                    List<List<InstallRecord>> acctUpdateList = fixedGrouping(successList, maxUpdate);
                    for (List<InstallRecord> installRecords : acctUpdateList) {
                        int res = installRecordSelfMapper.updateFundProAndDescBach(successList);
                        //乐观锁控制  if (res != list.size()
                    }
                } else {
                    int res = installRecordSelfMapper.updateFundProAndDescBach(successList);
                    //乐观锁控制 if (res != successList.size())
                }
            }
            if (!failList.isEmpty()) {
                installRecordSelfMapper.updateFundProAndDescBach(failList);
            }

        } catch (Exception exec) {
            logger.error("Failed to resolve cash installment loan file: exception={}", exec.getMessage(), exec);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ANALYSIS_CASH_INST_FAIL_FAULT);
        }
    }

    /**
     * 将一组数据固定分组，每组n个元素
     *
     * @param <T>
     * @param source 要分组的数据源
     * @param n      每组n个元素
     * @return
     */
    public static <T> List<List<T>> fixedGrouping(List<T> source, int n) {
        if (source == null || source.isEmpty() || n <= 0) {
            return null;
        }
        List<List<T>> result = new ArrayList<List<T>>();

        int sourceSize = source.size();
        int size = (source.size() / n) + 1;
        for (int i = 0; i < size; i++) {
            List<T> subset = new ArrayList<T>();
            for (int j = i * n; j < (i + 1) * n; j++) {
                if (j < sourceSize) {
                    subset.add(source.get(j));
                }
            }
            if (!org.apache.commons.collections.CollectionUtils.isEmpty(subset)) {
                result.add(subset);
            }
        }
        return result;
    }

    /**
     * 解析现金分期放款返回的文件
     **/
    private String readFile(String pathName) throws IOException {
        FileInputStream in = null;
        File file = new File(pathName);
        in = new FileInputStream(file);
        try {
            //得到通道
            FileChannel channel = in.getChannel();
            //准备buffer缓存区
            ByteBuffer buffer = ByteBuffer.allocate((int) file.length());
            //创建char缓冲区
            CharBuffer charBuffer = CharBuffer.allocate((int) file.length());
            char[] tem = null;
            //获取字符集编码对象
            Charset charset = StandardCharsets.UTF_8;
            //为charset创建新的解码构造器
            CharsetDecoder charsetDecoder = charset.newDecoder();
            //从文件通道读取字节到buffer.
            int read = channel.read(buffer);
            while (read != -1) {
                //切换buffer从写模式到读模式
                buffer.flip();
                charsetDecoder.decode(buffer, charBuffer, true);
                charBuffer.flip();
                tem = new char[charBuffer.length()];
                while (charBuffer.hasRemaining()) {
                    charBuffer.get(tem);
                }
                buffer.clear();
                charBuffer.clear();
                read = channel.read(buffer);
            }
            return new String(buffer.array());
        } catch (Exception exec) {
            logger.error("Failed to parse cash installment loan file: exception={}", exec.getMessage(), exec);
        } finally {
            //关流

            in.close();
        }
        return "";
    }

    /**
     * 封装分期清算流水表
     **/
    private List<InstallSettlementLog> packageInstallSettlementLog(List<InstallRecord> recordIdList) {
        List<InstallSettlementLog> instalSetLogList = new ArrayList<>();
        //List<InstallRecord> recordIdList = BeanMapping.copyList(recordDTOIdList, InstallRecord.class)
        List<InstallRecord> lists = installRecordSelfMapper.selectBatchRecordById(recordIdList);
        OrganizationInfoResDTO organizationInfo = null;
        if (!CollectionUtils.isEmpty(lists)) {
            organizationInfo = getOrganizationInfo(lists.get(0).getOrganizationNumber());
        }
        for (InstallRecord record : lists) {
            InstallSettlementLog installSettlementLog = new InstallSettlementLog();
            installSettlementLog.setId(IdGeneratorManager.sequenceIdGenerator().generateSeqId());
            installSettlementLog.setTxnAccountManageId(record.getAccountManagementId());
            installSettlementLog.setTxnParentTxnAccountId("");
            installSettlementLog.setTxnOriginalTxnBalanceId("");
            installSettlementLog.setTxnCardNumber(record.getCardNumber());
            installSettlementLog.setTxnOriginalTxnDate(LocalDate.of(1, 1, 1).atTime(0,0,0));
            installSettlementLog.setTxnGlobalFlowNumber(record.getGlobalFlowNumber());
            installSettlementLog.setTxnOriginalGlobalFlowNum("");
            //因为批量入账移到日切前了,需要赋值入账方式为实时入账
            installSettlementLog.setTxnPostMethod(PostMethodEnum.REAL_TIME.getCode());
            installSettlementLog.setTxnRepostFromSuspend(RepostFromSuspendEnum.NORMAL_TRANS.getCode());
            installSettlementLog.setTxnReverseFeeIndicator(InstallmentConstant.REVERSE_FEE_INDICATOR_NO);
            String org = record.getOrganizationNumber();
            String productCode = record.getProductCode();
            InstallProductInfoResDTO product = getInstallProByOrgAndCode(org, productCode);

            String postingTransaction = product.getPostingTransactionParmId();
            InstallAccountingTransParmResDTO accountTran = getInstallAccountTranByOrgNumAndTableId(org, postingTransaction);
            String installTransactionCode = accountTran.getInstallTransactionCode();
            installSettlementLog.setTxnTransactionCode(installTransactionCode);
            installSettlementLog.setTxnTransactionSource(TransactionSourceEnum.LOCAL.getCode());
            installSettlementLog.setTxnTransactionDescription("");
            installSettlementLog.setTxnTransactionDate(record.getTransactionDate().atTime(LocalTime.now()));
            installSettlementLog.setTxnTransactionAmount(record.getInstallAmount());
            installSettlementLog.setTxnTransactionCurrency(record.getInstallCcy());
            installSettlementLog.setTxnBillingDate(organizationInfo.getNextProcessingDay());
            installSettlementLog.setTxnBillingAmount(record.getInstallAmount());
            installSettlementLog.setTxnBillingCurrency(record.getInstallCcy());
            installSettlementLog.setTxnSettlementAmount(record.getInstallAmount());
            installSettlementLog.setTxnSettlementCurrency(record.getInstallCcy());
            installSettlementLog.setTxnExchangeRate(BigDecimal.ZERO);
            installSettlementLog.setTxnAuthorizationCode(record.getAuthorizationCode());
            installSettlementLog.setTxnZipCode("");
            installSettlementLog.setTxnMerchantId(record.getMerchantId());
            installSettlementLog.setTxnMerchantName("");
            installSettlementLog.setTxnMerchantCategoryCode(record.getMcc());
            installSettlementLog.setTxnCountryCode("");
            installSettlementLog.setTxnStateCode("");
            installSettlementLog.setTxnCityCode("");
            installSettlementLog.setTxnReferenceNumber("");
            installSettlementLog.setTxnAuthMatchIndicator(AuthMatchIndicatorEnum.NOT_MATCH_AUTH.getCode());
            installSettlementLog.setTxnLimitNodeId("");
            installSettlementLog.setTxnOutstandingAmount(BigDecimal.ZERO);
            installSettlementLog.setTxnOpponentBankNum("");
            installSettlementLog.setTxnOpponentAccountNum("");
            installSettlementLog.setTxnOpponentAccountName("");
            installSettlementLog.setTxnSecondMerchantId("");
            installSettlementLog.setTxnSecondMerchantName("");
            installSettlementLog.setTxnPosEntryMode("");
            installSettlementLog.setTxnVisaChargeFlag("");
            installSettlementLog.setTxnReimbursementAttribute("");
            installSettlementLog.setTxnIfiIndicator("");
            installSettlementLog.setTxnPsvIndicator("");
            installSettlementLog.setTxnDccIndicator("");
            installSettlementLog.setTxnForcePostIndicator("");
            installSettlementLog.setTxnFallBackIndicator("");
            installSettlementLog.setTxnInstallmetProductCode(record.getProductCode());
            installSettlementLog.setTxnInstallmentIndicator(InstallmentIndicatorEnum.ALREADY_INSTALL.getCode());
            installSettlementLog.setTxnInstallmentOrderId(0L);
            installSettlementLog.setTxnInstallmetType(record.getInstallType());
            installSettlementLog.setTxnInstallmentTerm(record.getInstallTerm());
            installSettlementLog.setTxnOriginTransId(record.getOriginTransactionId());
            installSettlementLog.setTxnAcquireReferencNo(record.getAcquireReferenceNo());
            installSettlementLog.setTxnInstallmentPriceFlag(record.getInstallPriceFlag());
            installSettlementLog.setTxnInstallmentTotalFee(record.getInstallTotalFee());
            installSettlementLog.setTxnInstallmentFeeRate(record.getInstallFeeRate());
            installSettlementLog.setTxnInstallmentDerateMethod(record.getInstallDerateMethod());
            installSettlementLog.setTxnInstallmentDerateValue(record.getInstallDerateValue());
            installSettlementLog.setTxnInterestTableId("");
            installSettlementLog.setTxnFeeTableId("");
            installSettlementLog.setCreateTime(LocalDateTime.now());
            installSettlementLog.setUpdateTime(LocalDateTime.now());
            installSettlementLog.setUpdateBy("");
            installSettlementLog.setVersionNumber(1L);
            installSettlementLog.setTxnLimitNodeId(record.getLimitCode());
            installSettlementLog.setCustomerRegion(record.getCustomerRegion());
            installSettlementLog.setOrganizationNumber(record.getOrganizationNumber());
            instalSetLogList.add(installSettlementLog);
        }
        return instalSetLogList;
    }

    /**
     * 封装清算流水表
     **/
   /* @Deprecated
    private ArrayList packageSettlementLog(List<InstallRecord> successList) {
        List<SettlementLogDTO> setLogList = new ArrayList<>();
        List<InstallRecord> lists = installRecordSelfMapper.selectBatchRecordById(successList);
        for (InstallRecord record : lists) {
            SettlementLogDTO settlementLog = new SettlementLogDTO();
            //入账方式 1=批量入账
            ////因为批量入账移到日切前了,需要赋值入账方式为实时入账
            settlementLog.setTxnPostMethod(PostMethodEnum.REAL_TIME.getCode());
            //拒绝重入账标志 0=普通交易
            settlementLog.setTxnRepostFromSuspend(RepostFromSuspendEnum.NORMAL_TRANS.getCode());
            //冲减交易费用标识
            settlementLog.setTxnReverseFeeIndicator(InstallmentConstant.REVERSE_FEE_INDICATOR_NO);
            //交易来源
            settlementLog.setTxnTransactionSource(TransactionSourceEnum.LOCAL_OUT.getCode());
            //授权匹配标志
            settlementLog.setTxnAuthMatchIndicator(AuthMatchIndicatorEnum.NOT_MATCH_AUTH.getCode());
            //是否恢复授权占用额度标志
            settlementLog.setTxnReleaseAuthAmount(ReleaseAuthAmountEnum.RECOVER.getCode());
            settlementLog.setTxnVisaChargeFlag("");
            settlementLog.setTxnReimbursementAttribute("");
            settlementLog.setTxnIfiIndicator("");
            settlementLog.setTxnPsvIndicator("");
            settlementLog.setTxnDccIndicator("");
            settlementLog.setTxnForcePostIndicator("");
            settlementLog.setTxnFallBackIndicator("");
            //分期标识
            settlementLog.setTxnInstallmentIndicator(InstallmentIndicatorEnum.ALREADY_INSTALL.getCode());
            //分期期数
            settlementLog.setTxnInstallmentTerm("");
            //交易币种
            settlementLog.setTxnTransactionCurrency(record.getInstallCcy());
            //入账币种
            settlementLog.setTxnBillingCurrency(record.getInstallCcy());
            //清算币种
            settlementLog.setTxnSettlementCurrency(record.getInstallCcy());
            settlementLog.setTxnStateCode("");
            settlementLog.setTxnPosEntryMode("");
            settlementLog.setTxnCountryCode("");
            settlementLog.setTxnMerchantCategoryCode(record.getMcc());
            String org = record.getOrganizationNumber();
            String productCode = record.getProductCode();
            InstallProductInfoResDTO product = getInstallProByOrgAndCode(org, productCode);
            String postingTransaction = product.getPostingTransactionParmId();
            InstallAccountingTransParmResDTO accountTran = getInstallAccountTranByOrgNumAndTableId(org, postingTransaction);
            String installTransactionCode = accountTran.getInstallTransactionCode();
            //交易码
            settlementLog.setTxnTransactionCode(installTransactionCode);
            //授权码
            settlementLog.setTxnAuthorizationCode(record.getAuthorizationCode());
            settlementLog.setTxnLimitNodeId("");
            settlementLog.setTxnInterestTableId("");
            settlementLog.setTxnFeeTableId("");
            settlementLog.setTxnMerchantId(record.getMerchantId());
            //卡号
            settlementLog.setTxnCardNumber(record.getCardNumber());
            //管理账户ID
            settlementLog.setTxnAccountManageId(record.getAccountManagementId());
            //父级交易账户ID
            settlementLog.setTxnParentTxnAccountId("");
            settlementLog.setTxnOriginalTxnBalanceId("");
            //全局业务流水号
            settlementLog.setTxnGlobalFlowNumber(record.getGlobalFlowNumber());
            //原全局业务流水号
            settlementLog.setTxnOriginalGlobalFlowNum("");
            settlementLog.setTxnZipCode("");
            settlementLog.setTxnMerchantName("");
            //原始交易日期
            settlementLog.setTxnOriginalTxnDate(null);
            OrganizationInfoResDTO organizationInfo = getOrganizationInfo(record.getOrganizationNumber());
            LocalDate nextProcessDay = organizationInfo.getNextProcessingDay();
            settlementLog.setTxnTransactionDate(nextProcessDay.atTime(LocalTime.now()));
            settlementLog.setTxnBillingDate(nextProcessDay);
            //入账金额
            settlementLog.setTxnTransactionAmount(record.getInstallAmount());
            settlementLog.setTxnBillingAmount(record.getInstallAmount());
            //清算金额
            settlementLog.setTxnSettlementAmount(record.getInstallAmount());
            settlementLog.setTxnOutstandingAmount(BigDecimal.ZERO);
            settlementLog.setTxnExchangeRate(BigDecimal.ZERO);
            settlementLog.setVersionNumber(null);
            settlementLog.setCreateTime(LocalDateTime.now());
            settlementLog.setUpdateTime(LocalDateTime.now());
            settlementLog.setTxnSecondMerchantId("");
            settlementLog.setUpdateBy("");
            settlementLog.setTxnOpponentAccountName("");
            settlementLog.setTxnOpponentAccountNum("");
            settlementLog.getTxnCityCode();
            settlementLog.setTxnOpponentBankNum("");
            settlementLog.setTxnOpponentAccountNum("");
            settlementLog.setTxnTransactionDescription("");
            setLogList.add(settlementLog);
        }
        return (ArrayList) setLogList;
    }*/

    /**
     * 获取机构参数
     *
     * @param orgNum 机构号
     * @return OrganizationInfoResDTO
     */
    private OrganizationInfoResDTO getOrganizationInfo(String orgNum) {
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(orgNum);
        if (organizationInfo == null) {
            logger.error("Organization parameter does not exist, organization number: {}", orgNum);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORG_INFO_PARAM_NOT_EXIST_FAULT);
        }
        return organizationInfo;
    }

    private InstallProductInfoResDTO getInstallProByOrgAndCode(String organizationNumber, String productCode) {
        InstallProductInfoResDTO installPro = installProductInfoService.findByIndex(organizationNumber, productCode);
        if (installPro == null) {
            logger.error("Failed to query installment product parameters by organization number and installment product");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PRO_PARAM_NOT_EXIST_FAULT);
        }
        return installPro;
    }

    private InstallAccountingTransParmResDTO getInstallAccountTranByOrgNumAndTableId(String organizationNumber, String tableId) {
        InstallAccountingTransParmResDTO accountPro = installAccountingTransParmService.selectByIndex(organizationNumber, tableId);
        if (accountPro == null) {
            logger.error("Failed to query installment accounting transaction parameter table by organization number and parameter table id");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ACCT_TRANS_PARAM_NOT_EXIST_FAULT);
        }
        return accountPro;
    }
}



