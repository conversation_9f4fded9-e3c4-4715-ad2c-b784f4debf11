package com.anytech.anytxn.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.account.base.service.IAutoPaymentLogHistoryService;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.business.base.account.domain.dto.AutoPaymentInfoHistoryDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AutoPaymentLogHisSearchKeyDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2019-11-16
 */
@RestController
@Tag(name = "自动还款历史接口")
public class AutoPaymentLogHistoryController extends BizBaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(AutoPaymentLogHistoryController.class);
    
    @Autowired
    private IAutoPaymentLogHistoryService autoPaymentLogHistoryService;

    @Operation(summary = "根据搜索条件分页查询自动还款历史")
    @PostMapping(path = "/account/autoPaymentHis")
    AnyTxnHttpResponse<PageResultDTO<AutoPaymentInfoHistoryDTO>> findByPage(
            @RequestBody(required = false) AutoPaymentLogHisSearchKeyDTO searchKey){
        logger.info("Find auto payment history by search key request received: searchKey={}", searchKey);
        
        PageResultDTO<AutoPaymentInfoHistoryDTO> result = autoPaymentLogHistoryService.findByCardNum(searchKey);
        
        logger.info("Find auto payment history by search key completed successfully: resultSize={}", result.getList().size());
        return AnyTxnHttpResponse.success(result);
    }

    @Operation(summary = "根据自动还款历史id查询自动还款历史")
    @GetMapping("/account/autoPaymentHis/id/{id}")
    @Parameter(name = "id", required = true, description = "自动还款历史id")
    AnyTxnHttpResponse<AutoPaymentInfoHistoryDTO> getAutoPaymentHisById(
            @PathVariable("id") String id) {
        logger.info("Get auto payment history by id request received: id={}", id);
        
        AutoPaymentInfoHistoryDTO autoPaymentLogDTO = autoPaymentLogHistoryService.selectById(id);
        
        logger.info("Get auto payment history by id completed successfully");
        return AnyTxnHttpResponse.success(autoPaymentLogDTO);
    }
}
