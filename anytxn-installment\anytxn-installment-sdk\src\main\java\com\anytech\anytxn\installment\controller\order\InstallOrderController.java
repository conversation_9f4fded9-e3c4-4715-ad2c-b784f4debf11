package com.anytech.anytxn.installment.controller.order;

import com.anytech.anytxn.installment.base.domain.dto.AdjustInstallTermDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallOrderSearchKeyDTO;
import com.anytech.anytxn.installment.base.enums.InstallRepDetailEnum;
import com.anytech.anytxn.installment.base.service.IInstallAdjustTermService;
import com.anytech.anytxn.installment.base.service.IInstallOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallmentLimitUnitCrossDTO;
import com.anytech.anytxn.business.base.installment.service.InstallmentLimitUnitCrossService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2019-06-06 14:58
 **/
@RestController
@Api(tags = "分期订单")
public class InstallOrderController extends BizBaseController {

    private static final Logger logger = LoggerFactory.getLogger(InstallOrderController.class);

    @Autowired
    private IInstallOrderService installOrderService;

    @Autowired
    private InstallmentLimitUnitCrossService installmentLimitUnitCrossService;

    @Autowired
    private IInstallAdjustTermService installAdjustTermService;

    @ApiOperation(value = "根据订单id 查询订单表")
    @GetMapping(value = "/install/istallorder/orderId/{orderId}")
    public AnyTxnHttpResponse<InstallOrderDTO> getOrderById(@PathVariable(value = "orderId") String orderId) {
        logger.info("Starting get order by id: orderId={}", orderId);
        InstallOrderDTO installOrderRes = installOrderService.findOrderById(orderId);
        logger.info("Get order by id completed successfully: orderId={}, found={}", orderId, installOrderRes != null);
        return AnyTxnHttpResponse.success(installOrderRes);
    }

    @ApiOperation(value = "根据订单accountManagementId productCode transactionDate acquireReferenceNo查询订单表")
    @GetMapping(value = "/install/istallorder/accountManagementId/{accountManagementId}/productCode/{productCode}/transactionDate/{transactionDate}/authorizationCode/{authorizationCode}/installmentAmount/{installmentAmount}")
    public AnyTxnHttpResponse<InstallOrderDTO> getOrderByManageAndCodeAndDateAndAcquire(@PathVariable(value = "accountManagementId") String accountManagementId,
                                                                                        @PathVariable(value = "productCode") String productCode,
                                                                                        @PathVariable(value = "transactionDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate transactionDate,
                                                                                        @PathVariable(value = "authorizationCode") String authorizationCode,
                                                                                        @PathVariable(value = "installmentAmount") BigDecimal installmentAmount) {
        logger.info("Starting get order by manage and code and date and acquire: accountManagementId={}, productCode={}, transactionDate={}, authorizationCode={}, installmentAmount={}",
                   accountManagementId, productCode, transactionDate, authorizationCode, installmentAmount);
        InstallOrderDTO installOrderRes = installOrderService.orderByManageAndCodeAndDateAndAcquire(
                accountManagementId,
                productCode,
                transactionDate,
                authorizationCode,
                installmentAmount);
        logger.info("Get order by manage and code and date and acquire completed successfully: accountManagementId={}, found={}", accountManagementId, installOrderRes != null);
        return AnyTxnHttpResponse.success(installOrderRes);
    }

    @ApiOperation(value = "分页查询分期订单表", notes = "分页查询分期订单表")
    @PostMapping(value = "/install/istallorder")
    public AnyTxnHttpResponse<PageResultDTO<InstallOrderDTO>> getOrderPageByOptions(@RequestBody(required = false) InstallOrderSearchKeyDTO installOrderSearchKeyDTO) {
        logger.info("Starting get order page by options");
        PageResultDTO<InstallOrderDTO> txnPage = installOrderService.orderByOrgAndManageAndProductAndDateAndAuthorAndAmount(installOrderSearchKeyDTO);
        logger.info("Get order page by options completed successfully: resultCount={}",
                   txnPage != null && txnPage.getRecords() != null ? txnPage.getRecords().size() : 0);
        return AnyTxnHttpResponse.success(txnPage);
    }

    @ApiOperation(value = "分页查询分期订单管控单元关联表", notes = "分页查询分期订单管控单元关联表")
    @GetMapping(value = "/install/istallorder/installLimitUnitCross")
    public AnyTxnHttpResponse<PageResultDTO<InstallmentLimitUnitCrossDTO>> getOrderPageByOptions(@ApiParam("机构号") @RequestParam String organizationNumber,
                                                                                                 @ApiParam("分期订单号") @RequestParam String installmentOrderId,
                                                                                                 @ApiParam("页码，默认1") @RequestParam(required = false) int page,
                                                                                                 @ApiParam("每页显示条数,默认8") @RequestParam(required = false) int pageSize) {
        logger.info("Starting get install limit unit cross page: organizationNumber={}, installmentOrderId={}, page={}, pageSize={}",
                   organizationNumber, installmentOrderId, page, pageSize);
        PageResultDTO<InstallmentLimitUnitCrossDTO> txnPage = installmentLimitUnitCrossService.selectByOrgAndInstallOrderId(organizationNumber,installmentOrderId,page,pageSize);
        logger.info("Get install limit unit cross page completed successfully: organizationNumber={}, resultCount={}",
                   organizationNumber, txnPage != null && txnPage.getRecords() != null ? txnPage.getRecords().size() : 0);
        return AnyTxnHttpResponse.success(txnPage);
    }

    @ApiOperation(value = "支持分期调整还款期数（缩短或延长）", notes = "支持分期调整还款期数（缩短或延长）")
    @PostMapping(value = "/install/adjustTerm")
    public AnyTxnHttpResponse<Void> getOrderPageByOptions(@RequestBody(required = false) AdjustInstallTermDTO adjustTermDto) {
        logger.info("Starting adjust install term: orderId={}, newTerm={}",
                   adjustTermDto != null ? adjustTermDto.getOrderId() : null,
                   adjustTermDto != null ? adjustTermDto.getNewTerm() : null);
        installAdjustTermService.adjustInstallTerm(adjustTermDto);
        logger.info("Adjust install term completed successfully: orderId={}",
                   adjustTermDto != null ? adjustTermDto.getOrderId() : null);
        return AnyTxnHttpResponse.successDetail(InstallRepDetailEnum.AD_RE.message());
    }
}
