package com.anytech.anytxn.installment.batch.job.createorder.step;

import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.installment.model.InstallSettlementLog;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.database.JdbcPagingItemReader;
import org.springframework.batch.item.database.Order;
import org.springframework.batch.item.database.PagingQueryProvider;
import org.springframework.batch.item.database.support.SqlPagingQueryProviderFactoryBean;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * 分期批量创建订单
 *
 * <AUTHOR>
 * @date 2019-07-17
 **/
public class CreateInstallOrderReader extends JdbcPagingItemReader<InstallSettlementLog> {

    private static final Logger logger = LoggerFactory.getLogger(CreateInstallOrderReader.class);

    public CreateInstallOrderReader(String partitionKey, DataSource dataSource) {
        super();
        this.setRowMapper((new BeanPropertyRowMapper<>(InstallSettlementLog.class)));
        this.setQueryProvider(oraclePagingQueryProvider(partitionKey,dataSource));
    }

    private PagingQueryProvider oraclePagingQueryProvider(String partitionKey, DataSource dataSource) {
        SqlPagingQueryProviderFactoryBean providerFactoryBean = new SqlPagingQueryProviderFactoryBean();
        providerFactoryBean.setSelectClause("ID, TXN_ACCOUNT_MANAGE_ID, TXN_PARENT_TXN_ACCOUNT_ID, TXN_ORIGINAL_TXN_BALANCE_ID, "+
                "TXN_CARD_NUMBER, TXN_ORIGINAL_TXN_DATE, TXN_GLOBAL_FLOW_NUMBER, TXN_ORIGINAL_GLOBAL_FLOW_NUM,  "+
                "TXN_POST_METHOD, TXN_REPOST_FROM_SUSPEND, TXN_REVERSE_FEE_INDICATOR, TXN_TRANSACTION_CODE, "+
                "TXN_TRANSACTION_SOURCE, TXN_TRANSACTION_DESCRIPTION, TXN_TRANSACTION_DATE, TXN_TRANSACTION_AMOUNT, "+
                "TXN_TRANSACTION_CURRENCY, TXN_BILLING_DATE, TXN_BILLING_AMOUNT, TXN_BILLING_CURRENCY, "+
                "TXN_SETTLEMENT_AMOUNT, TXN_SETTLEMENT_CURRENCY, TXN_EXCHANGE_RATE, TXN_AUTHORIZATION_CODE, "+
                "TXN_ZIP_CODE, TXN_MERCHANT_ID, TXN_MERCHANT_NAME, TXN_MERCHANT_CATEGORY_CODE, "+
                "TXN_COUNTRY_CODE, TXN_STATE_CODE, TXN_CITY_CODE, TXN_REFERENCE_NUMBER, TXN_AUTH_MATCH_INDICATOR, "+
                "TXN_RELEASE_AUTH_AMOUNT, TXN_LIMIT_NODE_ID, TXN_OUTSTANDING_AMOUNT, TXN_OPPONENT_BANK_NUM,"+
                "TXN_OPPONENT_ACCOUNT_NUM, TXN_OPPONENT_ACCOUNT_NAME, TXN_SECOND_MERCHANT_ID, "+
                "TXN_SECOND_MERCHANT_NAME, TXN_POS_ENTRY_MODE, TXN_VISA_CHARGE_FLAG, TXN_REIMBURSEMENT_ATTRIBUTE, "+
                "TXN_IFI_INDICATOR, TXN_PSV_INDICATOR, TXN_DCC_INDICATOR, TXN_FORCE_POST_INDICATOR, "+
                "TXN_FALL_BACK_INDICATOR, TXN_INSTALLMENT_INDICATOR, TXN_INSTALLMENT_ORDER_ID, "+
                "TXN_INSTALLMET_TYPE, TXN_INSTALLMENT_TERM, TXN_INTEREST_TABLE_ID, TXN_FEE_TABLE_ID, "+
                "CREATE_TIME, UPDATE_TIME, UPDATE_BY, VERSION_NUMBER, PARTITION_KEY, TXN_INSTALLMET_PRODUCT_CODE, "+
                "TXN_ORIGIN_TRANS_ID, TXN_ACQUIRE_REFERENC_NO, TXN_INSTALLMENT_PRICE_FLAG, TXN_INSTALLMENT_TOTAL_FEE, "+
                "TXN_INSTALLMENT_FEE_RATE, TXN_INSTALLMENT_DERATE_METHOD, TXN_INSTALLMENT_DERATE_VALUE, CUSTOMER_REGION," +
                "organization_number,CREATE_STATUS,AUTH_MATCH_STATUS,FIRST_PAYMENT_AMOUNT, INSTALMENT_PERIOD, INSTALLMENT_RATE, TOTAL_INTEREST_AMOUNT, " +
                "MONTHLY_PAYMENT_AMOUNT, TOTAL_TRANSACTION_AMOUNT ");
        providerFactoryBean.setFromClause("INSTALL_SETTLEMENT_LOG");
        providerFactoryBean.setWhereClause(" organization_number = '" + OrgNumberUtils.getOrg() + "'" + "and CREATE_STATUS = '0' ");
        Map<String, Order> sortKey = new HashMap<>(10);
        sortKey.put("ID", Order.ASCENDING);
        providerFactoryBean.setSortKeys(sortKey);

        providerFactoryBean.setDataSource(dataSource);
        try {
            return providerFactoryBean.getObject();
        } catch (Exception e) {
            logger.error("Failed to create PagingQueryProvider: exception={}", e.getMessage(), e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_SELECT_DATABASE_FAULT,e);
        }
    }
}
