package com.anytech.anytxn.account.feign;


import com.anytech.anytxn.account.base.domain.dto.LoyaltyCustomerInfoResDTO;
import com.anytech.anytxn.account.base.domain.dto.LoyaltyInfoRespDTO;
import com.anytech.anytxn.account.base.domain.dto.TxnUpdateToLoyaltyReqDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * Loyalty服务调用
 * <AUTHOR>
 * @Date 2024/4/9  11:13
 * @Version 1.0
 */
@FeignClient(value = "${anytxn.feign.accounting.serviceName:GATEWAY}", path = "/loyalty/v1/biz/api")
@ResponseBody
public interface TxnToLoyaltyFeign {
  /**
   * txn同步账户权益信息到Loyalty:权益改变
   * @param txnUpdateToLoyalty
   * @return
   */
  @PutMapping(path = "/program/update")
  AnyTxnHttpResponse txnUpdateToLoyalty01(@RequestBody TxnUpdateToLoyaltyReqDTO txnUpdateToLoyalty,@RequestHeader("organizationNumber") String organizationNumber);

  /**
   * txn同步账户权益信息到Loyalty:权益不变，账户信息改变
   * @param txnUpdateToLoyalty
   * @return
   */
  @PutMapping(path = "/program/accountUpdate")
  AnyTxnHttpResponse txnUpdateToLoyalty02(@RequestBody TxnUpdateToLoyaltyReqDTO txnUpdateToLoyalty,@RequestHeader("organizationNumber") String organizationNumber);

  /**
   * 积分回馈活动查询
   * @param cardNumber
   * @param accountNumber
   * @param productNumber
   * @return
   */
  @GetMapping(value = "/program/select")
  AnyTxnHttpResponse<LoyaltyInfoRespDTO<LoyaltyCustomerInfoResDTO>>  customerCardProject(@RequestParam String cardNumber, @RequestParam String accountNumber, @RequestParam String productNumber,@RequestHeader("organizationNumber") String organizationNumber);
}