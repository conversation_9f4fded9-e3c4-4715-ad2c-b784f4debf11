package com.anytech.anytxn.accounting.service;

import com.anytech.anytxn.accounting.base.service.IAccountSummaryService;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlamsSumMapper;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlamsSumSelfMapper;
import com.anytech.anytxn.business.dao.accounting.mapper.SubjectRecordSumOracleLogMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlamsSum;
import com.anytech.anytxn.business.dao.accounting.model.SubjectRecordSum;
import com.anytech.anytxn.business.dao.accounting.model.SubjectRecordSumOracleLog;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlamsDefinitionDTO;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlamsDefinitionService;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionTypeResDTO;
import com.anytech.anytxn.parameter.base.common.domain.model.ParmAcctProductMainInfo;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.service.ITransactionTypeService;
import com.anytech.anytxn.parameter.common.mapper.broadcast.product.ParmAcctProductMainInfoSelfMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlamsSumResDTO;
import com.anytech.anytxn.accounting.base.exception.AnyTxnAccountantException;
import com.anytech.anytxn.accounting.base.enums.AccountantRespCodeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * 交易会计汇总流水表
 * @date 2019-12-04 16:19
 **/
@Service
public class AccountSummaryServiceImpl implements IAccountSummaryService {
    private static final Logger logger = LoggerFactory.getLogger(AccountSummaryServiceImpl.class);
    @Resource
    private AccountantGlamsSumMapper accountantGlamsSumMapper;
    @Resource
    private AccountantGlamsSumSelfMapper accountantGlamsSumSelfMapper;
    @Resource
    private ITransactionTypeService transactionTypeService;
    @Resource
    private ITransactionCodeService transactionCodeService;
    @Resource
    private ITPmsGlamsDefinitionService glamsDefinitionService;
    @Resource
    private ParmAcctProductMainInfoSelfMapper parmAcctProductMainInfoSelfMapper;

    @Resource
    private SubjectRecordSumOracleLogMapper subjectRecordSumOracleLogMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;



    @Override
    public TAmsGlamsSumResDTO selectByPrimaryKey(String id) {
        if (id == null) {
            logger.error("Transaction accounting summary flow table id is empty");
            throw new AnyTxnAccountantException(AccountantRespCodeEnum.P_PARAM_EMPTY_FAIL);

        }
        AccountantGlamsSum amsGlamsSum = accountantGlamsSumMapper.selectByPrimaryKey(id);
        TAmsGlamsSumResDTO tAmsGlamsSumResDTO = null;
        if (amsGlamsSum != null) {
            TransactionTypeResDTO transactionType = null;
            TransactionCodeResDTO transactionCode = null;
            TransactionCodeResDTO transactionCodeOrig = null;
            ParmAcctProductMainInfo acctProductMainInfo = null;
            TPmsGlamsDefinitionDTO pmsGlamsDefinition = null;
            try {
                //交易类型描述
                if (amsGlamsSum.getTransactionTypeCode() != null) {
                    transactionType = transactionTypeService.findTransactionType(amsGlamsSum.getOrganizationNumber(),
                            amsGlamsSum.getTransactionTypeCode());
                }
            } catch (Exception e) {
                logger.error("Current parameter is not defined: transactionTypeCode={}", amsGlamsSum.getTransactionTypeCode(), e);
            }
            try {
                //交易码描述
                if (amsGlamsSum.getTxnCode() != null) {
                    transactionCode = transactionCodeService.findTransactionCode(amsGlamsSum.getOrganizationNumber(),
                            amsGlamsSum.getTxnCode());
                }
            } catch (Exception e) {
                logger.error("Current parameter is not defined: txnCode={}", amsGlamsSum.getTxnCode(), e);
            }
            try {
                //原始交易码描述
                if (amsGlamsSum.getTxnCodeOrig() != null) {
                    transactionCodeOrig = transactionCodeService.findTransactionCode(amsGlamsSum.getOrganizationNumber(),
                            amsGlamsSum.getTxnCodeOrig());
                }
            } catch (Exception e) {
                logger.error("Current parameter is not defined: txnCodeOrig={}", amsGlamsSum.getTxnCodeOrig(), e);
            }
            try {
                //原始交易码描述
                if (amsGlamsSum.getAcctLogo() != null) {
                    acctProductMainInfo = parmAcctProductMainInfoSelfMapper.selectByOrgNumAndProNum(amsGlamsSum.getOrganizationNumber(), amsGlamsSum.getAcctLogo());

                }
            } catch (Exception e) {
                logger.error("Current parameter is not defined: acctLogo={}", amsGlamsSum.getAcctLogo(), e);
            }
            try {
                //流水控制表
                if (amsGlamsSum.getRuleId() != null) {
                    pmsGlamsDefinition = glamsDefinitionService.selectByIndex(amsGlamsSum.getOrganizationNumber(),
                            "110110", amsGlamsSum.getRuleId());
                }
            } catch (Exception e) {
                logger.error("Current parameter is not defined: ruleId={}", amsGlamsSum.getRuleId(), e);
            }
            tAmsGlamsSumResDTO = BeanMapping.copy(amsGlamsSum, TAmsGlamsSumResDTO.class);

            if (transactionType != null) {
                tAmsGlamsSumResDTO.setTransactionTypeCodeDesc(transactionType.getDescription());
            }
            if (transactionCode != null) {
                tAmsGlamsSumResDTO.setTxnCodeDesc(transactionCode.getDescription());
            }
            if (transactionCodeOrig != null) {
                tAmsGlamsSumResDTO.setTxnCodeOrigDesc(transactionCodeOrig.getDescription());
            }
            if (pmsGlamsDefinition != null) {
                tAmsGlamsSumResDTO.setRuleIdDesc(amsGlamsSum.getRuleId() + " - " + pmsGlamsDefinition.getDescription());
            }
            if (acctProductMainInfo != null) {
                tAmsGlamsSumResDTO.setAcctLogoDesc(acctProductMainInfo.getDescription());
            }
        }
        return tAmsGlamsSumResDTO;
    }

    @Override
    public PageResultDTO<TAmsGlamsSumResDTO> findPage(Integer page, Integer rows, String postingDate,
                                                      String accountManagementId, String globalFlowNo, String processInd) {
        Page page1 = PageHelper.startPage(page, rows);
        List<AccountantGlamsSum> sumList = accountantGlamsSumSelfMapper.getTamsGlamsByPage(OrgNumberUtils.getOrg(), postingDate, accountManagementId,
                globalFlowNo, processInd);
        List<TAmsGlamsSumResDTO> tAmsGlamsSumResDtos = BeanMapping.copyList(sumList, TAmsGlamsSumResDTO.class);

        if (CollectionUtils.isNotEmpty(tAmsGlamsSumResDtos)) {
            tAmsGlamsSumResDtos.forEach(o -> {
                TransactionCodeResDTO transactionCode = null;
                if (o.getTxnCode() != null) {
                    try {
                        transactionCode = transactionCodeService.findTransactionCode(o.getOrganizationNumber(),
                                o.getTxnCode());
                    } catch (Exception e) {
                        logger.error("Current parameter is not defined: txnCode={}", o.getTxnCode(), e);
                    }
                }
                if (transactionCode != null) {
                    o.setTxnCodeDesc(transactionCode.getDescription());
                }
            });
        }
        return new PageResultDTO(page, rows, page1.getTotal(), page1.getPages(), tAmsGlamsSumResDtos);
    }



    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public SubjectRecordSumOracleLog insertSumOracleRecord(SubjectRecordSum subjectRecordSum) {
        SubjectRecordSumOracleLog subjectRecordSumOracleLog = new SubjectRecordSumOracleLog();
        subjectRecordSumOracleLog.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        subjectRecordSumOracleLog.setSumDate(subjectRecordSum.getSumDate());
        subjectRecordSumOracleLog.setModuleFlag(subjectRecordSum.getModuleFlag());
        subjectRecordSumOracleLog.setCrdOrganization(subjectRecordSum.getCrdOrganization());
        subjectRecordSumOracleLog.setAccountProduct(subjectRecordSum.getAccountProduct());
        subjectRecordSumOracleLog.setTotalAmount(subjectRecordSum.getTotalAmount());
        subjectRecordSumOracleLog.setProcessFlag("0");
        subjectRecordSumOracleLog.setOrganizationNumber(OrgNumberUtils.getOrg());
        subjectRecordSumOracleLog.setCreateTime(LocalDateTime.now());
        subjectRecordSumOracleLog.setUpdateTime(LocalDateTime.now());
        subjectRecordSumOracleLog.setUpdateBy("admin");

        SubjectRecordSumOracleLog exit = subjectRecordSumOracleLogMapper.exitByGroupCondition(subjectRecordSumOracleLog);
        if (exit != null){
            logger.info("Insert sum oracle record completed: record already exists, id={}", exit.getId());
            return exit;
        }


        int i = subjectRecordSumOracleLogMapper.insertSelective(subjectRecordSumOracleLog);

        if (i != 1){
            logger.error("Insert sum oracle record failed: insert result={}", i);
            throw new AnyTxnAccountantException(AccountantRespCodeEnum.D_DATABASE_FAULT);
        }
        return subjectRecordSumOracleLog;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public Integer updateSuccessLogStatus(String id) {
        return subjectRecordSumOracleLogMapper.updateSuccess(id);
    }
}
