package com.anytech.anytxn.account.job.newautopay.step;

import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.database.JdbcPagingItemReader;
import org.springframework.batch.item.database.Order;
import org.springframework.batch.item.database.PagingQueryProvider;
import org.springframework.batch.item.database.support.SqlPagingQueryProviderFactoryBean;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import javax.sql.DataSource;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * 自扣读取管理账户表
 *
 * <AUTHOR>
 * @date 2021/11/10
 */
public class AutoPaymentReader extends JdbcPagingItemReader<AccountManagementInfo> {

    private static final Logger logger = LoggerFactory.getLogger(AutoPaymentReader.class);

    public AutoPaymentReader(DataSource dataSource, IOrganizationInfoService organizationInfoService) {
        super();
        logger.info("Calling organizationInfoService.findOrganizationInfo, orgNumber={}", OrgNumberUtils.getOrg());
        OrganizationInfoResDTO orgInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        logger.info("Called organizationInfoService.findOrganizationInfo, result={}", orgInfo != null ? "success" : "null");
        this.setRowMapper(new BeanPropertyRowMapper<>(AccountManagementInfo.class));
        this.setQueryProvider(AutoPaymentReader.oraclePagingQueryProvider(dataSource,orgInfo));
    }

    /**
     * 大莱改造：读取管理账户表
     */
    private static PagingQueryProvider oraclePagingQueryProvider(DataSource dataSource,OrganizationInfoResDTO orgInfo) {
        LocalDate today = orgInfo.getToday();
        SqlPagingQueryProviderFactoryBean provider = new SqlPagingQueryProviderFactoryBean();
        //获得入口数据
        provider.setSelectClause("ACCOUNT_MANAGEMENT_ID, CUSTOMER_ID, ORGANIZATION_NUMBER, PRODUCT_NUMBER, CURRENCY, " +
                "BRANCH_NUMBER, NETPOINT_NUMBER, OPEN_DATE, CLOSE_DATE, CYCLE_DAY, AUTO_EXCHANGE_INDICATOR, " +
                "ACCOUNT_STATUS, ACCOUNT_STATUS_SET_DATE, FINANCE_STATUS, FINANCE_STATUS_SET_DATE, " +
                "LAST_STATEMENT_DATE, BLOCK_CODE, BLOCK_CODE_SET_DATE, PREVIOUS_BLOCK_CODE, PREVIOUS_BLOCK_CODE_SET_DATE, " +
                "CHARGE_OFF_DATE, CHARGE_OFF_REASON, TOTAL_GRACE_PAYMENT_AMOUNT, TOTAL_DUE_AMOUNT, " +
                "CURRENT_DUE_AMOUNT, PAST_DUE_AMOUNT, DAY30_DUE_AMOUNT, DAY60_DUE_AMOUNT, DAY90_DUE_AMOUNT, " +
                "DAY120_DUE_AMOUNT, DAY150_DUE_AMOUNT, DAY180_DUE_AMOUNT, DAY210_DUE_AMOUNT, " +
                "PREVIOUS_STATEMENT_DATE, LAST_CYCLE_CREDIT_ADJ_AMOUNT, CYCLE_DUE, LAST_AGED_DATE, " +
                "PAST_DUE_COUNT, DAY30_DUE_COUNT, DAY60_DUE_COUNT, DAY90_DUE_COUNT, DAY120_DUE_COUNT, " +
                "DAY150_DUE_COUNT, DAY180_DUE_COUNT, DAY210_DUE_COUNT, PAYMENT_HISTORY, IN_COLLECTION_INDICATOR, " +
                "CORPORATE_INDICATOR,CORPORATE_CUSTOMER_ID,LIABILITY,REASON_CODE, " +
                "SEPARATE_STATEMENT_IND,SUSPEND_DATE,CANCEL_DATE,LEGAL_DATE,FRAUD_DATE,RESERVE_DATE,REINSTATE_DATE,LAST_USER_TXN_CODE," +
                "LAST_USER_TXN_DATE,LAST_SYS_TXN_CODE,LAST_SYS_TXN_DATE,VIP_MIN_PAYMENT_RATE,STATEMENT_HOLD_INDICATOR,HI_BALANCE," +
                "ACCOUNT_PREVIOS_STATUS_CODE,ACCOUNT_PREVIOS_STATUS_SET_DATE,ACCOUNT_PREVIOS_STATUS_SET_DATE,DECEASE_DATE,INACTIVE_DATE," +
                "NPL_STATUS,NPL_STATUS_DATE,PREVIOUS_NPL_STATUS,PREVIOUS_NPL_STATUS_DATE,ORIGINAL_CHARGE_OFF_DATE,ORIGINAL_CHARGE_OFF_REASON," +
                "COLLECTION_REASON,LAST_COLLECTION_DATE,COLLECTION_COUNT,FIRST_ACTIVATED_DATE,AUTOPAY_RATE,ACCOUNT_STATUS_REASON,SYS_STATUS_CHANGE_DATE," +
                "CHECKSUM,RISK_PROFILE_ID,COLLECTION_DATE," +
                "PREFERENTIAL_INTEREST_RATE_INDICATOR,PREFERENTIAL_INTEREST_RATE_END_DATE,PREFERENTIAL_INTEREST_RATE_CASH,PREFERENTIAL_INTEREST_RATE_RETAIL," +
                "PREFERENTIAL_INTEREST_RATE_SERVICE_FEE,PREFERENTIAL_INTEREST_RATE_BAL_TRANSFER,PREFERENTIAL_INTEREST_RATE_CREDIT_BAL,PREFERENTIAL_INTEREST_RATE_LATE_FEE," +
                "LAST_AGE_DATE,TOTAL_UNSTATEMENT_AMOUNT,DAY30_PAYMENT_DATE,DAY60_PAYMENT_DATE,DAY90_PAYMENT_DATE,DAY120_PAYMENT_DATE," +
                "DAY150_PAYMENT_DATE,DAY180_PAYMENT_DATE,DAY210_PAYMENT_DATE," +
                "DAY240_DUE_AMOUNT,DAY240_DUE_COUNT,DAY240_PAYMENT_DATE,DAY270_DUE_AMOUNT,DAY270_DUE_COUNT,DAY270_PAYMENT_DATE," +
                "DAY300_DUE_AMOUNT,DAY300_DUE_COUNT,DAY300_PAYMENT_DATE,DAY330_DUE_AMOUNT,DAY330_DUE_COUNT,DAY330_PAYMENT_DATE," +
                "DAY360_DUE_AMOUNT,DAY360_DUE_COUNT,DAY360_PAYMENT_DATE,DAY390_DUE_AMOUNT,DAY390_DUE_COUNT,DAY390_PAYMENT_DATE," +
                "ACCOUNT_STATUS_SET_USER,LAST_PAYMENT_DATE,LAST_AGE_CODE,MONTH_DEBIT_ADJUSTMENT_AMOUNT,MONTH_DEBIT_ADJUSTMENT_COUNT,MONTH_CREDIT_ADJUSTMENT_AMOUNT,MONTH_CREDIT_ADJUSTMENT_COUNT," +
                "MONTH_DISPUTE_AMOUNT,MONTH_DISPUTE_COUNT,MONTH_REPOST_AMOUNT,MONTH_REPOST_COUNT,MONTH_CHARGEBACK_AMOUNT,MONTH_CHARGEBACK_COUNT,MONTH_WOFF_FRAUD_AMOUNT," +
                "MONTH_WOFF_FRAUD_COUNT,MONTH_WOFF_OPS_AMOUNT,MONTH_WOFF_OPS_COUNT,MONTH_NPL_AMOUNT,MONTH_NPL_COUNT,MONTH_DECLASSIFIED_AMOUNT,MONTH_DECLASSIFIED_COUNT," +
                "MONTH_RESCHEDULED_AMOUNT,MONTH_RESCHEDULED_COUNT,MONTH_SETTLED_AMOUNT,MONTH_SETTLED_COUNT," +
                "YEAR_PAYMENT_AMOUNT,AUTO_PAYMENT_ACCT_NUMBER,AUTO_PAYMENT_BANK_NUMBER,AUTO_PAYMENT_BRANCH_NUMBER,AUTO_PAYMENT_FLAG,AUTO_PAYMENT_TYPE," +
                "YEAR_PAYMENT_COUNT,YEAR_DEBIT_ADJUSTMENT_AMOUNT,YEAR_DEBIT_ADJUSTMENT_COUNT,YEAR_CREDIT_ADJUSTMENT_AMOUNT,YEAR_CREDIT_ADJUSTMENT_COUNT,YEAR_DISPUTE_AMOUNT," +
                "YEAR_DISPUTE_COUNT,YEAR_REPOST_AMOUNT,YEAR_REPOST_COUNT,YEAR_CHARGEBACK_AMOUNT,YEAR_CHARGEBACK_COUNT,YEAR_WOFF_FRAUD_AMOUNT," +
                "YEAR_WOFF_FRAUD_COUNT,YEAR_WOFF_OPS_AMOUNT,YEAR_WOFF_OPS_COUNT,YEAR_NPL_AMOUNT,YEAR_NPL_COUNT,YEAR_DECLASSIFIED_AMOUNT,YEAR_DECLASSIFIED_COUNT," +
                "YEAR_RESCHEDULED_AMOUNT,YEAR_RESCHEDULED_COUNT,YEAR_SETTLED_AMOUNT,YEAR_SETTLED_COUNT," +
                "CTD_TOTAL_FEE_COUNT,CTD_TOTAL_FEE_AMOUNT,REVOLVING_AMOUNT_INDICATOR,RETURN_CHEQUE_COUNT," +
                "MONTH_IPP_AMOUNT,CTD_RETURN_CHEQUE_AMOUNT,MONTH_RETURN_CHEQUE_COUNT," +
                "MONTH_RETURN_CHEQUE_AMOUNT,YEAR_RETURN_CHEQUE_COUNT,YEAR_RETURN_CHEQUE_AMOUNT," +
                "PARTITION_KEY, CREATE_TIME, UPDATE_TIME, UPDATE_BY, VERSION_NUMBER, " +
                " AUTO_PAYMENT_FIRST_DATE, AUTO_PAYMENT_SECOND_DATE, WAIVE_LATE_FEE_FLG, " +
                "WAIVE_INTEREST_FLG, AUTO_EXCHANGE_PAYMENT_TYPE, WAIVE_LATE_FEE_NUM, STATEMENT_DUE_AMOUNT,  " +
                "PREVIOUS_FINANCE_STATUS, " +
                "PREVIOUS_FINANCE_STATUS_DATE,FINANCE_STATUS_IND,FINANCE_STATUS_IND_DATE," +
                "  ABS_TYPE,ABS_PRODUCT_CODE_FIRST,ABS_PRODUCT_CODE_CURR,ABS_END_DATE," +
                "STATEMENT_ADDRESS_TYPE,FIVE_TYPE_INDICATOR,TOTAL_PAYMENT_AMOUNT,FIRST_DUE_DATE, " +
                "CURRENT_AMOUNT_OVERDUE, ACCUMULATED_OVERDUE_NUMBER, MAX_OVERDUE_NUMBER,NO_OF_RETURN_CHEQUE,  " +
                "CURRENT_BONUS_POINTS, LAST_STMT_BONUS_POINTS,LOCAL_AUTO_PAY_RATE,LOCAL_AUTO_PAY_BANK_NAME,AUTO_PAY_ACCOUNT_NAME_LOCAL,AUTO_PAY_RATE_FOREIGN, " +
                "AUTO_PAY_BANK_NAME_FOREIGN,AUTO_PAY_ACCOUNT_NUMBER_FOREIGN,AUTO_PAY_ACCOUNT_NAME_FOREIGN,AUTO_EXTRACTED_DATE");
        provider.setFromClause("account_management_info");
        String orgConditionStr = "ORGANIZATION_NUMBER = " + OrgNumberUtils.getOrg();
        //设置查询条件
        provider.setWhereClause(orgConditionStr + " and account_status not in ('8','9') " +
                "and AUTO_PAYMENT_FLAG = '1' " + " and (AUTO_EXTRACTED_DATE != '"+ today+"'" + " or AUTO_EXTRACTED_DATE is null)");
        Map<String, Order> sortKey = new HashMap<>(10);
        sortKey.put("ACCOUNT_MANAGEMENT_ID", Order.ASCENDING);
        provider.setSortKeys(sortKey);
        provider.setDataSource(dataSource);
        try {
            return provider.getObject();
        } catch (Exception e) {
            logger.error("Failed to create PagingQueryProvider for AutoPaymentReader", e);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.UNKONWN_ERR);
        }
    }
}
