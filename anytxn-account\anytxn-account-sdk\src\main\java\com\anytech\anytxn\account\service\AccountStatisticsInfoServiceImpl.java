package com.anytech.anytxn.account.service;

import com.anytech.anytxn.business.base.account.domain.dto.AccStaDTO;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.account.base.constants.Constants;
import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.enums.TransactionTypeCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.account.base.service.IAccountStatisticsInfoService;
import com.anytech.anytxn.common.core.base.PageResultDTO;

import com.anytech.anytxn.business.base.account.domain.dto.AccountStatisticsInfoDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatisticsInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatisticsInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountStatisticsInfo;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-09-29 17:32
 **/
@Service
public class AccountStatisticsInfoServiceImpl implements IAccountStatisticsInfoService {

    private static final Logger logger = LoggerFactory.getLogger(AccountStatisticsInfoServiceImpl.class);
    
    @Autowired
    private AccountStatisticsInfoSelfMapper accountStatisticsInfoSelfMapper;

    @Autowired
    private AccountStatisticsInfoMapper accountStatisticsInfoMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;


    @Override
    public PageResultDTO<AccountStatisticsInfoDTO> findListAccStaInfo(Integer page,
                                                                      Integer rows,
                                                                      String accountManagementId) {
        logger.info("Query statistical account information by management account id, management account id: {}", accountManagementId);
        Page pageInfo = PageHelper.startPage(page, rows);
        List<AccountStatisticsInfoDTO> listAccStaInfoDTO = null;
        try {
            List<AccountStatisticsInfo> listAccStaInfo =
                    accountStatisticsInfoSelfMapper.selectAsiByMid(accountManagementId);

            if (!CollectionUtils.isEmpty(listAccStaInfo)) {
                listAccStaInfoDTO = new ArrayList<>();
                BeanCopier copierAccountStatisticsModelToDTO = BeanCopier.create(AccountStatisticsInfo.class, AccountStatisticsInfoDTO.class, false);
                for (AccountStatisticsInfo info : listAccStaInfo) {
                    AccountStatisticsInfoDTO dto = new AccountStatisticsInfoDTO();
                    copierAccountStatisticsModelToDTO.copy(info, dto, null);
                    listAccStaInfoDTO.add(dto);
                }
            }
        } catch (Exception e) {
            logger.error("Failed to call [{}] to query database table [{}]", "selectASIByMid", "ACCOUNT_STATISTICS_INFO", e);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR);
        }
        return new PageResultDTO<>(page, rows, pageInfo.getTotal(), pageInfo.getPages(),
                listAccStaInfoDTO);
    }

    @Override
    public AccountStatisticsInfoDTO findStaById(String statisticsId) {
        logger.info("Query statistical account information by statistical account id, statistical account id: {}", statisticsId);
        AccountStatisticsInfoDTO dto = null;
        try {
            AccountStatisticsInfo info = accountStatisticsInfoMapper.selectByPrimaryKey(statisticsId);
            if (null != info) {
                dto = new AccountStatisticsInfoDTO();
                BeanCopier copierAccountStatisticsModelToDTO = BeanCopier.create(AccountStatisticsInfo.class, AccountStatisticsInfoDTO.class, false);
                copierAccountStatisticsModelToDTO.copy(info, dto, null);
            }
        } catch (Exception e) {
            logger.error("Failed to call [{}] to query database table [{}]",
                    "selectByPrimaryKey", "ACCOUNT_STATISTICS_INFO", e);
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR);
        }
        return dto;
    }

    @Override
    public void modifyStaBaAndCode(String statisticsId, BigDecimal balance,
                                   String transactionTypeCode) {
        logger.info("Modify transaction type and transaction amount of statistical account, statistical account id: {}, modified amount: {}, modified transaction type: {}", statisticsId, balance,
                transactionTypeCode);
        try {
            accountStatisticsInfoSelfMapper
                    .updateBaAndCode(statisticsId, balance, transactionTypeCode);
        } catch (Exception e) {
            logger.error("Failed to call [{}] to update database table [{}]",
                    "updateBaAndCode", "ACCOUNT_STATISTICS_INFO", e);
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    TransactionEnum.DATABASE_ERROR.getReason());*/
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR);
        }

    }

    @Override
    public AccountStatisticsInfoDTO getAccountStatisticsInfo(String accountManagementId, String transactionTypeCode) {
        AccountStatisticsInfoDTO dto = null;
        try {
            AccountStatisticsInfo info = accountStatisticsInfoSelfMapper.selectByIdAndType(accountManagementId, transactionTypeCode);
            if (null != info) {
                dto = new AccountStatisticsInfoDTO();
                BeanCopier copierAccountStatisticsModelToDTO = BeanCopier.create(AccountStatisticsInfo.class, AccountStatisticsInfoDTO.class, false);
                copierAccountStatisticsModelToDTO.copy(info, dto, null);
            }
        } catch (Exception e) {
            logger.error("Failed to call [{}] to query database table [{}]",
                    "selectByIdAndType", "ACCOUNT_STATISTICS_INFO", e);
            /*throw new AnyTXNBusRuntimeException(TransactionEnum.DATABASE_ERROR.getCode(),
                    String.format("查询管理账户:[%s]交易类型:[%s]统计账户失败！", accountManagementId, transactionTypeCode));*/
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR, AccountingRepDetailEnum.Q_MA, accountManagementId, transactionTypeCode);
        }
        return dto;
    }


    @Override
    public List<AccountStatisticsInfoDTO> getNotOverRepaymentAsiByMid(String accountManagementId) {

        List<AccountStatisticsInfoDTO> resultList = null;
        try {
            List<AccountStatisticsInfo> infoList = accountStatisticsInfoSelfMapper.selectByMid(accountManagementId);

            if (!CollectionUtils.isEmpty(infoList)) {
                resultList = new ArrayList<>();
                for (AccountStatisticsInfo info : infoList) {
                    AccountStatisticsInfoDTO dto = new AccountStatisticsInfoDTO();
                    BeanCopier copierAccountStatisticsModelToDTO = BeanCopier.create(AccountStatisticsInfo.class, AccountStatisticsInfoDTO.class, false);
                    copierAccountStatisticsModelToDTO.copy(info, dto, null);
                    resultList.add(dto);
                }
            }
        } catch (Exception e) {
            logger.error("Failed to call [{}] to query database table [{}]",
                    "selectByMId", "ACCOUNT_STATISTICS_INFO", e);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR, AccountingRepDetailEnum.Q_ST, accountManagementId);
        }
        return resultList;
    }

    @Override
    public AccountStatisticsInfoDTO getOverPaymentAsi(String accountManagementId) {

        AccountStatisticsInfoDTO dto = null;
        try {
            AccountStatisticsInfo info = accountStatisticsInfoSelfMapper.selectOverPaymentAsi(accountManagementId);
            if (null != info) {
                dto = new AccountStatisticsInfoDTO();
                BeanCopier copierAccountStatisticsModelToDTO = BeanCopier.create(AccountStatisticsInfo.class, AccountStatisticsInfoDTO.class, false);
                copierAccountStatisticsModelToDTO.copy(info, dto, null);
            }
        } catch (Exception e) {
            logger.error("Failed to call [{}] to query database table [{}]",
                    "selectOverPaymentASI", "ACCOUNT_STATISTICS_INFO", e);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR, AccountingRepDetailEnum.Q_ST2, accountManagementId);
        }
        return dto;
    }

    @Override
    public void addAccStaInfo(AccStaDTO accStaDto) {
        AccountStatisticsInfo accountStatisticsInfo = new AccountStatisticsInfo();
        String sid = sequenceIdGen.generateId(TenantUtils.getTenantId());
        accountStatisticsInfo.setStatisticsId(sid);
        accountStatisticsInfo.setCurrency(accStaDto.getCurrency());
        accountStatisticsInfo.setTransactionTypeCode(TransactionTypeCodeEnum.AGGREGATION.getCode());
        accountStatisticsInfo.setBalance(BigDecimal.ZERO);
        accountStatisticsInfo.setStatementBalance(BigDecimal.ZERO);
        accountStatisticsInfo.setLastStatementDate(Constants.INITIAL_DATE);
        accountStatisticsInfo.setLastActivityDate(Constants.INITIAL_DATE);
        accountStatisticsInfo.setLastActivityAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setCycleToDateDebitAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setCycleToDateCreditAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setLastCycleDebitAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setLastCycleCreditAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setDebitTotalAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setCreditTotalAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setCreateTime(LocalDateTime.now());
        accountStatisticsInfo.setUpdateTime(LocalDateTime.now());
        accountStatisticsInfo.setUpdateBy(LoginUserUtils.getLoginUserName());
        accountStatisticsInfo.setVersionNumber(1L);
        accountStatisticsInfo.setAccountManagementId(accStaDto.getAccountManagementId());
        accountStatisticsInfo.setOrganizationNumber(accStaDto.getOrganizationNumber());
        accountStatisticsInfo.setCreateDate(accStaDto.getCreateDate());
        try {
            accountStatisticsInfoMapper.insertSelective(accountStatisticsInfo);
        } catch (Exception e) {
            logger.error("Failed to call [{}] to insert database table [{}]",
                    "addAccStaInfo", "ACCOUNT_STATISTICS_INFO", e);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR, AccountingRepDetailEnum.A_SU, sid);
        }
    }

    @Override
    public void registAccountStatisticsInfo(AccountStatisticsInfoDTO accountStatisticsInfoDTO) {
        try {
            AccountStatisticsInfo info = new AccountStatisticsInfo();
            BeanCopier copierAccountStatementDtoToModel = BeanCopier.create(AccountStatisticsInfoDTO.class, AccountStatisticsInfo.class, false);
            copierAccountStatementDtoToModel.copy(accountStatisticsInfoDTO, info, null);
            accountStatisticsInfoMapper.insertSelective(info);
        } catch (Exception e) {
            logger.error("Failed to call [{}] to insert database table [{}]",
                    "insertAccStaInfo", "ACCOUNT_STATISTICS_INFO", e);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR);
        }
    }

    @Override
    public void modifyAccountStatisticsInfo(AccountStatisticsInfoDTO accountStatisticsInfoDTO) {


        try {
            AccountStatisticsInfo info = new AccountStatisticsInfo();
            BeanCopier copierAccountStatementDtoToModel = BeanCopier.create(AccountStatisticsInfoDTO.class, AccountStatisticsInfo.class, false);
            copierAccountStatementDtoToModel.copy(accountStatisticsInfoDTO, info, null);
            accountStatisticsInfoMapper.updateByPrimaryKeySelective(info);
        } catch (Exception e) {
            logger.error("Failed to call [{}] to update database table [{}]",
                    "updateAccStaInfo", "ACCOUNT_STATISTICS_INFO", e);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_DATABASE_ERROR);
        }
    }

}
