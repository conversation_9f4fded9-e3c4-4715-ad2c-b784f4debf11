package com.anytech.anytxn.accounting.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlbalchkDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlbalchkResDTO;
import com.anytech.anytxn.accounting.base.service.IAccountsCheckService;
import com.anytech.anytxn.accounting.base.utils.AmountUtil;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlactbalSelfMapper;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlinsbalSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlactbal;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlinsbal;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvatbalSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvatbal;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlacgnDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmTransactionType;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmTransactionTypeSelfMapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
/**
 * <AUTHOR>
 * 分户某日(today)发生额
 */
@Service
public class AccountsCheckServiceImpl implements IAccountsCheckService {

    private static final Logger logger = LoggerFactory.getLogger(AccountsCheckServiceImpl.class);

    @Resource
    private AccountantGlactbalSelfMapper tAmsGlactbalSelfMapper;
    @Resource
    private AccountantGlinsbalSelfMapper tAmsGlinsbalSelfMapper;
    @Resource
    private AccountantGlvatbalSelfMapper amsGlvatbalSelfMapper;
    @Resource
    private ParmTransactionTypeSelfMapper parmTransactionTypeSelfMapper;
    @Resource
    private IOrganizationInfoService organizationInfoService;

    @Override
    public PageResultDTO<TAmsGlbalchkResDTO> getBalChkByPage(Integer page, Integer rows, String postingDate) {
        return null;
    }

    @Override
    public TAmsGlbalchkDTO accountCheckHandle(TPmsGlacgnDTO pmsGlacgnDTO) {
        return null;
    }

    /**
     * 分户某日(today)发生额
     * @param pmsGlacgnDTO              会计科目
     * @param today                     日期
     * @param accountManagementId       管理帐号id
     * @return   发生额
     */
    @Override
    public BigDecimal getGlBalance(TPmsGlacgnDTO pmsGlacgnDTO, LocalDate today, String accountManagementId) {
        logger.info("Get gl balance: glAcct={}, today={}, accountManagementId={}", pmsGlacgnDTO != null ? pmsGlacgnDTO.getGlAcct() : null, today, accountManagementId);

        // 最终余额
        BigDecimal acBalance = BigDecimal.ZERO;
        // 科目号
        String glAcct = pmsGlacgnDTO.getGlAcct();
        // 币种
        String currCode = pmsGlacgnDTO.getCurrCode();
        // 机构号
        String organizationNumber = pmsGlacgnDTO.getOrganizationNumber();
        // 分行号
        String branchid = pmsGlacgnDTO.getBranchid();
        // 总分核对规则
        String glAcctBalchkOption = pmsGlacgnDTO.getGlAcctBalchkOption();

        // 通用查询参数
        HashMap<String, Object> map = new HashMap<>(8);
        map.put("today",today);
        map.put("currency", currCode);
        map.put("organizationNumber", organizationNumber);
        map.put("branchid", branchid);
        map.put("accountManagementId", accountManagementId);

        //根据条件匹配对应的总分核对规则
        acBalance = getBigDecimal(pmsGlacgnDTO, today, accountManagementId, acBalance, glAcct, currCode, map, glAcctBalchkOption);

        logger.info("Get gl balance completed: glAcct={}, acBalance={}", glAcct, acBalance);
        return acBalance;
    }

    /**
     * 根据条件匹配对应的总分核对规则
     * @param pmsGlacgnDTO   会计科目
     * @param today          当前业务处理日
     * @param accountManagementId  管理账户号
     * @param acBalance      分户某日的发生额
     * @param glAcct         科目号
     * @param currCode       币种
     * @param map            通用查询参数
     * @param glAcctBalchkOption     总分核对规则
     * @return     对应规则对应的金额
     */
    private BigDecimal getBigDecimal(TPmsGlacgnDTO pmsGlacgnDTO, LocalDate today, String accountManagementId, BigDecimal acBalance,
                                     String glAcct, String currCode, HashMap<String, Object> map, String glAcctBalchkOption) {
        logger.info("Enter rule enumeration: glAcctBalchkOption={}", glAcctBalchkOption);
        switch (glAcctBalchkOption) {
            // 分期手续费递延核对（T_AMS_GLINSBAL）
            case "11":
                // 分期余额
                // 查询参数： 币种 + 机构号 + 处理日期 + [账户号]
                AccountantGlinsbal amsGlinsbal = tAmsGlinsbalSelfMapper.selectByAbs(map);
                logger.info("Input data: acBalance={}, query result: {}", acBalance, amsGlinsbal != null ? "found" : "not found");
                if (amsGlinsbal != null) {
                    // 未确认收入费用金额
                    acBalance = amsGlinsbal.getTermPostFeeAmt();
                }
                break;
            // 增值税垫付（ACCOUNTANT_GLVATBAL）
            case "12":
                map.put("glAcct", glAcct);
                AccountantGlvatbal amsGlvatbal;
                if (StringUtils.isNotEmpty(accountManagementId)){
                    amsGlvatbal = amsGlvatbalSelfMapper.selectSumByParams(map);
                }else {
                    //利息转表外垫付增值税
                    // 查询餐素： 机构号 + 分行号 + 科目号 + 币种
                    amsGlvatbal = amsGlvatbalSelfMapper.selectCheckSumByParams(map);
                }
                logger.info("Input data: acBalance={}, query result: {}", acBalance, amsGlvatbal != null ? "found" : "not found");
                if (amsGlvatbal != null) {
                    OrganizationInfoResDTO organizationInfo =
                            organizationInfoService.findOrganizationInfo(pmsGlacgnDTO.getOrganizationNumber());
                    if (StringUtils.isNotEmpty(accountManagementId)){
                        // 会计日期需是当日
                        if (today.compareTo(organizationInfo.getToday()) == 0
                                && organizationInfo.getToday().compareTo(amsGlvatbal.getUpdateDate()) == 0){
                            // 贷方余额
                            if ("C".equals(pmsGlacgnDTO.getGlBalFlag())){
                                acBalance = AmountUtil.subtractAmount(amsGlvatbal.getCrCurr(), amsGlvatbal.getDrCurr());
                                // 借方余额
                            }else {
                                acBalance =AmountUtil.subtractAmount(amsGlvatbal.getDrCurr(), amsGlvatbal.getCrCurr());
                            }
                        }
                    } else {
                        // 贷方金额 - 借方金额
                        acBalance = AmountUtil.subtractAmount(amsGlvatbal.getCbal(), amsGlvatbal.getDbal());
                    }
                }
                break;
            // 表内本金核对（T_AMS_GLACTBAL）
            case "01":
                //正常账户本金余额
                List<String> finaStatus1 = Collections.singletonList("0");
                // 交易类型
                List<ParmTransactionType> transactionTypes = parmTransactionTypeSelfMapper.selectByBalType("1", OrgNumberUtils.getOrg());
                for (ParmTransactionType transactionType : transactionTypes) {
                    AccountantGlactbal tAmsGlactbal = tAmsGlactbalSelfMapper.sumNormalAcctBalanceInfo(transactionType.getTransactionTypeCode(),
                            transactionType.getOrganizationNumber(),
                            finaStatus1,
                            currCode,
                            "A",
                            today,
                            accountManagementId);
                    logger.info("Input data: acBalance={}, query result: {}", acBalance, tAmsGlactbal != null ? "found" : "not found");
                    if (tAmsGlactbal != null) {
                        acBalance = AmountUtil.addAmount(acBalance,tAmsGlactbal.getBalance());
//                        break;
                    }
                }
                break;
            // 表内本金核对 - 逾期（T_AMS_GLACTBAL）
            case "13":
                //正常账户本金余额
                List<String> finaStatus1x = Collections.singletonList("3");
                // 交易类型
                List<ParmTransactionType> transactionTypes1x = parmTransactionTypeSelfMapper.selectByBalType("1", OrgNumberUtils.getOrg());
                for (ParmTransactionType transactionType : transactionTypes1x) {
                    AccountantGlactbal tAmsGlactbal = tAmsGlactbalSelfMapper.sumNormalAcctBalanceInfo(transactionType.getTransactionTypeCode(),
                            transactionType.getOrganizationNumber(),
                            finaStatus1x,
                            currCode,
                            "A",
                            today,
                            accountManagementId);
                    logger.info("Input data: acBalance={}, query result: {}", acBalance, tAmsGlactbal != null ? "found" : "not found");
                    if (tAmsGlactbal != null) {
                        acBalance = AmountUtil.addAmount(acBalance,tAmsGlactbal.getBalance());
//                        break;
                    }
                }
                break;
            // 表内本金核对 - 非应计（T_AMS_GLACTBAL）
            case "14":
                //正常账户本金余额
                List<String> finaStatus1z = Collections.singletonList("1");
                // 交易类型
                List<ParmTransactionType> transactionTypes1z = parmTransactionTypeSelfMapper.selectByBalType("1", OrgNumberUtils.getOrg());
                for (ParmTransactionType transactionType : transactionTypes1z) {
                    AccountantGlactbal tAmsGlactbal = tAmsGlactbalSelfMapper.sumNormalAcctBalanceInfo(transactionType.getTransactionTypeCode(),
                            transactionType.getOrganizationNumber(),
                            finaStatus1z,
                            currCode,
                            "A",
                            today,
                            accountManagementId);
                    logger.info("Input data: acBalance={}, query result: {}", acBalance, tAmsGlactbal != null ? "found" : "not found");
                    if (tAmsGlactbal != null) {
                        acBalance = AmountUtil.addAmount(acBalance,tAmsGlactbal.getBalance());
//                        break;
                    }
                }
                break;
            // 表内费用核对
            case "02":
                //正常账户费用余额
                List<String> finaStatus2 = Arrays.asList("0", "1", "3");
                // 交易类型
                List<ParmTransactionType> transactionTypes2 = parmTransactionTypeSelfMapper.selectByBalType("3",OrgNumberUtils.getOrg());
                for (ParmTransactionType transactionType : transactionTypes2) {
                    AccountantGlactbal tAmsGlactbal = tAmsGlactbalSelfMapper.sumNormalAcctBalanceInfo(transactionType.getTransactionTypeCode(),
                            transactionType.getOrganizationNumber(),
                            finaStatus2,
                            currCode,
                            "A",
                            today,
                            accountManagementId);
                    logger.info("Input data: acBalance={}, query result: {}", acBalance, tAmsGlactbal != null ? "found" : "not found");
                    if (tAmsGlactbal != null) {
                        acBalance = AmountUtil.addAmount(acBalance,tAmsGlactbal.getBalance());
//                        break;
                    }
                }
                break;
            // 表内利息核对
            case "03":
                //应计利息分户余额：
                List<String> intInd1 = Arrays.asList("1", "2");
                map.put("intInds", intInd1);
                //正常账户费用余额
                List<String> finaStatus3 = Arrays.asList("0", "3");
                // 交易类型
                List<ParmTransactionType> transactionTypes3 = parmTransactionTypeSelfMapper.selectByBalType("2",OrgNumberUtils.getOrg());
                for (ParmTransactionType transactionType : transactionTypes3) {
                    AccountantGlactbal tAmsGlactbal = tAmsGlactbalSelfMapper.selectSumAccrualInterest(transactionType.getTransactionTypeCode(),
                            transactionType.getOrganizationNumber(),
                            finaStatus3,
                            currCode,
                            "A",
                            intInd1,
                            today,
                            accountManagementId);
                    logger.info("Input data: acBalance={}, query result: {}", acBalance, tAmsGlactbal != null ? "found" : "not found");
                    if (tAmsGlactbal != null) {
                        acBalance = AmountUtil.addAmount(acBalance,tAmsGlactbal.getBalance());
//                        break;
                    }
                }
                break;
            // 非应计利息核对
            case "04":
                //表外应收未收利息分户余额
                List<String> intInd2 = Arrays.asList("0","1", "2", "3");
                map.put("intInds", intInd2);
                // 交易类型
                List<ParmTransactionType> transactionTypes4 = parmTransactionTypeSelfMapper.selectByBalType("2",OrgNumberUtils.getOrg());
                for (ParmTransactionType transactionType : transactionTypes4) {
                    AccountantGlactbal tAmsGlactbal = tAmsGlactbalSelfMapper.selectSumNotAccrualInterest(transactionType.getTransactionTypeCode(),
                            transactionType.getOrganizationNumber(),
                            "1",
                            currCode,
                            "N",
                            intInd2,
                            today,
                            accountManagementId);
                    logger.info("Input data: acBalance={}, query result: {}", acBalance, tAmsGlactbal != null ? "found" : "not found");
                    if (tAmsGlactbal != null) {
                        acBalance = AmountUtil.addAmount(acBalance,tAmsGlactbal.getBalance());
//                        break;
                    }
                }
                break;
            // 分期垫付核对
            case "05":
                map.put("absStatus", "A");
                //分期未下账本金余额
                AccountantGlinsbal amsGlinsbal1 = tAmsGlinsbalSelfMapper.selectByNotAbs(map);
                logger.info("Input data: acBalance={}, query result: {}", acBalance, amsGlinsbal1 != null ? "found" : "not found");
                if (amsGlinsbal1 != null) {
                    acBalance = amsGlinsbal1.getUnpostedAmt();
                }
                break;
            // 核销本金核对
            case "06":
                map.put("finaStatus", "2");
                map.put("absStatus", "N");
                map.put("balType", "1");
                // 交易类型
                List<ParmTransactionType> transactionTypes5 = parmTransactionTypeSelfMapper.selectByBalType("1",OrgNumberUtils.getOrg());
                for (ParmTransactionType transactionType : transactionTypes5) {
                    AccountantGlactbal tAmsGlactbal = tAmsGlactbalSelfMapper.selectHx(transactionType.getTransactionTypeCode(),
                            transactionType.getOrganizationNumber(),
                            "2",
                            currCode,
                            "N",
                            today,
                            accountManagementId);
                    logger.info("Input data: acBalance={}, query result: {}", acBalance, tAmsGlactbal != null ? "found" : "not found");
                    if (tAmsGlactbal != null) {
                        acBalance = AmountUtil.addAmount(acBalance,tAmsGlactbal.getBalance());
//                        break;
                    }
                }
                break;
            // 核销费用核对
            case "07":
                map.put("finaStatus", "2");
                map.put("absStatus", "N");
                map.put("balType", "3");
                // 交易类型
                List<ParmTransactionType> transactionTypes6 = parmTransactionTypeSelfMapper.selectByBalType("3",OrgNumberUtils.getOrg());
                for (ParmTransactionType transactionType : transactionTypes6) {
                    AccountantGlactbal tAmsGlactbal = tAmsGlactbalSelfMapper.selectHx(transactionType.getTransactionTypeCode(),
                            transactionType.getOrganizationNumber(),
                            "2",
                            currCode,
                            "N",
                            today,
                            accountManagementId);
                    logger.info("Input data: acBalance={}, query result: {}", acBalance, tAmsGlactbal != null ? "found" : "not found");
                    if (tAmsGlactbal != null) {
                        acBalance = AmountUtil.addAmount(acBalance,tAmsGlactbal.getBalance());
//                        break;
                    }
                }
                break;
            // 核销利息核对
            case "08":
                map.put("finaStatus", "2");
                map.put("absStatus", "N");
                map.put("balType", "2");
                // 交易类型
                List<ParmTransactionType> transactionTypes7 = parmTransactionTypeSelfMapper.selectByBalType("2",OrgNumberUtils.getOrg());
                for (ParmTransactionType transactionType : transactionTypes7) {
                    AccountantGlactbal tAmsGlactbal = tAmsGlactbalSelfMapper.selectHx(transactionType.getTransactionTypeCode(),
                            transactionType.getOrganizationNumber(),
                            "2",
                            currCode,
                            "N",
                            today,
                            accountManagementId);
                    logger.info("Input data: acBalance={}, query result: {}", acBalance, tAmsGlactbal != null ? "found" : "not found");
                    if (tAmsGlactbal != null) {
                        acBalance = AmountUtil.addAmount(acBalance,tAmsGlactbal.getBalance());
//                        break;
                    }
                }
                break;
            // 溢缴款核对
            case "09":
                //溢缴款科目分户余额
                map.put("txnTypeCode", "OV999");
                AccountantGlactbal overPanyment  = tAmsGlactbalSelfMapper.selectSumOverPanyment(map);
                logger.info("Input data: acBalance={}, query result: {}", acBalance, overPanyment != null ? "found" : "not found");
                if (overPanyment != null) {
                    acBalance = overPanyment.getBalance();
                }
                break;
            // 表外代保管核对
            case "10":
                map.put("absStatus", "A");
                //代保管科目总分核对
                AccountantGlinsbal amsGlinsbal2 = tAmsGlinsbalSelfMapper.selectByAbs(map);
                logger.info("Input data: acBalance={}, query result: {}", acBalance, amsGlinsbal2 != null ? "found" : "not found");
                if (amsGlinsbal2 != null) {
                    acBalance = amsGlinsbal2.getUnpostedAmt();
                }
                AccountantGlactbal absn  = tAmsGlactbalSelfMapper.selectByAbsStatus(map);
                if (absn != null) {
                    acBalance = AmountUtil.addAmount(acBalance, absn.getBalance());
                }
                break;
            default:
                break;
        }
        logger.info("AcBalance after rule processing: {}", acBalance);
        return acBalance;
    }
}
