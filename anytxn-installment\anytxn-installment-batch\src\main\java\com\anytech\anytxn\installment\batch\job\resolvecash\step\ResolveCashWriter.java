package com.anytech.anytxn.installment.batch.job.resolvecash.step;

import com.anytech.anytxn.business.dao.installment.model.InstallRecord;
import com.anytech.anytxn.installment.batch.job.resolvecash.service.InstallCashLoanService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2020-02-09 20:12
 **/
public class ResolveCashWriter implements ItemWriter<InstallRecord> {

    private static final Logger logger = LoggerFactory.getLogger(ResolveCashWriter.class);

    @Autowired
    private InstallCashLoanService installCashLoanService;
    @Override
    public void write(Chunk<? extends InstallRecord> list) throws Exception {
        int size = list != null ? list.size() : 0;
        logger.info("Starting resolve cash install loan: itemCount={}", size);
        //写入待入账交易信息表
        installCashLoanService.resolveCashInstallLoan((List<? extends InstallRecord>) list);
        logger.info("Resolve cash install loan completed successfully: itemCount={}", size);
    }
}
