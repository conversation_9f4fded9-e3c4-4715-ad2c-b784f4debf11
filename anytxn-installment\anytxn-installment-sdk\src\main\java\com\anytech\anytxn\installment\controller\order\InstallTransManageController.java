package com.anytech.anytxn.installment.controller.order;

import com.anytech.anytxn.installment.base.domain.dto.AccountManagementRecordedDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallEntryDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallEntryResDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallTradingDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallTradingSearchKeyDTO;
import com.anytech.anytxn.installment.base.domain.dto.OperateInstallFeeResDto;
import com.anytech.anytxn.installment.base.domain.dto.SingleInstallDTO;
import com.anytech.anytxn.installment.base.enums.InstallRepDetailEnum;
import com.anytech.anytxn.installment.base.service.IInstallBillEntryService;
import com.anytech.anytxn.installment.base.service.IInstallSingleEntryService;
import com.anytech.anytxn.installment.base.service.IInstallStagingListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 账单、单笔分期查询及录入
 *
 * <AUTHOR>
 * @date 2019/9/2
 */
@Api(tags = "账单、单笔分期查询及录入")
@RestController
public class InstallTransManageController extends BizBaseController {

    private static final Logger logger = LoggerFactory.getLogger(InstallTransManageController.class);

    @Autowired
    private IInstallBillEntryService installBillEntryService;

    @Autowired
    private IInstallSingleEntryService installSingleEntryService;

    @Autowired
    private IInstallStagingListService installStagingListService;

    @ApiOperation(value = "根据账号查询账单分期", notes = "根据账号查询账单分期")
    @GetMapping(value = "/install/searchBillInstall/accountManagementId/{accountManagementId}")
    public AnyTxnHttpResponse<List<InstallTradingDTO>> getBillInstall(@PathVariable String accountManagementId) {
        logger.info("Starting get bill install: accountManagementId={}", accountManagementId);
        List<InstallTradingDTO> installTradingDtos = installBillEntryService.findBillInstall(accountManagementId);
        logger.info("Get bill install completed successfully: accountManagementId={}, resultCount={}",
                   accountManagementId, installTradingDtos != null ? installTradingDtos.size() : 0);
        return AnyTxnHttpResponse.success(installTradingDtos);
    }

    @ApiOperation(value = "根据管理账号匹配查询卡号", notes = "根据管理账号匹配查询卡号")
    @GetMapping(value = "/install/searchCardNumber/accountManagementId/{accountManagementId}")
    public AnyTxnHttpResponse<List<AccountManagementRecordedDTO>> getCardNumberByAccountManagementId(@PathVariable String accountManagementId) {
        logger.info("Starting get card number by account management id: accountManagementId={}", accountManagementId);
        List<AccountManagementRecordedDTO> accountManagementRecordedDTos = installBillEntryService.findCardNumberByAccountManagementId(accountManagementId);
        logger.info("Get card number by account management id completed successfully: accountManagementId={}, resultCount={}",
                   accountManagementId, accountManagementRecordedDTos != null ? accountManagementRecordedDTos.size() : 0);
        return AnyTxnHttpResponse.success(accountManagementRecordedDTos);
    }

    @ApiOperation(value = "账单分期交易录入", notes = "账单分期交易录入")
    @PostMapping(value = "/install/billEntry")
    public AnyTxnHttpResponse<InstallEntryResDTO> billInstallTransEntry(@Valid @RequestBody InstallEntryDTO installEntryDTO) {
        logger.info("Starting bill install trans entry: cardNumber={}", installEntryDTO != null ? installEntryDTO.getCardNumber() : null);
        InstallEntryResDTO billEntry = installBillEntryService.billInInstallment(installEntryDTO);
        logger.info("Bill install trans entry completed successfully: cardNumber={}", installEntryDTO != null ? installEntryDTO.getCardNumber() : null);
        return AnyTxnHttpResponse.success(billEntry, InstallRepDetailEnum.BI_IN_T.message());
    }

    @ApiOperation(value = "根据账号查询单笔分期", notes = "根据账号查询单笔分期")
    @GetMapping(value = "/install/searchSingleInstall/accountManagementId/{accountManagementId}/installFlag/{installFlag}")
    public AnyTxnHttpResponse<List<SingleInstallDTO>> getSingleInstall(@PathVariable String accountManagementId, @PathVariable String installFlag) {
        logger.info("Starting get single install: accountManagementId={}, installFlag={}", accountManagementId, installFlag);
        List<SingleInstallDTO> singleInstallDtos = installSingleEntryService.findByAccountManagementId(accountManagementId, installFlag);
        logger.info("Get single install completed successfully: accountManagementId={}, resultCount={}",
                   accountManagementId, singleInstallDtos != null ? singleInstallDtos.size() : 0);
        return AnyTxnHttpResponse.success(singleInstallDtos);
    }

    @ApiOperation(value = "单笔分期列表查询", notes = "单笔分期列表查询")
    @PostMapping(value = "/install/searchSingleInstallList")
    public AnyTxnHttpResponse<List<InstallTradingDTO>> getSingleInstallList(@RequestBody(required = false) InstallTradingSearchKeyDTO installTradingSearchKeyDTO) {
        logger.info("Starting get single install list");
        List<InstallTradingDTO> installSingleList = installSingleEntryService.findInstallSingleList(installTradingSearchKeyDTO);
        logger.info("Get single install list completed successfully: resultCount={}",
                   installSingleList != null ? installSingleList.size() : 0);
        return AnyTxnHttpResponse.success(installSingleList);
    }

    @ApiOperation(value = "单笔分期交易录入", notes = "单笔分期交易录入")
    @PostMapping(value = "/install/singleEntry")
    public AnyTxnHttpResponse<InstallEntryResDTO> singleInstallTransEntry(@Valid @RequestBody InstallEntryDTO installEntryDTO) {
        logger.info("Starting single install trans entry: cardNumber={}", installEntryDTO != null ? installEntryDTO.getCardNumber() : null);
        InstallEntryResDTO installEntryResDTO = installSingleEntryService.singleInstallment(installEntryDTO);
        logger.info("Single install trans entry completed successfully: cardNumber={}", installEntryDTO != null ? installEntryDTO.getCardNumber() : null);
        return AnyTxnHttpResponse.success(installEntryResDTO, InstallRepDetailEnum.SI_IT.message());
    }

    @ApiOperation(value = "可分期列表查询", notes = "可分期列表查询")
    @PostMapping(value = "/install/searchStagingList")
    public AnyTxnHttpResponse<List<InstallTradingDTO>> getStagingList(@RequestBody(required = false) InstallTradingSearchKeyDTO installTradingSearchKeyDTO) {
        logger.info("Starting get staging list");
        List<InstallTradingDTO> installTradingDtos = installStagingListService.findInstallTradingByOptions(installTradingSearchKeyDTO);
        logger.info("Get staging list completed successfully: resultCount={}",
                   installTradingDtos != null ? installTradingDtos.size() : 0);
        return AnyTxnHttpResponse.success(installTradingDtos);
    }

    @ApiOperation(value = "计算费用(信用卡运营用)", notes = "计算费用")
    @PostMapping(value = "/install/fee")
    public AnyTxnHttpResponse<OperateInstallFeeResDto> calculaeFee(@RequestBody(required = false) InstallTradingSearchKeyDTO installTradingSearchKeyDTO) {
        logger.info("Starting calculate fee");
        OperateInstallFeeResDto operateInstallFeeResDto = installStagingListService.calculateFeeForOperate(installTradingSearchKeyDTO);
        logger.info("Calculate fee completed successfully");
        return AnyTxnHttpResponse.success(operateInstallFeeResDto);
    }

}
