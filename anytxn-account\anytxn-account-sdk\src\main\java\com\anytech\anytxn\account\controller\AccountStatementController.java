package com.anytech.anytxn.account.controller;

import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.service.IAccountStatementInfoService;
import com.anytech.anytxn.account.base.utils.AnyTxnHttpResponseHelper;
import com.anytech.anytxn.business.base.account.domain.dto.ModifyPaymentDueDateDTO;
import com.anytech.anytxn.business.base.transaction.domain.dto.CardStatementDTO2;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.business.base.account.domain.dto.AccountStatementInfoDTO;

import com.anytech.anytxn.business.base.transaction.domain.dto.CardStatementDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 账单API
 *
 * <AUTHOR>
 * @date 2019-01-18 18:28
 **/
@RestController
@Tag(name = "账单账户操作")
public class AccountStatementController extends BizBaseController {

    private static final Logger logger = LoggerFactory.getLogger(AccountStatementController.class);

    @Autowired
    private IAccountStatementInfoService accountStatementInfoService;

    /**
     * 查询最近24期内的账单
     */
    @Operation(summary = "查询最近24期内的账单")
    @GetMapping("/account/lastedStatementInfo")
    AnyTxnHttpResponse<List<AccountStatementInfoDTO>> queryLastedStatementInfo(
            @Parameter(name = "accountManageInfoId", required = false, description = "管理账户Id")@RequestParam(value = "accountManageInfoId", required = false) String accountManageInfoId,
            @Parameter(name = "cardNumber", required = false, description = "卡号")@RequestParam(value = "cardNumber", required = false) String cardNumber) {
            logger.info("Query lasted statement info request received: accountManageInfoId={}, cardNumber={}", accountManageInfoId, cardNumber);
            List<AccountStatementInfoDTO> result = accountStatementInfoService.findLastedStatementInfo(accountManageInfoId, cardNumber);
            logger.info("Query lasted statement info completed: accountManageInfoId={}, cardNumber={}, resultSize={}", accountManageInfoId, cardNumber, result.size());
            return AnyTxnHttpResponse.success(result);
    }

    @Operation(summary = "更新延迟还款日")
    @PutMapping("/account/modifyPaymentDueDate")
    public AnyTxnHttpResponse modifyPaymentDueDate(@RequestBody ModifyPaymentDueDateDTO modifyPaymentDueDateDto) {
            logger.info("Modify payment due date request received");
            accountStatementInfoService.modifyPaymentDueDate(modifyPaymentDueDateDto);
            logger.info("Modify payment due date completed successfully");
        return AnyTxnHttpResponseHelper.success(AccountingRepDetailEnum.US.getCnMsg());
    }

    /**
     * 查询个人卡账单信息
     */
    @Operation(summary = "查询个人卡账单")
    @PostMapping("/account/personalStatement")
    AnyTxnHttpResponse<PageResultDTO<CardStatementDTO>> queryList(@RequestBody CardStatementDTO2 req) {
        logger.info("Query personal statement request received");
        PageResultDTO<CardStatementDTO> result = accountStatementInfoService.findCardStatementInfos(req);
        logger.info("Query personal statement completed: resultSize={}", result.getTotal());
        return AnyTxnHttpResponse.success(result);
    }
}
