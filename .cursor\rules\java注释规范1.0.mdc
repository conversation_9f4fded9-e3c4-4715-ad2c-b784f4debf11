---
description: Java代码注释规范与Javadoc指南 # 规则的简要描述，让用户或系统清楚其作用
globs:
  - **/*.java" # 指定该规则应应用于所有子目录下的所有.java文件
alwaysApply: false # 默认为false，因为我们通过globs指定了应用范围
---
## 注释规范
1、不得修改业务逻辑。只修改注释，不修改其他
2、必须按照下面的规则，对每个类进行注释的优化。
3、不要有java代码遗漏
4、可以按照目录的顺序逐个修改
### 1. 类与方法注释规范

#### 【强制】Javadoc规范
- 类、类属性、类方法的注释必须使用 Javadoc 规范，格式为 `/**内容*/`，禁止使用 `//xxx`


#### 【强制】Javadoc规范
- 类、类属性、类方法的注释必须使用 Javadoc 规范，格式为 `/**内容*/`，禁止使用 `//xxx`

#### 【强制】抽象方法注释
- 所有抽象方法（含接口方法）必须用 Javadoc 注释，需包含功能说明、参数、返回值、异常，以及子类实现要求或调用注意事项。

#### 【强制】创建者信息
- 所有类必须添加创建者信息。

### 2. 代码内注释规则

#### 【强制】注释格式
- 方法内部单行注释：在被注释语句上方另起一行，使用 `//`
- 多行注释：使用 `/* */`，并与代码对齐

#### 【强制】枚举注释
- 枚举类型字段必须注释每个数据项的用途

### 3. 注释语言与维护

#### 【强制】注释语言
- 优先用中文清晰注释，专有名词保留英文（如"TCP"无需翻译）

#### 【强制】注释更新
- 修改代码时同步更新注释，尤其是参数、返回值、异常和核心逻辑部分

### 4. 特殊注释标记

#### 【参考】标记使用
- 使用标记需注明人员和时间，定期清理：
  - **TODO**：(标记人，标记时间，[预计处理时间])
    > 标识未实现功能，适用于类、接口、方法。
  - **FIXME**：(标记人，标记时间，[预计处理时间])
    > 标记存在错误且需紧急修复的代码。

### 5. 注释模板参考

#### 【参考】类注释模板
```java
/**
 * 类描述：该类的主要功能描述
 *
 * <AUTHOR>
 * @date 创建日期
 * @version 版本号
 */
```
- <AUTHOR>
- @date 创建日期：当不存在创建日期时，默认使用创建日期为2024/03/21
- @version 版本号：当不存在版本号时，默认使用版本号为1.0
#### 【参考】方法注释模板
```java
/**
 * 方法描述：该方法的主要功能描述
 *
 * @param param1 参数1的说明
 * @param param2 参数2的说明
 * @return 返回值的说明
 * @throws Exception 异常说明
 */
```

#### 【参考】字段注释模板
```java
/**
 * 字段描述：该字段的主要用途说明
 */
private String fieldName;
```

#### 【参考】接口注释模板
```java
/**
 * 接口描述：该接口的主要功能描述
 *
 * <AUTHOR>
 * @date 创建日期
 * @version 版本号
 */
```

#### 【参考】枚举注释模板
```java
/**
 * 枚举描述：该枚举的主要用途说明
 *
 * <AUTHOR>
 * @date 创建日期
 */
public enum EnumName {
    /**
     * 枚举值1的说明
     */
    VALUE1,
    
    /**
     * 枚举值2的说明
     */
    VALUE2
}
```

#### 【参考】TODO注释模板
```java
// TODO: (作者名, 日期) 待完成的功能说明
```

#### 【参考】FIXME注释模板
```java
// FIXME: (作者名, 日期) 需要修复的问题说明

```
