package com.anytech.anytxn.account.base.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * httpClient工具类
 *
 */
public class HttpClientUtils {

    private static final Logger logger = LoggerFactory.getLogger(HttpClientUtils.class);

    private static final String CONTENT_TYPE = "application/json; charset=UTF-8";

    private static PoolingHttpClientConnectionManager connMgr;
    private static RequestConfig requestConfig;
    private static final int MAX_TIMEOUT = 60000;

    private HttpClientUtils() {
    }

    static {
        // 设置连接池
        connMgr = new PoolingHttpClientConnectionManager();
        // 设置连接池大小
        connMgr.setMaxTotal(200);
        connMgr.setDefaultMaxPerRoute(connMgr.getMaxTotal());

        RequestConfig.Builder configBuilder = RequestConfig.custom();
        // 设置连接超时时间，单位毫秒。
        configBuilder.setConnectTimeout(MAX_TIMEOUT);
        // 请求获取数据的超时时间，单位毫秒。 如果访问一个接口，多少时间内无法返回数据，就直接放弃此次调用。
        configBuilder.setSocketTimeout(MAX_TIMEOUT);
        // 设置从连接池获取连接实例的超时
        configBuilder.setConnectionRequestTimeout(MAX_TIMEOUT);
        requestConfig = configBuilder.build();
    }

    /**
     * post 请求
     * @param uri
     * @return
     * @throws IOException
     */
    public static String post(String uri) throws IOException {
        return post(uri, null, null);
    }

    /**
     * post 请求
     * @param uri
     * @param content
     * @return
     * @throws IOException
     */
    public static String post(String uri, String content) throws IOException {
        return post(uri, content, null);
    }

    /**
     * post 请求
     * @param uri
     * @param content
     * @param params
     * @return
     * @throws IOException
     */
    public static String post(String uri, String content, Map<String, String> params) throws IOException {
        return post(uri, content, params, null);
    }

    /**
     * post 实现
     * @param uri
     * @param content
     * @param params
     * @param headers
     * @return
     * @throws IOException
     */
    public static String post(String uri, String content, Map<String, String> params, Map<String, String> headers) throws IOException {
        if (StringUtils.isEmpty(uri)) {
            return "";
        }
        RequestBuilder requestBuilder = RequestBuilder.post(uri);
        requestBuilder.setCharset(StandardCharsets.UTF_8);
        requestBuilder.setConfig(requestConfig);
        handleParams(requestBuilder, params);
        if (content != null) {
            StringEntity stringEntity = new StringEntity(content, ContentType.create("application/json", "UTF-8"));
            requestBuilder.setEntity(stringEntity);
        } else {
            requestBuilder.addHeader(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE);
        }
        handleHeaders(requestBuilder, headers);
        HttpUriRequest request = requestBuilder.build();
        return process2(request);
    }


    /**
     * 处理请求参数
     * @param requestBuilder
     * @param params
     */
    private static void handleParams(RequestBuilder requestBuilder, Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return;
        }
        for (Map.Entry<String, String> entry : params.entrySet()) {
            String value = entry.getValue() == null ? "" : entry.getValue();
            requestBuilder.addParameters(new BasicNameValuePair(entry.getKey(), value));
        }
    }

    /**
     * 请求头处理
     * @param requestBuilder
     * @param headers
     */
    private static void handleHeaders(RequestBuilder requestBuilder, Map<String, String> headers) {
        if (headers == null || headers.isEmpty()) {
            return;
        }
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            String value = entry.getValue() == null ? "" : entry.getValue();
            requestBuilder.addHeader(entry.getKey(), value);
        }
    }

    private static String process2(HttpUriRequest request) throws IOException {
        String content = "";
        CloseableHttpResponse response = null;
        try {
            CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(connMgr).build();
            response = httpClient.execute(request);
            if (response.getStatusLine().getStatusCode() == 200) {
                HttpEntity entity = response.getEntity();
                content = EntityUtils.toString(entity, "UTF-8");
                EntityUtils.consume(entity);
            } else {
                request.abort();
                //throw new RuntimeException("HttpClient error, status=" + response.getStatusLine().getStatusCode() + ",message:" + response.getStatusLine().getReasonPhrase())
            }
        } catch (IOException e) {
            logger.error("http post request exception", e);
        } finally {
            if (null != response) {
                EntityUtils.consume(response.getEntity());
            }
        }
        return content;
    }

}

