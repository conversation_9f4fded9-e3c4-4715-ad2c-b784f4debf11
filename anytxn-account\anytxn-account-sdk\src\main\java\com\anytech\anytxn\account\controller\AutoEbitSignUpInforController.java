package com.anytech.anytxn.account.controller;

import com.anytech.anytxn.account.base.utils.AnyTxnHttpResponseHelper;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.service.AutoEbitSignUpInforService;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.business.base.card.domain.dto.AutoEbitSignUpInforDTO;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @date 2021-05-13 15:08
 **/
@RestController
@Tag(name = "自扣和临扣签约表接口")
public class AutoEbitSignUpInforController extends BizBaseController {

    private static final Logger logger = LoggerFactory.getLogger(AutoEbitSignUpInforController.class);

    @Resource
    private AutoEbitSignUpInforService autoEbitSignUpInforService;


    @Operation(summary = "更新约定扣款记录")
    @PostMapping(path = "/account/updateAutoInfo")
    public AnyTxnHttpResponse updateAutoInfo(@RequestBody AutoEbitSignUpInforDTO autoEbitSignUpInfor) {
        logger.info("Update auto debit info request received: id={}", autoEbitSignUpInfor.getId());
        
        if (StringUtils.isEmpty(autoEbitSignUpInfor.getId())){
            autoEbitSignUpInforService.addAutoInfo(autoEbitSignUpInfor);
            logger.info("Add auto debit info completed successfully");
            return AnyTxnHttpResponseHelper.success(AccountingRepDetailEnum.AUTO_OF.getCnMsg());
        }else{
            autoEbitSignUpInforService.updateAutoInfo(autoEbitSignUpInfor);
            logger.info("Update auto debit info completed successfully");
            return AnyTxnHttpResponseHelper.success(AccountingRepDetailEnum.AUTO_UPDATE_OF.getCnMsg());
        }
    }

    @Operation(summary = "查询签约记录")
    @GetMapping("/account/auto/{contractType}/{customerId}/{autoDebitType}")
    AnyTxnHttpResponse<AutoEbitSignUpInforDTO> findStatisticsInfoDetailById(
            @PathVariable(value = "contractType") String contractType,
            @PathVariable(value = "customerId") String customerId,
            @PathVariable(value = "autoDebitType")String autoDebitType) {
        logger.info("Find auto debit info request received: contractType={}, customerId={}, autoDebitType={}", contractType, customerId, autoDebitType);
        
        AutoEbitSignUpInforDTO autoEbitSignUpInfor = autoEbitSignUpInforService.findStaById(contractType, customerId, autoDebitType);
        
        logger.info("Find auto debit info completed successfully");
        return AnyTxnHttpResponse.success(autoEbitSignUpInfor);
    }


    @Operation(summary = "更新临时扣款记录")
    @PostMapping(path = "/account/updateAutoTempInfo")
    public AnyTxnHttpResponse updateAutoTempInfo(@RequestBody AutoEbitSignUpInforDTO autoEbitSignUpInfor) {
        logger.info("Update auto temp debit info request received: id={}", autoEbitSignUpInfor.getId());
        
        if (StringUtils.isEmpty(autoEbitSignUpInfor.getId())){
            autoEbitSignUpInforService.addAutoTempInfo(autoEbitSignUpInfor);
            logger.info("Add auto temp debit info completed successfully");
            return AnyTxnHttpResponseHelper.success(AccountingRepDetailEnum.AUTO_TEMP_OF.getCnMsg());
        }else{
            autoEbitSignUpInforService.updateAutoTempInfo(autoEbitSignUpInfor);
            logger.info("Update auto temp debit info completed successfully");
            return AnyTxnHttpResponseHelper.success(AccountingRepDetailEnum.AUTO_TEMP_UPDATE_OF.getCnMsg());
        }
    }


    @Operation(summary = "根据客户号查询签约记录")
    @PostMapping("/account/auto")
    AnyTxnHttpResponse<String> findInfoByCustId(@RequestBody AutoEbitSignUpInforDTO autoEbitSignUpInfor) {
        logger.info("Find info by customer id request received: customerId={}", autoEbitSignUpInfor.getCustomerId());
        
        autoEbitSignUpInforService.selectByCustid(autoEbitSignUpInfor);
        
        logger.info("Find info by customer id completed successfully");
        return AnyTxnHttpResponse.success(AccountingRepDetailEnum.AUTO_TEMP.getCnMsg());
    }
}
