package com.anytech.anytxn.account.job.egprepayschedule.steps;

import com.anytech.anytxn.account.base.domain.dto.BtiBlockCodeUpdateReportDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardBasicInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.card.model.CardBasicInfo;
import com.anytech.anytxn.business.dao.transaction.mapper.CustomerBTInfoSelfMapper;
import com.anytech.anytxn.business.dao.transaction.mapper.CustomerBtiInfoMapper;
import com.anytech.anytxn.business.dao.transaction.model.CustomerBtiInfo;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmBtiGroup;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmBtiGroupMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmBtiGroupSelfMapper;
import com.anytech.anytxn.parameter.common.service.system.OrganizationInfoServiceImpl;
import com.anytech.anytxn.transaction.service.TransactionFileWriteHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
/**
 * 月末批，更新BTI组标志和封锁标志
 *
 * <AUTHOR>
 * @date 2022/01/24
 */
@Component
public class UpdateEgpRepayScheduleInfoTasklet implements Tasklet {

    private static final Logger logger = LoggerFactory.getLogger(UpdateEgpRepayScheduleInfoTasklet.class);

    @Autowired
    private CustomerBTInfoSelfMapper customerBTInfoSelfMapper;
    @Autowired
    private CustomerBtiInfoMapper customerBtiInfoMapper;
    @Resource
    private OrganizationInfoServiceImpl organizationInfoService;
    @Autowired
    private ParmBtiGroupSelfMapper parmBtiGroupSelfMapper;
    @Autowired
    private ParmBtiGroupMapper parmBtiGroupMapper;
    @Autowired
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;
    @Autowired
    private CardBasicInfoMapper cardBasicInfoMapper;
    @Resource
    private TransactionFileWriteHandler transactionFileWriteHandler;
    @Resource
    private AnytxnFilePathConfig btiBlockCodeUpdateReportOutFilePathConfig;
    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) throws Exception {
        List<CustomerBtiInfo> customerBtiInfos = customerBTInfoSelfMapper.selectAll(OrgNumberUtils.getOrg());
        if (customerBtiInfos.size() != 0){
            List<BtiBlockCodeUpdateReportDTO> btiBlockCodeUpdateReportDTOS = new ArrayList<>(16);
            for (CustomerBtiInfo customerBtiInfo:customerBtiInfos){
                int n = 0;
                OrganizationInfoResDTO organizationInfoResDTO = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
                ParmBtiGroup parmBtiGroup = parmBtiGroupSelfMapper.selectByTableIdAndOrgNum("BTI001",OrgNumberUtils.getOrg());
                String date = parmBtiGroup.getEgpExpiry().substring(0,4) + "-" + parmBtiGroup.getEgpExpiry().substring(4,6) + "-" + parmBtiGroup.getEgpExpiry().substring(6);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                LocalDate expireDate = LocalDate.parse(date,formatter);
                if (3 == customerBtiInfo.getBtiAgeCode()){
                    if ("A".equals(parmBtiGroup.getBtiGroupIndicator())){
                        customerBtiInfo.setBtiBlockIndicator("Y");
                        n = customerBtiInfoMapper.updateByPrimaryKeySelective(customerBtiInfo);
                    }
                    /*if ("B".equals(parmBtiGroup.getBtiGroupIndicator()) && expireDate.isBefore(organizationInfoResDTO.getToday()) && customerBtiInfo.getBtiIndex() > 12){
                        customerBtiInfo.setBtiBlockIndicator("Y");
                        customerBtiInfoMapper.updateByPrimaryKeySelective(customerBtiInfo);
                        parmBtiGroup.setBtiGroupIndicator("A");
                        parmBtiGroupMapper.updateByPrimaryKeySelective(parmBtiGroup);

                    }
                    if ("C".equals(parmBtiGroup.getBtiGroupIndicator()) && expireDate.isBefore(organizationInfoResDTO.getToday()) && customerBtiInfo.getBtiIndex() > 12){
                        customerBtiInfo.setBtiBlockIndicator("Y");
                        customerBtiInfoMapper.updateByPrimaryKeySelective(customerBtiInfo);
                        parmBtiGroup.setBtiGroupIndicator("A");
                        parmBtiGroupMapper.updateByPrimaryKeySelective(parmBtiGroup);
                    }*/
                    if (n != 0){
                        List<CardAuthorizationInfo> cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByCustomerId(customerBtiInfo.getCustomerId());
                        for (CardAuthorizationInfo cardAuthorizationInfo:cardAuthorizationInfos){
                            CardBasicInfo cardBasicInfo = cardBasicInfoMapper.selectByPrimaryKey(cardAuthorizationInfo.getCardNumber(),OrgNumberUtils.getOrg());
                            BtiBlockCodeUpdateReportDTO btiBlockCodeUpdateReportDTO = generateBtiBlockCodeUpdate(customerBtiInfo, cardAuthorizationInfo,cardBasicInfo);
                            btiBlockCodeUpdateReportDTOS.add(btiBlockCodeUpdateReportDTO);
                        }
                    }
                }else {
                    logger.info("Account age code is not 3, but: {}, no group flag and block flag update", customerBtiInfo.getBtiAgeCode());
                }
            }
            //生成报表
            if (!CollectionUtils.isEmpty(btiBlockCodeUpdateReportDTOS)) {
                List<String> strings = new ArrayList<>(16);
                for (BtiBlockCodeUpdateReportDTO btiBlockCodeUpdateReportDTO : btiBlockCodeUpdateReportDTOS) {
                    strings.add(btiBlockCodeUpdateReportDTO.toString());
                }
                transactionFileWriteHandler.outFile(getReportName(btiBlockCodeUpdateReportOutFilePathConfig, "KCPM7551-BTIBlockCodeUpdateListing.SCV"), strings);
            }
        }
        return null;
    }

    /**
     * generateBtiSimReport
     *
     * @param customerBTInfoDTO       CustomerBTInfoDTO
     * @param cardAuthorizationInfo      CardAuthorizationInfo
     * @return BtiSimReportDTO
     */
    private BtiBlockCodeUpdateReportDTO generateBtiBlockCodeUpdate(CustomerBtiInfo customerBTInfoDTO, CardAuthorizationInfo cardAuthorizationInfo, CardBasicInfo cardBasicInfo) {

        BtiBlockCodeUpdateReportDTO btiBlockCodeUpdateReportDTO = new BtiBlockCodeUpdateReportDTO();
        btiBlockCodeUpdateReportDTO.setCustomerId(customerBTInfoDTO.getIdNo());
        btiBlockCodeUpdateReportDTO.setCardNumber(cardAuthorizationInfo.getCardNumber());
        btiBlockCodeUpdateReportDTO.setRenewalFlag(cardBasicInfo.getRenewalFlag());
        btiBlockCodeUpdateReportDTO.setBtiAgeCode(customerBTInfoDTO.getBtiAgeCode().toString());
        btiBlockCodeUpdateReportDTO.setBtiIndex(String.valueOf(customerBTInfoDTO.getBtiIndex()));
        btiBlockCodeUpdateReportDTO.setBtiBlockCode(customerBTInfoDTO.getBtiBlockIndicator());
        btiBlockCodeUpdateReportDTO.setCardExpiryDate(cardAuthorizationInfo.getExpireDate());
        btiBlockCodeUpdateReportDTO.setProductCode(cardAuthorizationInfo.getProductNumber());
        btiBlockCodeUpdateReportDTO.setRelationshipIndicator(cardAuthorizationInfo.getRelationshipIndicator());
        return btiBlockCodeUpdateReportDTO;
    }

    /**
     * 获取写出报表路径+文件名
     *
     * @param btiInFilePathConfig AnytxnFilePathConfig
     * @param fileName            String
     * @return String
     */
    private String getReportName(AnytxnFilePathConfig btiInFilePathConfig, String fileName) {
        String filePath = btiInFilePathConfig.getCommonPath();
        String pathForSystem = transactionFileWriteHandler.getPathForSystem();
        String[] pathWords = filePath.split(pathForSystem);
        pathWords[pathWords.length-1] = OrgNumberUtils.getOrg();
        filePath =String.join(pathForSystem,pathWords);
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        LocalDate today = organizationInfo.getToday();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String format = dateTimeFormatter.format(today);
        return filePath + pathForSystem + format + pathForSystem + fileName;
    }
}
