package com.anytech.anytxn.installment.controller.order;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallTrialResDTO;
import com.anytech.anytxn.installment.base.domain.dto.*;
import com.anytech.anytxn.installment.base.service.IInstallBillAppEntryService;
import com.anytech.anytxn.installment.base.service.IInstallOrderAppService;
import com.anytech.anytxn.installment.base.service.IInstallSingleAppEntryService;
import com.anytech.anytxn.installment.base.utils.InstallmentThreadLocalHolder;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.List;
@RestController
public class InstallTransAppManageController extends BizBaseController {

    private static final Logger logger = LoggerFactory.getLogger(InstallTransAppManageController.class);

    @Autowired
    private IInstallBillAppEntryService installBillAppEntryService;

    @Autowired
    private IInstallSingleAppEntryService installSingleAppEntryService;

    @Autowired
    private IInstallOrderAppService installOrderAppService;

    @ApiOperation(value = "APP-根据账号查询单笔分期", notes = "APP-根据账号查询单笔分期 R:单笔分期 S:账单分期")
    @GetMapping(value = "/install/searchSingleInstallApp/cardNumber/{cardNumber}/installFlag/{installFlag}")
    public AnyTxnHttpResponse<List<SingleInstallAppDTO>> getSingleInstallApp(@PathVariable String cardNumber, @PathVariable String installFlag) {
        logger.info("Starting get single install app: cardNumber={}, installFlag={}", cardNumber, installFlag);
        List<SingleInstallAppDTO> singleInstallDtos = installSingleAppEntryService.singleInstallmentApp(cardNumber, installFlag);
        logger.info("Get single install app completed successfully: cardNumber={}, resultCount={}", cardNumber, singleInstallDtos != null ? singleInstallDtos.size() : 0);
        return AnyTxnHttpResponse.success(singleInstallDtos);
    }


    @ApiOperation(value = "APP-单笔分期列表详细查询", notes = "APP-单笔分期列表详细查询")
    @PostMapping(value = "/install/searchSingleInstallListApp")
    public AnyTxnHttpResponse<List<InstallTradingDTO>> getSingleInstallList(@RequestBody(required = false) InstallTradingSearchKeyDTO installTradingSearchKeyDTO) {
        logger.info("Starting get single install list app");
        List<InstallTradingDTO> installSingleList = installSingleAppEntryService.findInstallSingleListApp(installTradingSearchKeyDTO);
        logger.info("Get single install list app completed successfully: resultCount={}", installSingleList != null ? installSingleList.size() : 0);
        return AnyTxnHttpResponse.success(installSingleList);
    }

    @ApiOperation(value = "APP-单笔分期交易录入", notes = "APP-单笔分期交易录入")
    @PostMapping(value = "/install/singleEntryApp")
    public AnyTxnHttpResponse<InstallEntryAppResDTO> singleInstallTransEntry(@Valid @RequestBody InstallEntryAppDTO installEntryAppDTO) {
        logger.info("Starting single install trans entry app: cardNumber={}", installEntryAppDTO != null ? installEntryAppDTO.getCardNumber() : null);
        InstallEntryAppResDTO installEntryResDTO = installSingleAppEntryService.singleInstallmentApp(installEntryAppDTO);
        logger.info("Single install trans entry app completed successfully: cardNumber={}", installEntryAppDTO != null ? installEntryAppDTO.getCardNumber() : null);
        return AnyTxnHttpResponse.success(installEntryResDTO);
    }
    @ApiOperation(value = "APP-根据账号查询账单分期", notes = "APP-根据账号查询账单分期")
    @GetMapping(value = "/install/searchBillInstallApp/cardNumber/{cardNumber}")
    public AnyTxnHttpResponse<InstallTradingAppDTO> getBillInstall(@PathVariable String cardNumber) {
        logger.info("Starting get bill install app: cardNumber={}", cardNumber);
        InstallTradingAppDTO installTradingDtos = installBillAppEntryService.findBillInstallApp(cardNumber);
        logger.info("Get bill install app completed successfully: cardNumber={}", cardNumber);
        return AnyTxnHttpResponse.success(installTradingDtos);
    }

    @ApiOperation(value = "APP-账单分期交易录入", notes = "APP-账单分期交易录入")
    @PostMapping(value = "/install/billEntryApp")
    public AnyTxnHttpResponse<InstallEntryAppResDTO> billInstallTransEntry(@Valid @RequestBody InstallEntryAppDTO installEntryAppDTO) {
        logger.info("Starting bill install trans entry app: cardNumber={}", installEntryAppDTO != null ? installEntryAppDTO.getCardNumber() : null);
        InstallEntryAppResDTO billEntry = installBillAppEntryService.billInInstallmentApp(installEntryAppDTO);
        logger.info("Bill install trans entry app completed successfully: cardNumber={}", installEntryAppDTO != null ? installEntryAppDTO.getCardNumber() : null);
        return AnyTxnHttpResponse.success(billEntry);
    }

    @ApiOperation(value = "APP-分期试算", notes = "分期试算")
    @PostMapping(value = "/install/trialApp")
    public AnyTxnHttpResponse<InstallTrialResDTO> trialInstallFee(@Valid @RequestBody InstallEntryDTO installEntryDTO) {
        try {
            logger.info("Starting trial install fee app: cardNumber={}", installEntryDTO != null ? installEntryDTO.getCardNumber() : null);
            InstallTrialResDTO installTrialResDTO = installOrderAppService.trialInstallFeeApp(installEntryDTO);
            logger.info("Trial install fee app completed successfully: cardNumber={}", installEntryDTO != null ? installEntryDTO.getCardNumber() : null);
            return AnyTxnHttpResponse.success(installTrialResDTO);
        } finally {
            InstallmentThreadLocalHolder.remove();
        }
    }
    /*@ApiOperation(value = "APP-分期交易录入走审核流程", notes = "APP-分期产品录入")
    @PostMapping(value = "/install/entryApp")
    public AnyTxnHttpResponse<InstallEntryAppResDTO> entryApp(@Valid @RequestBody InstallEntryAppDTO installEntryAppDTO) {
        InstallEntryAppResDTO installEntryAppResDTO = installOrderService.entryApp(installEntryAppDTO);
        return AnyTxnHttpResponse.success(installEntryAppResDTO, InstallRepDetailEnum.BI_IN_T.message());
    }*/

    @ApiOperation(value = "APP-分期交易明细查询", notes = "已提交审核的分期交易")
    @GetMapping(value = "/install/installmentRecordDetail/cardNumber/{cardNumber}/processStage/{processStage}")
    public AnyTxnHttpResponse<List<InstallAppResDTO>> installmentRecordDetail(@PathVariable String cardNumber, @PathVariable String processStage) {
        logger.info("Starting installment record detail: cardNumber={}, processStage={}", cardNumber, processStage);
        List<InstallAppResDTO> installEntryAppResDTOS = installOrderAppService.installmentRecordDetail(cardNumber,processStage);
        logger.info("Installment record detail completed successfully: cardNumber={}, resultCount={}", cardNumber, installEntryAppResDTOS != null ? installEntryAppResDTOS.size() : 0);
        return AnyTxnHttpResponse.success(installEntryAppResDTOS);
    }
    @ApiOperation(value = "APP-根据订单id 查询订单表")
    @GetMapping(value = "/install/installOrderApp/orderId/{orderId}")
    public AnyTxnHttpResponse<InstallOrderAppResDTO> getOrderById(@PathVariable(value = "orderId") String orderId) {
        logger.info("Starting get order app by id: orderId={}", orderId);
        InstallOrderAppResDTO installOrderAppDTO = installOrderAppService.findOrderAppById(orderId);
        logger.info("Get order app by id completed successfully: orderId={}, found={}", orderId, installOrderAppDTO != null);
        return AnyTxnHttpResponse.success(installOrderAppDTO);
    }
}

