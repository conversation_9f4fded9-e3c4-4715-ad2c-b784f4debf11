package com.anytech.anytxn.account.job.exchange.step;

import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.business.base.transaction.domain.dto.ExPurchaseInfoDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.database.JdbcPagingItemReader;
import org.springframework.batch.item.database.Order;
import org.springframework.batch.item.database.PagingQueryProvider;
import org.springframework.batch.item.database.support.SqlPagingQueryProviderFactoryBean;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-11-09 14:55
 **/
public class ExchangePaymentReader extends JdbcPagingItemReader<ExPurchaseInfoDTO> {
    private static final Logger logger = LoggerFactory.getLogger(ExchangePaymentReader.class);

    public ExchangePaymentReader(String partitionKey ,DataSource dataSource) {
        super();
        logger.info("Query foreign currency management accounts that need repayment and are auto exchange purchase");
        this.setRowMapper(new BeanPropertyRowMapper<>(ExPurchaseInfoDTO.class));
        this.setQueryProvider(oraclePagingQueryProvider(partitionKey, dataSource));
    }

    private PagingQueryProvider oraclePagingQueryProvider(String partitionKey,DataSource dataSource) {
        SqlPagingQueryProviderFactoryBean provider = new SqlPagingQueryProviderFactoryBean();
        provider.setSelectClause("id, CUSTOMER_ID, AUTO_FOREIGN_EXCHANGE_PURCHASE_TYPE," +
                "AUTO_FOREIGN_EXCHANGE_PURCHASE_PAYMENT_TYPE,CONTRACT_TYPE, CONTRACT_DATE, RELEASE_DATE, SOURCE, ORGANIZATION_NUMBER");
        provider.setFromClause(" AUTO_FOREIGN_EXCHANGE_PURCHASE_CONTRACT_INFO ");


//        String sql = " ACCOUNT_MANAGEMENT_ID >= :fromId AND ACCOUNT_MANAGEMENT_ID <= :endId";

//        if(null != partitionKey){
//            sql += " and (PARTITION_KEY >= :partitionKey0 and PARTITION_KEY <= :partitionKey1)  ";
//        }
        //设置查询条件
        String orgConditionStr = " ORGANIZATION_NUMBER = "+ OrgNumberUtils.getOrg();
        provider.setWhereClause(orgConditionStr + " AND CONTRACT_TYPE = '0'");

        Map<String, Order> sortKey = new HashMap<>(10);
        sortKey.put("id", Order.ASCENDING);
        provider.setSortKeys(sortKey);
        provider.setDataSource(dataSource);
        try {
            return provider.getObject();
        } catch (Exception e) {
            logger.error("PagingQueryProvider exception", e);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.UNKONWN_ERR);
        }
    }
}
