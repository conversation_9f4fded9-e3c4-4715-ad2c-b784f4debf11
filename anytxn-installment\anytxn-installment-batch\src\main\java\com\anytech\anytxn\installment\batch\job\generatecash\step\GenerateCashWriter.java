package com.anytech.anytxn.installment.batch.job.generatecash.step;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.dao.installment.model.InstallRecord;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallRecordDTO;
import com.anytech.anytxn.installment.batch.job.resolvecash.service.InstallCashLoanService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2020-02-09 15:30
 **/
public class GenerateCashWriter implements ItemWriter<InstallRecord> {

    private static final Logger logger = LoggerFactory.getLogger(GenerateCashWriter.class);

    @Autowired
    private InstallCashLoanService installCashLoanService;

    @Override
    public void write(Chunk<? extends InstallRecord> list) throws Exception {
        List list1 = (List) list;
        int size = list1 != null ? list1.size() : 0;
        logger.info("Starting generate cash install loan: itemCount={}", size);
        installCashLoanService.generateCashInstalLoan(BeanMapping.copyList(list1, InstallRecordDTO.class));
        logger.info("Generate cash install loan completed successfully: itemCount={}", size);
    }
}
