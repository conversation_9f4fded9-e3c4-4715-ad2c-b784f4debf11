package com.anytech.anytxn.account.service;

import com.anytech.anytxn.account.base.domain.bo.GiroAutoPayBO;
import com.anytech.anytxn.account.base.utils.ListUtils;
import com.anytech.anytxn.business.base.common.domain.dto.*;
import com.anytech.anytxn.business.dao.account.mapper.*;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;

import com.anytech.anytxn.business.dao.account.model.AutoPaymentInfoHistory;
import com.anytech.anytxn.business.base.common.constants.MaintenanceConstant;

import com.anytech.anytxn.business.base.common.service.IMaintenanceLogBisService;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardProductInfoSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardProductInfo;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;

import com.anytech.anytxn.transaction.base.domain.dto.dbs.DbsInterBankGiroFileDTO;
import com.anytech.anytxn.transaction.base.enums.AnyTxnTransactionRespCodeEnum;
import com.anytech.anytxn.transaction.base.exception.AnyTxnTransactionException;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import com.anytech.anytxn.account.base.domain.bo.AutoPaymentBO;
import com.anytech.anytxn.account.base.constants.Constants;
import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.account.base.service.IAutoPaymentLogService;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.account.enums.BankFlagEnum;
import com.anytech.anytxn.business.base.account.domain.dto.AutoPaymentLogDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AutoPaymentLogSearchKeyDTO;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AutoPaymentLog;
import com.anytech.anytxn.business.base.card.domain.dto.AutoEbitSignUpInforDTO;
import com.anytech.anytxn.business.dao.card.mapper.AutoEbitSignUpInforMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.anytech.anytxn.parameter.base.common.domain.dto.CurrencyRateResDTO;
import com.anytech.anytxn.parameter.base.common.service.ICurrencyRateService;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 约定扣款
 *
 * @Author: Wenwu Huang
 * @Date: 2019/1/21 16:04
 */
@Service
public class AutoPaymentLogServiceImpl implements IAutoPaymentLogService {
    private static final Logger logger = LoggerFactory.getLogger(AutoPaymentLogServiceImpl.class);

    @Resource
    private AutoPaymentLogMapper autoPaymentLogMapper;
    @Resource
    private AutoPaymentLogSelfMapper autoPaymentLogSelfMapper;
    @Autowired
    private SqlSessionFactory sqlSessionFactory;
    @Resource
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Resource
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;

    @Resource
    private AccountManagementInfoMapper accountManagementInfoMapper;

    @Resource
    private ParmOrganizationInfoMapper parmOrganizationInfoMapper;

    @Resource
    private ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;

    @Resource
    private ICurrencyRateService currencyRateService;

    @Resource
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;

    @Autowired
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;
    @Autowired
    private AutoEbitSignUpInforMapper autoEbitSignUpInforMapper;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private AutoPaymentInfoHistorySelfMapper autoPaymentInfoHistorySelfMapper;
    @Resource
    private IMaintenanceLogBisService maintenanceLogService;
    @Autowired
    private SequenceIdGen sequenceIdGen;



    @Value("${anytxn.batch.stmt.max-insert:500}")
    private int maxInsert;
    @Value("${anytxn.batch.stmt.max-update:500}")
    private int maxUpdate;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void batchSave(List<AutoPaymentBO> list) {
        List<AutoPaymentLog> autoPaymentLogs = new ArrayList<>();
        list.forEach(x->{
            if (!CollectionUtils.isEmpty(x.getAutoPayments())){
                autoPaymentLogs.addAll(x.getAutoPayments());
            } });
        if (!CollectionUtils.isEmpty(autoPaymentLogs)){
            //批量插入，防止单个sql过大设置阈值控制
            if (autoPaymentLogs.size() > maxInsert){
                List<List<AutoPaymentLog>> insertLists = ListUtils.fixedGrouping(autoPaymentLogs, maxInsert);
                for (List<AutoPaymentLog> autoPaymentLogList : insertLists){
                    autoPaymentLogSelfMapper.insertBatch(autoPaymentLogList);
                }
            }else {
                autoPaymentLogSelfMapper.insertBatch(autoPaymentLogs);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void newAutoPayBatchSave(List<GiroAutoPayBO> giroAutoPayBOs) {
        List<AutoPaymentLog> autoPaymentLogs = new ArrayList<>();
        List<AccountManagementInfo> accountManagementInfos = new ArrayList<>();
        logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", OrgNumberUtils.getOrg());
        OrganizationInfoResDTO orgInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        logger.info("organizationInfoService.findOrganizationInfo completed: today={}", orgInfo.getToday());
        if (!CollectionUtils.isEmpty(giroAutoPayBOs)){
            giroAutoPayBOs.forEach(x ->{
                if (x.getAutoPaymentLog() != null){
                    autoPaymentLogs.add(x.getAutoPaymentLog());
                }
                accountManagementInfos.add(x.getAccountManagementInfo());
            });
            if (!CollectionUtils.isEmpty(accountManagementInfos)){
                for (AccountManagementInfo accountManagementInfo:accountManagementInfos){
                    if (accountManagementInfo != null){
                        AccountManagementInfo newAccountManagementInfo = BeanMapping.copy(accountManagementInfo, AccountManagementInfo.class);
                        if("1".equals(accountManagementInfo.getAutoPaymentType())){
                            newAccountManagementInfo.setAutoPaymentType("3");
                        }else if("2".equals(accountManagementInfo.getAutoPaymentType())){
                            newAccountManagementInfo.setAutoPaymentType("4");
                        }
                        newAccountManagementInfo.setAutoExtractedDate(orgInfo.getToday());
                        MaintenanceLogDTO maintenanceLog = new MaintenanceLogDTO();
                        maintenanceLog.setOperationTimestamp(LocalDateTime.now());
                        maintenanceLog.setPrimaryKeyValue(accountManagementInfo.getAccountManagementId());
                        maintenanceLog.setOperationType(MaintenanceConstant.OPERATION_U);
                        maintenanceLog.setTransactionDataType(MaintenanceConstant.DATA_A);
                        maintenanceLog.setOriginalValue(accountManagementInfo.getAutoPaymentType());
                        maintenanceLog.setUpdatedValue(newAccountManagementInfo.getAutoPaymentType());
                        maintenanceLog.setOperatorId(LoginUserUtils.getLoginUserName());
                        maintenanceLogService.add(maintenanceLog, newAccountManagementInfo, accountManagementInfo, MaintenanceConstant.ACCOUNT_MANAGEMENT_INFO);
                        accountManagementInfoSelfMapper.updateByPrimaryKeySelective(newAccountManagementInfo);
                    }
                }
            }

            if (!CollectionUtils.isEmpty(autoPaymentLogs)){
                if (autoPaymentLogs.get(0) != null){
                    //写autoPaymentLog
                    if (autoPaymentLogs.size() > maxInsert){
                        List<List<AutoPaymentLog>> insertLists = ListUtils.fixedGrouping(autoPaymentLogs, maxInsert);
                        for (List<AutoPaymentLog> autoPaymentLogList : insertLists){
                            autoPaymentLogSelfMapper.insertBatch(autoPaymentLogList);
                        }
                        logger.info("Extracted and admitted management account count: {}, batch insert", accountManagementInfos.size());
                    }else {
                        autoPaymentLogSelfMapper.insertBatch(autoPaymentLogs);
                        logger.info("Extracted and admitted management account count: {}", accountManagementInfos.size());
                    }

                    //写autoPaymentLogHistory
                    logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", OrgNumberUtils.getOrg());
                    OrganizationInfoResDTO paramOrganizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
                    logger.info("organizationInfoService.findOrganizationInfo completed: accruedThruDay={}", paramOrganizationInfo.getAccruedThruDay());
                    LocalDate accruedThruDay = paramOrganizationInfo.getAccruedThruDay();
                    List<AutoPaymentInfoHistory> autoPaymentInfoHisList = new ArrayList<>();
                    autoPaymentLogs.forEach(x ->{
                        AutoPaymentInfoHistory autoPaymentInfoHistory = new AutoPaymentInfoHistory();
                        autoPaymentInfoHistory.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                        autoPaymentInfoHistory.setOrganizationNumber(x.getOrganizationNumber());
                        autoPaymentInfoHistory.setAccountManagementId(x.getAccountManagementId());
                        autoPaymentInfoHistory.setAutoPaymentCreateDate(accruedThruDay);
                        autoPaymentInfoHistory.setAutoPaymentBillingDate(null);
                        autoPaymentInfoHistory.setAutoPaymentCurrency(x.getOriginalCurrency());
                        autoPaymentInfoHistory.setAutoPaymentRepayAmount(x.getFinalPaymentAmount());
                        autoPaymentInfoHistory.setAutoPaymentRealAmount(BigDecimal.ZERO);
                        autoPaymentInfoHistory.setAutoPaymentResultCode("1");
                        if(org.apache.commons.lang3.StringUtils.isNotEmpty(x.getAutoPaymentDebitAcctNumber())){
                            autoPaymentInfoHistory.setAutoPaymentDebitBraNumber(x.getAutoPaymentBranchNumber());
                            autoPaymentInfoHistory.setAutoPaymentDebitBankNumber(x.getAutoPaymentDebitBankNumber());
                        }else {
                            autoPaymentInfoHistory.setAutoPaymentDebitBraNumber(x.getAutoPaymentOtherBranch());
                            autoPaymentInfoHistory.setAutoPaymentDebitBankNumber(x.getAutoPaymentOtherBankNumber());
                        }
                        autoPaymentInfoHistory.setCreateTime(LocalDateTime.now());
                        autoPaymentInfoHistory.setUpdateTime(LocalDateTime.now());
                        autoPaymentInfoHistory.setUpdateBy(LoginUserUtils.getLoginUserName());
                        autoPaymentInfoHistory.setVersionNumber(1L);
                        autoPaymentInfoHisList.add(autoPaymentInfoHistory);
                    });
                    //批量插入，防止单个sql过大设置阈值控制
                    if (autoPaymentLogs.size() > maxInsert){
                        List<List<AutoPaymentInfoHistory>> insertLists = ListUtils.fixedGrouping(autoPaymentInfoHisList, maxInsert);
                        for (List<AutoPaymentInfoHistory> autoPaymentInfoHistories : insertLists){
                            autoPaymentInfoHistorySelfMapper.insertBatch(autoPaymentInfoHistories);
                        }
                    }else {
                        autoPaymentInfoHistorySelfMapper.insertBatch(autoPaymentInfoHisList);
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void insertAutoPaymentLog(String accountManagementId, String cardNumber, BigDecimal originalPaymentAmount,String trnDate) {
        if (StringUtils.isBlank(accountManagementId) || originalPaymentAmount == null) {
            logger.error("Insert auto payment log failed, accountManagementId or originalPaymentAmount is null");
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.MA_DE);
        }

        AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(accountManagementId);
        if (Objects.isNull(accountManagementInfo)){
            logger.error("Insert auto payment log failed, account management info not found, accountManagementId: {}", accountManagementId);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.ME);
        }
        //合约类型：0-签约、1-解约
        AutoEbitSignUpInforDTO autoEbitSignUpInfor = autoEbitSignUpInforMapper.selectByCustomerAndOrgAndContractType(accountManagementInfo.getCustomerId(), accountManagementInfo.getOrganizationNumber());
        if (null == autoEbitSignUpInfor){
            logger.error("Insert auto payment log failed, auto ebit sign up info not found, customerId: {}, organizationNumber: {}", accountManagementInfo.getCustomerId(), accountManagementInfo.getOrganizationNumber());
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR, AccountingRepDetailEnum.CS);
        }else{
            AutoPaymentLog autoPaymentLog = new AutoPaymentLog();
            autoPaymentLog.setAutoPaymentId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            autoPaymentLog.setAccountManagementId(accountManagementId);
            //本行/他行标志：0-本行、1-他行
            if("0".equals(autoEbitSignUpInfor.getBankflag())){
                autoPaymentLog.setAutoPaymentDebitAcctNumber(autoEbitSignUpInfor.getAutoDebitAffiliatedAccount());
                autoPaymentLog.setAutoPaymentBranchNumber(autoEbitSignUpInfor.getAutoDebitRelatedAccountBranchNumber());
                autoPaymentLog.setAutoPaymentDebitBankNumber(autoEbitSignUpInfor.getAutoDebitRelatedAccountBankNumber());
            }else {
                autoPaymentLog.setAutoPaymentOtherAcctNumber(autoEbitSignUpInfor.getAutoDebitAffiliatedAccount());
                autoPaymentLog.setAutoPaymentOtherBranch(autoEbitSignUpInfor.getAutoDebitRelatedAccountBranchNumber());
                autoPaymentLog.setAutoPaymentOtherBankNumber(autoEbitSignUpInfor.getAutoDebitRelatedAccountBankNumber());
            }
            autoPaymentLog.setOriginalPaymentAmount(originalPaymentAmount);
            autoPaymentLog.setOriginalCurrency(accountManagementInfo.getCurrency());

            if (Constants.RMB.equals(accountManagementInfo.getCurrency())) {
                autoPaymentLog.setExchangeRate(BigDecimal.ONE);
            } else {
                logger.info("Calling currencyRateService.findByOrgAndCurrencyAndRateType: organizationNumber={}, currency={}", accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCurrency());
                CurrencyRateResDTO paramCurrencyRate = currencyRateService.findByOrgAndCurrencyAndRateType(
                        accountManagementInfo.getOrganizationNumber(),
                        accountManagementInfo.getCurrency(),
                        Constants.RMB,
                        "0");
                logger.info("currencyRateService.findByOrgAndCurrencyAndRateType completed: rateValue={}", paramCurrencyRate != null ? paramCurrencyRate.getRateValue() : null);
                if (null != paramCurrencyRate) {
                    BigDecimal rateValue = new BigDecimal(paramCurrencyRate.getRateValue());
                    int exponent = paramCurrencyRate.getExponent();
                    BigDecimal rate = rateValue.divide(BigDecimal.valueOf(Math.pow(10, exponent)));
                    autoPaymentLog.setExchangeRate(rate);
                }
            }
            // 扣款类型是9，则购汇标志为1，否则为0
           /* if("9".equals(accountManagementInfo.getAutoPaymentType())||"9".equals(accountManagementInfo.getTempPaymentType())){
                autoPaymentLog.setExchangePurchasingIndicator(Constants.PfExchangeFlag.PF_EXCHANGE_FLAG_Y);
            }else{
                autoPaymentLog.setExchangePurchasingIndicator(Constants.PfExchangeFlag.PF_EXCHANGE_FLAG_N);
            }*/
           //自动扣款购汇标志：0-非自动扣款购汇、1-自动扣款购汇
            autoPaymentLog.setExchangePurchasingIndicator(autoEbitSignUpInfor.getAutoDebitExchangeSign());
            // 如果购汇标志是1并且原始扣款币种（original_currency）not = 156（人民币），最终扣款金额 = 上述计算后的原始扣款金额 * 扣款汇率
            if (Constants.PfExchangeFlag.PF_EXCHANGE_FLAG_Y.equals(
                    autoPaymentLog.getExchangePurchasingIndicator())
                    && !Constants.RMB.equals(accountManagementInfo.getCurrency())) {
                autoPaymentLog.setFinalPaymentAmount(
                        autoPaymentLog.getOriginalPaymentAmount().multiply(autoPaymentLog.getExchangeRate()));
            } else {
                autoPaymentLog.setFinalPaymentAmount(autoPaymentLog.getOriginalPaymentAmount());
            }
            autoPaymentLog.setPartitionKey(String.valueOf(accountManagementInfo.getPartitionKey()));
            //云修改  时间去当前系统时间  by  lxl
            autoPaymentLog.setCreateTime(LocalDateTime.now());
            autoPaymentLog.setUpdateTime(accountManagementInfo.getUpdateTime());
            autoPaymentLog.setUpdateBy(accountManagementInfo.getUpdateBy());
            autoPaymentLog.setVersionNumber(accountManagementInfo.getVersionNumber());
            ParmOrganizationInfo parmOrganizationInfo = parmOrganizationInfoSelfMapper.selectByOrganizationNumber(accountManagementInfo.getOrganizationNumber());

            if (null == trnDate || "".equals(trnDate)){
	            autoPaymentLog.setTrnDate(parmOrganizationInfo.getToday());
            }else {
	            autoPaymentLog.setTrnDate(LocalDate.parse(trnDate));
            }

            CustomerAuthorizationInfo customerAuthorizationInfo = customerAuthorizationInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(), accountManagementInfo.getCustomerId());
            autoPaymentLog.setIdType(customerAuthorizationInfo.getIdType());
            autoPaymentLog.setIdNumber(customerAuthorizationInfo.getIdNumber());
            autoPaymentLog.setAcctType(" ");
            autoPaymentLog.setCustomerName(customerAuthorizationInfo.getChineseName());
            autoPaymentLog.setCustomerId(customerAuthorizationInfo.getCustomerId());
            if (StringUtils.isEmpty(cardNumber)){
                // 输入活跃的主卡
                List<CardAuthorizationInfo> cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByPrimaryCustomerId(accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId());
                cardAuthorizationInfos.stream().filter(x -> "1".equals(x.getStatus())).findFirst().ifPresent(x-> {
                    autoPaymentLog.setCardNbr(x.getCardNumber());
                });
            }else{
                autoPaymentLog.setCardNbr(cardNumber);
            }
            autoPaymentLog.setMoPhone(customerAuthorizationInfo.getMobilePhone());
            autoPaymentLog.setOrganizationNumber(accountManagementInfo.getOrganizationNumber());
            autoPaymentLog.setOutFile("0");
            autoPaymentLogMapper.insert(autoPaymentLog);
        }
    }

    /**
     * 根据搜索条件分页查询约定扣款流水记录
     * @param searchKey 搜索条件
     * @return PageResultDTO<AutoPaymentLogDTO>
     */
    @Override
    public PageResultDTO<AutoPaymentLogDTO> findByCardNum(AutoPaymentLogSearchKeyDTO searchKey) {
        logger.info("Query auto payment log records by search criteria with pagination");
        String orgNum = null;
        String accountMid = null;
        Map<String, String> map = new HashMap<>(2);
        if (null != searchKey.getCardNumber() && !"".equals(searchKey.getCardNumber())){
            CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(searchKey.getCardNumber(),searchKey.getOrganizationNumber());
            if (null == cardAuthorizationInfo){
                return null;
            }
            String customerId = cardAuthorizationInfo.getPrimaryCustomerId();
            List<AccountManagementInfo> accountManagementInfos = accountManagementInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(),customerId);
            if(!CollectionUtils.isEmpty(accountManagementInfos)){
                accountMid = accountManagementInfos.get(0).getAccountManagementId();
                map.put("account1Id", accountMid);
                orgNum = accountManagementInfos.get(0).getOrganizationNumber();
            }

//            map.put("account2Id", cardAuthorizationInfo.getAccount2Id());
        }
        Page<AutoPaymentLog> page = PageHelper.startPage(searchKey.getPage(), searchKey.getRows());
        List<AutoPaymentLog> autoPaymentLogs = autoPaymentLogSelfMapper.selectByOptions(map);
        List<AutoPaymentLogDTO> autoPaymentLogDtos = BeanMapping.copyList(autoPaymentLogs, AutoPaymentLogDTO.class);
        for (AutoPaymentLogDTO autoPaymentLogDTO : autoPaymentLogDtos){
            if (null != autoPaymentLogDTO.getAutoPaymentOtherAcctNumber()){
                autoPaymentLogDTO.setBankFlag(BankFlagEnum.OTHER_BANK.getCode());
            }
            if (null != autoPaymentLogDTO.getAutoPaymentDebitAcctNumber()){
                autoPaymentLogDTO.setBankFlag(BankFlagEnum.OWN_BANK.getCode());
            }
        }
        return new PageResultDTO<>(searchKey.getPage(), searchKey.getRows(), page.getTotal(), page.getPages(), autoPaymentLogDtos);
    }

    /**
     * 根据主键查询约定扣款流水表明细
     * @param autoPaymentId 约定扣款id
     * @return AutoPaymentLogDTO
     */
    @Override
    public AutoPaymentLogDTO selectById(String autoPaymentId) {
        if (null == autoPaymentId){
            logger.error("Query auto payment log detail by primary key failed, primary key cannot be null");
            //throw new AnyTXNBusRuntimeException("500", "根据主键查询约定扣款流水明细 主键不能为空");
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL, AccountingRepDetailEnum.PR_AG);
        }
        AutoPaymentLog autoPaymentLog = autoPaymentLogMapper.selectByPrimaryKey(autoPaymentId);
        if (null == autoPaymentLog){
            logger.error("Query auto payment log detail by primary key failed, autoPaymentId: {}", autoPaymentId);
            //throw new AnyTXNBusRuntimeException("500", "根据主键查询约定扣款流水明细失败");
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.DE_DE);
        }
        return BeanMapping.copy(autoPaymentLog, AutoPaymentLogDTO.class);
    }

    /**
     * 大莱自扣还款文件写出处理
     *
     * @param autoPaymentLog AutoPaymentLog
     * @return DbsInterBankGiroFileDTO
     */
    public DbsInterBankGiroFileDTO newAutoPayFileOutWriter(AutoPaymentLog autoPaymentLog){

        if (!ObjectUtils.isEmpty(autoPaymentLog)) {
            SqlSession session = sqlSessionFactory.openSession(ExecutorType.BATCH);
            //构建自扣还款写出文件
            BigDecimal finalPaymentAmount = autoPaymentLog.getFinalPaymentAmount();
            DbsInterBankGiroFileDTO dbsInterBankGiroFileDto = null;
            if (finalPaymentAmount != null && finalPaymentAmount.compareTo(BigDecimal.ZERO) > 0){
                dbsInterBankGiroFileDto = buildDbsInterBankGiroFileDTO(autoPaymentLog, session);
            }
            // outFile暂时拿掉这块逻辑 不通过这个条件来判断是否已出过自扣还款文件
//            AutoPaymentLogMapper mapper = session.getMapper(AutoPaymentLogMapper.class);
//            autoPaymentLog.setOutFile("1");
//            mapper.updateByPrimaryKeySelective(autoPaymentLog);
//            session.commit();
            return dbsInterBankGiroFileDto;
        }
        return null;
    }

    /**
     * 构建自扣还款输出文件
     *
     * @param autoPaymentLog AutoPaymentLog
     * @return DbsInterBankGiroFileDTO
     */
    private DbsInterBankGiroFileDTO buildDbsInterBankGiroFileDTO(AutoPaymentLog autoPaymentLog,SqlSession session){
        AccountManagementInfoMapper mapper = session.getMapper(AccountManagementInfoMapper.class);
        CardAuthorizationInfoSelfMapper mapper1 = session.getMapper(CardAuthorizationInfoSelfMapper.class);
        ParmCardProductInfoSelfMapper parmCardProductInfoSelfMapper = session.getMapper(ParmCardProductInfoSelfMapper.class);
        AccountManagementInfo accountManagementInfo = mapper.selectByPrimaryKey(autoPaymentLog.getAccountManagementId());
        if (accountManagementInfo == null){
            logger.error("Query account management info by id failed, data not exist, accountManagementId: {}", autoPaymentLog.getAccountManagementId());
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST);
        }
        String customerId = accountManagementInfo.getCustomerId();
        DbsInterBankGiroFileDTO dbsInterBankGiroFileDto = new DbsInterBankGiroFileDTO();
        dbsInterBankGiroFileDto.setPaymentType("30");
        String externalReferenceNumber = accountManagementInfo.getExternalReferenceNumber();

        if (externalReferenceNumber == null || externalReferenceNumber.equals("")){
            // 输入第一张卡
            String accountProductNumber = accountManagementInfo.getProductNumber();
            List<ParmCardProductInfo> parmCardProductInfos = parmCardProductInfoSelfMapper.selectByOrgAndAccountProductNum(accountManagementInfo.getOrganizationNumber(), accountProductNumber);
            if (parmCardProductInfos == null || parmCardProductInfos.isEmpty()){
                logger.error("Query card product parameter failed, data not exist, organizationNumber: {}, accountProductNumber: {}",
                        accountManagementInfo.getOrganizationNumber(), accountProductNumber);
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST);
            }
            ParmCardProductInfo parmCardProductInfo = parmCardProductInfos.get(0);
            String productNumber = parmCardProductInfo.getProductNumber();
            CardAuthorizationInfo cardAuthorizationInfo;
            if ("C".equals(accountManagementInfo.getLiability())){
                cardAuthorizationInfo = mapper1.selectByOrgNumAndCorpCustomerIdAndRelationshipIndicator(
                        accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId(),
                        "P", productNumber);
            }else {
                cardAuthorizationInfo = mapper1.selectByOrgNumAndCustomerIdAndRelationshipIndicator(
                        accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId(),
                        "P", productNumber);
            }

            if (cardAuthorizationInfo == null){
                logger.error("Query card authorization info failed, first card data not exist, organizationNumber: {}, customerId: {}, relationshipIndicator: {}, productNumber: {}",
                        accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId(), "P", productNumber);
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST);
            }
            externalReferenceNumber = cardAuthorizationInfo.getCardNumber();
        }

        dbsInterBankGiroFileDto.setBeneficiaryReference(externalReferenceNumber == null ? " " : externalReferenceNumber);
        dbsInterBankGiroFileDto.setPurposeCode("CCRD");
        String localAutoPayBankName = accountManagementInfo.getLocalAutoPayBankName();
        if (localAutoPayBankName == null || "".equals(localAutoPayBankName)){
            localAutoPayBankName = "XXXXXXXXXXX";
        }else if (localAutoPayBankName.length() < 11){
            StringBuilder fieldBuilder = new StringBuilder(localAutoPayBankName);
            for (int i = fieldBuilder.length(); i < 11; i++) {
                fieldBuilder = fieldBuilder.append("X");
            }
            localAutoPayBankName = fieldBuilder.toString();
        }
        dbsInterBankGiroFileDto.setReceivingBankBIC(localAutoPayBankName);
        dbsInterBankGiroFileDto.setReceivingAccountNumber(StringUtils.isEmpty(accountManagementInfo.getAutoPaymentAcctNumber())?" ":accountManagementInfo.getAutoPaymentAcctNumber());
        dbsInterBankGiroFileDto.setBulkTransferCurrency("SGD");
        String finalPaymentAmount = String.valueOf(autoPaymentLog.getFinalPaymentAmount()).replace(".", "");
        dbsInterBankGiroFileDto.setAmountInCents(Long.valueOf(finalPaymentAmount));

        CardAuthorizationInfo cardAuthorizationInfo;
        if ("C".equals(accountManagementInfo.getLiability()) && !StringUtils.isBlank(accountManagementInfo.getCentralBillingGiroCardNumber())){
            cardAuthorizationInfo = mapper1.selectByCardNumber(accountManagementInfo.getCentralBillingGiroCardNumber());
            logger.info("Corporate card uses central billing card number from account management: {}", accountManagementInfo.getCentralBillingGiroCardNumber());
        }else {
            cardAuthorizationInfo = mapper1.selectByCardNumber(autoPaymentLog.getCardNbr());
            logger.info("Non-corporate liability or central billing card number is empty, use card number from auto log: {}, liability: {}, centralCardNumber: {}", autoPaymentLog.getCardNbr(), accountManagementInfo.getLiability(), accountManagementInfo.getCentralBillingGiroCardNumber());

        }
        if (ObjectUtils.isEmpty(cardAuthorizationInfo)) {
            logger.error("Card not found, cardNumber: {}", autoPaymentLog.getCardNbr());
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATA_NOT_EXIST);
        }
        dbsInterBankGiroFileDto.setPaymentDetail(cardAuthorizationInfo.getCardNumber());
        dbsInterBankGiroFileDto.setDdaReference(cardAuthorizationInfo.getCobrandLoyaltyNumber());
        //自扣账户名称赋值为持卡人名字
        String shortName = cardAuthorizationInfo.getShortName();
        if (shortName == null || "".equals(shortName)){
            dbsInterBankGiroFileDto.setReceivingAccountName(" ");
        }else {
            dbsInterBankGiroFileDto.setReceivingAccountName(shortName);
        }
        dbsInterBankGiroFileDto.setPriorityIndicator("N");
        dbsInterBankGiroFileDto.setUltimateOriginator(" ");
        dbsInterBankGiroFileDto.setUltimateReceiver(" ");
        dbsInterBankGiroFileDto.setFiller(" ");
        dbsInterBankGiroFileDto.setRecordType("10");
        return dbsInterBankGiroFileDto;
    }
}
