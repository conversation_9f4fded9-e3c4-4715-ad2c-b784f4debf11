package com.anytech.anytxn.installment.batch.job.cleaninstallment.step;

import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.installment.base.service.IInstallSettlementLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 数据清理
 * <AUTHOR>
 * @date 2019-05-07
 **/
public class CleanInstallSettlementLogStep implements Tasklet{

    private static final Logger logger = LoggerFactory.getLogger(CleanInstallSettlementLogStep.class);

    @Autowired
    private IInstallSettlementLogService installSettlementLogService;

    private String organizationNumber;

    public CleanInstallSettlementLogStep(String organizationNumber) {
        this.organizationNumber = organizationNumber;
    }

    @Override
    public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext){
        logger.info("Calling installSettlementLogService.deleteAll() for org: {}", OrgNumberUtils.getOrg());
        installSettlementLogService.deleteAll(OrgNumberUtils.getOrg());
        logger.info("installSettlementLogService.deleteAll() completed successfully");
        return RepeatStatus.FINISHED;
    }
}
