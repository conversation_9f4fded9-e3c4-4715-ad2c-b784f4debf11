package com.anytech.anytxn.accounting.controller;

import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlacgdDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlacgmDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlacgyDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlamsPageDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlbalchkResDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvcherDTO;
import com.anytech.anytxn.accounting.base.service.IAccountsCheckService;
import com.anytech.anytxn.accounting.base.service.IAccountsOccurCheckService;
import com.anytech.anytxn.accounting.base.service.IAmsGlamsService;
import com.anytech.anytxn.accounting.base.service.IGlAmsAcgdService;
import com.anytech.anytxn.accounting.base.service.IGlAmsAcgmService;
import com.anytech.anytxn.accounting.base.service.IGlAmsAcgyService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;

import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 会计类API
 *
 * <AUTHOR>
 * @date 2019/12/04
 */
@RestController
@Tag(name = "会计类API")
public class AccountantController extends BizBaseController {
    private static final Logger logger = LoggerFactory.getLogger(AccountantController.class);

    @Autowired
    private IAmsGlamsService amsGlamsService;
    @Autowired
    private IAccountsCheckService iAccountsCheckService;
    @Autowired
    private IAccountsOccurCheckService iAccountsOccurCheckService;
    @Autowired
    private IGlAmsAcgdService iGlAmsAcgdService;
    @Autowired
    private IGlAmsAcgmService iGlAmsAcgmService;
    @Autowired
    private IGlAmsAcgyService iGlAmsAcgyService;

    /**
     * 根据id查询当前会计流水表数据
     */
    @Operation(summary = " 根据id查询当前会计流水表数据")
    @GetMapping("/accountant/tAmsGlamsById")
    @Parameter(name = "tAmsGlamsId", required = true, description = "会计流水表id")
    AnyTxnHttpResponse<TAmsGlamsPageDTO> findTamsGlamsById(@RequestParam("tAmsGlamsId") String tAmsGlamsId) {
        logger.info("Find tams glams by id: tAmsGlamsId={}", tAmsGlamsId);
        TAmsGlamsPageDTO result = amsGlamsService.findTamsGlamsById(tAmsGlamsId);
        logger.info("Find tams glams by id completed: tAmsGlamsId={}, result={}", tAmsGlamsId, result != null ? "found" : "not found");
        return AnyTxnHttpResponse.success(result);
    }

    /**
     * 分页查询会计表流水
     */
    @Operation(summary = "分页查询会计流水表数据")
    @GetMapping("/accountant/getTAmsGlamsByPage")
    AnyTxnHttpResponse<PageResultDTO<TAmsGlamsPageDTO>> getTamsGlamsByPage(
            @Parameter(name = "page", description = "当前页", example = "1")@RequestParam(value = "page", defaultValue = "1") Integer page,
            @Parameter(name = "rows", description = "每页大小", example = "8")@RequestParam(value = "rows", defaultValue = "8") Integer rows,
            @Parameter(name = "postingDate", description = "入账日期")@RequestParam(value = "postingDate", required = false) String postingDate,
            @Parameter(name = "accountManagementId", description = "管理账户信息id")@RequestParam(value = "accountManagementId", required = false) String accountManagementId,
            @Parameter(name = "globalFlowNo", description = "全局业务流水号")@RequestParam(value = "globalFlowNo", required = false) String globalFlowNo) {
        logger.info("Get tams glams by page: page={}, rows={}, postingDate={}, accountManagementId={}, globalFlowNo={}", page, rows, postingDate, accountManagementId, globalFlowNo);
        PageResultDTO<TAmsGlamsPageDTO> result = amsGlamsService.getTamsGlamsByPage(page, rows, postingDate, accountManagementId, globalFlowNo);
        logger.info("Get tams glams by page completed: totalCount={}", result != null ? result.getTotalCount() : 0);
        return AnyTxnHttpResponse.success(result);
    }

    /**
     * 分页查询会计表流水
     */
    @Operation(summary = "分页查询会计传票数据")
    @GetMapping("/accountant/getTamsGlvcherPage")
    AnyTxnHttpResponse<PageResultDTO<TAmsGlvcherDTO>> getTamsGlvcherPage(
            @Parameter(name = "page", description = "当前页", example = "1")@RequestParam(value = "page", defaultValue = "1") Integer page,
            @Parameter(name = "rows", description = "每页大小", example = "8")@RequestParam(value = "rows", defaultValue = "8") Integer rows,
            @Parameter(name = "postingDate", description = "入账日期")@RequestParam(value = "postingDate", required = false) String postingDate,
            @Parameter(name = "accountManagementId", description = "管理账户信息id")@RequestParam(value = "accountManagementId", required = false) String accountManagementId,
            @Parameter(name = "globalFlowNo", description = "全局业务流水号")@RequestParam(value = "globalFlowNo", required = false) String globalFlowNo,
            @Parameter(name = "tableSource", example = "1", description = "数据来源， 1. ACCOUNTANT_GLVCHER 2. " +
                    "ACCOUNTANT_GLVCHER_ABS 3.全部 ")@RequestParam(value = "tableSource", defaultValue = "1") String tableSource,
            @RequestParam(value = "processType", defaultValue = "0") String processType) {
        logger.info("Get tams glvcher page: page={}, rows={}, accountManagementId={}, globalFlowNo={}, postingDate={}, tableSource={}, processType={}", page, rows, accountManagementId, globalFlowNo, postingDate, tableSource, processType);
        PageResultDTO<TAmsGlvcherDTO> result = amsGlamsService.getTamsGlvcherPage(page, rows, accountManagementId,globalFlowNo, postingDate, tableSource,processType);
        logger.info("Get tams glvcher page completed: totalCount={}", result != null ? result.getTotalCount() : 0);
        return AnyTxnHttpResponse.success(result);
    }

    @Operation(summary = " 根据id查询当前会计传票数据")
    @GetMapping("/accountant/findTAmsGlvcherById")
    AnyTxnHttpResponse<TAmsGlvcherDTO> findTamsGlvcherById(
            @Parameter(name = "tAmsGlvcherId", description = "会计传票主键id")@RequestParam("tAmsGlvcherId") String tAmsGlvcherId,
            @Parameter(name = "tableSource", example = "1", description = "数据来源， 1. ACCOUNTANT_GLVCHER 2. " +
                    "ACCOUNTANT_GLVCHER_ABS 3.全部 ")@RequestParam(value = "tableSource", defaultValue = "1") String tableSource) {
        logger.info("Find tams glvcher by id: tAmsGlvcherId={}, tableSource={}", tAmsGlvcherId, tableSource);
        TAmsGlvcherDTO result = amsGlamsService.findtAmsGlvcherById(tAmsGlvcherId, tableSource);
        logger.info("Find tams glvcher by id completed: tAmsGlvcherId={}, tableSource={}, result={}", tAmsGlvcherId, tableSource, result != null ? "found" : "not found");
        return AnyTxnHttpResponse.success(result);
    }

    /**
     * 根据日期查询总分核对表
     */
    @Operation(summary = "根据日期查询总分核对表")
    @GetMapping("/accountant/balchk")
    AnyTxnHttpResponse<PageResultDTO<TAmsGlbalchkResDTO>> findBalChkByDate(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "8") Integer pageSize,
            @RequestParam(value = "date", required = false) String date) {
        logger.info("Find bal chk by date: page={}, pageSize={}, date={}", page, pageSize, date);
        PageResultDTO<TAmsGlbalchkResDTO> result = iAccountsCheckService.getBalChkByPage(page, pageSize, date);
        logger.info("Find bal chk by date completed: totalCount={}", result != null ? result.getTotalCount() : 0);
        return AnyTxnHttpResponse.success(result);
    }

    /**
     * 根据日期查询日总账
     */
    @Operation(summary = " 根据日期查询日总账")
    @GetMapping("/accountant/acgd")
    AnyTxnHttpResponse<PageResultDTO<TAmsGlacgdDTO>> findAcgd(@RequestParam(value = "page", defaultValue = "1") Integer page,
                                                              @RequestParam(value = "pageSize", defaultValue = "8") Integer pageSize,
                                                              @RequestParam(value = "date", required = false) String date) {
        logger.info("Find acgd: page={}, pageSize={}, date={}", page, pageSize, date);
        PageResultDTO<TAmsGlacgdDTO> result = iGlAmsAcgdService.getGlAmsAcgdByPage(page, pageSize, date);
        logger.info("Find acgd completed: totalCount={}", result != null ? result.getTotalCount() : 0);
        return AnyTxnHttpResponse.success(result);
    }

    /**
     * 根据日期查询月总账
     */
    @Operation(summary = " 根据日期查询月总账")
    @GetMapping("/accountant/acgm")
    @Parameter(name = "tAmsGlamsId", required = true, description = "会计流水表id")
    AnyTxnHttpResponse<PageResultDTO<TAmsGlacgmDTO>> findAcgm(@RequestParam(value = "page", defaultValue = "1") Integer page,
                                                              @RequestParam(value = "pageSize", defaultValue = "8") Integer pageSize,
                                                              @RequestParam(value = "date", required = false) String date) {
        logger.info("Find acgm: page={}, pageSize={}, date={}", page, pageSize, date);
        PageResultDTO<TAmsGlacgmDTO> result = iGlAmsAcgmService.getGlAmsAcgmByPage(page, pageSize, date);
        logger.info("Find acgm completed: totalCount={}", result != null ? result.getTotalCount() : 0);
        return AnyTxnHttpResponse.success(result);
    }

    /**
     * 根据日期查询年总账
     */
    @Operation(summary = " 根据日期查询年总账")
    @GetMapping("/accountant/acgy")
    AnyTxnHttpResponse<PageResultDTO<TAmsGlacgyDTO>> findAcgy(@RequestParam(value = "page", defaultValue = "1") Integer page,
                                                              @RequestParam(value = "pageSize", defaultValue = "8") Integer pageSize,
                                                              @RequestParam(value = "date", required = false) String date) {
        logger.info("Find acgy: page={}, pageSize={}, date={}", page, pageSize, date);
        PageResultDTO<TAmsGlacgyDTO> result = iGlAmsAcgyService.getGlAmsAcgyPage(page, pageSize, date);
        logger.info("Find acgy completed: totalCount={}", result != null ? result.getTotalCount() : 0);
        return AnyTxnHttpResponse.success(result);
    }

}