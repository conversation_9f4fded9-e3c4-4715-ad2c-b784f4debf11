<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-parent/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-business-core/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-third-party-service/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-parameter/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-gateway/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-mapping/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-common-manager/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-card/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-account/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-customer/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-monetary-processing/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-limit/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-authorization/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-transaction/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-accounting/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-installment/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-settlement/pom.xml" />
        <option value="$PROJECT_DIR$/anytxn-rule/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_17" default="true" project-jdk-name="17" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>