package com.anytech.anytxn.accounting.batch.config.file.subjectSumFile;

import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcherOriDetailInfo;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.model.SubjectRecordSum;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlacgnSelfMapper;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlacgn;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class RecordSumInDBProcessor implements ItemProcessor<AccountantGlvcherOriDetailInfo, SubjectRecordSum> {
    private static final Logger logger = LoggerFactory.getLogger(RecordSumInDBProcessor.class);
    
    @Autowired
    private TPmsGlacgnSelfMapper tPmsGlacgnSelfMapper;
    @Autowired
    private ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;


    @Override
    public SubjectRecordSum process(AccountantGlvcherOriDetailInfo item) throws Exception {
        // todo 临时添加一个null
        TPmsGlacgn tPmsGlacgn = tPmsGlacgnSelfMapper.selectByOrgAndGlAcct(OrgNumberUtils.getOrg(), item.getGlAcct(),null);
        if (ObjectUtils.isEmpty(tPmsGlacgn)) {
            logger.info("Account code {} not found", item.getGlAcct());
        }
        ParmOrganizationInfo parmOrganizationInfo = parmOrganizationInfoSelfMapper.selectByOrganizationNumber("101");
        LocalDate today = parmOrganizationInfo.getToday();
        String date = today.toString();
        String[] str = date.split("-");
        String strDate = str[0] + str[1] + str[2];
        SubjectRecordSum subjectRecordSum = new SubjectRecordSum();
        subjectRecordSum.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        subjectRecordSum.setSumDate(strDate);
        subjectRecordSum.setSubjectCodeId(tPmsGlacgn.getGlAcct());
        subjectRecordSum.setSubjectDesc(tPmsGlacgn.getGlAcctName());
        subjectRecordSum.setTotalAmount(item.getGlAmount());
        subjectRecordSum.setDebitCreditSide(item.getDrcr());
        subjectRecordSum.setCurrency(item.getCurrCode());
        subjectRecordSum.setAccountProduct(item.getProductType());
        if ("M".equals(item.getModuleFlag())) {
            subjectRecordSum.setModuleFlag("acquiring");
        } else if ("0".equals(item.getModuleFlag())) {
            subjectRecordSum.setModuleFlag("issuing");
        } else {
            subjectRecordSum.setModuleFlag("unknown");
        }
      
        subjectRecordSum.setCrdOrganization(StringUtils.isBlank(item.getCardOrgNumber()) ? "" : item.getCardOrgNumber());


        subjectRecordSum.setCreateTime(LocalDateTime.now());
        subjectRecordSum.setUpdateTime(LocalDateTime.now());
        subjectRecordSum.setUpdateBy("RECORD");
        //未处理
        subjectRecordSum.setProcessFlag("0");

        return subjectRecordSum;
    }
}
