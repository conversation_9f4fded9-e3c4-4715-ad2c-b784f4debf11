# 日志输出规范

## 1. 概述

本规范定义了 Java 应用中日志输出的统一标准，涵盖日志框架选择、日志级别使用、格式规范、内容编写及最佳实践，旨在提升日志的可读性、可维护性和问题定位效率。

## 2. 日志框架选择

### 2.1 核心原则

- 使用 SLF4J 作为日志门面，搭配 Logback 或 Log4j2 作为具体实现。
- 避免直接使用原生日志 API（如 System.out、java.util.logging、Log4j 1.x）。

### 2.2 基本原则

1. **隔离性**：日志输出不能影响系统正常运行；
2. **安全性**：日志打印本身不能存在逻辑异常或漏洞，导致产生安全问题；
3. **数据安全**：不允许输出机密、敏感信息，如用户联系方式、身份证号码、token 等；
4. **可监控分析**：日志可以提供给监控进行监控，分析系统进行分析；
5. **可定位排查**：日志信息输出需有意义，需具有可读性，可供日常开发同学排查线上问题。

### 2.3 示例代码

```java
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ExampleClass {
    private static final Logger logger = LoggerFactory.getLogger(ExampleClass.class);
}
```

## 3. 日志级别规范

### 3.1 级别定义（从低到高）

| 级别  | 用途描述                                   | 使用场景示例                                                 |
| ----- | ------------------------------------------ | ------------------------------------------------------------ |
| TRACE | 开发调试详细信息（仅开发环境）             | `logger.trace("进入方法: {}，参数: {}", methodName, params)` |
| DEBUG | 开发 / 测试阶段关键信息                    | `logger.debug("数据库查询耗时: {}ms", elapsedTime)`          |
| INFO  | 系统正常运行的关键状态（生产环境默认级别） | `logger.info("用户 {} 登录成功", username)`                  |
| WARN  | 潜在问题（不影响系统运行，但需关注）       | `logger.warn("配置文件 {} 未找到，使用默认值", configPath)`  |
| ERROR | 非致命错误（不影响系统继续运行）           | `logger.error("文件写入失败，路径: {}", filePath, e)`        |
| FATAL | 致命错误（导致系统崩溃）                   | `logger.fatal("应用初始化失败，即将退出", e); System.exit(1);` |

### 3.2 注意事项

- 生产环境禁止输出 **TRACE/DEBUG** 日志，避免性能损耗和信息冗余。
- 谨慎使用 **WARN**，仅记录需要人工介入的潜在风险。

## 4. 日志记录的时机

1. **代码初始化时或进入逻辑入口时**：记录系统或服务的启动参数、核心模块初始化状态，使用 INFO 日志打印参数及启动完成状态。
2. **编程语言提示异常**：捕获异常时根据业务影响选择 WARN 或 ERROR 级别，记录质量较高的报错信息。
3. **业务流程预期不符**：如外部参数不正确、数据处理返回码异常等，根据是否容忍该情形选择日志级别。
4. **系统 / 业务核心逻辑的关键动作**：记录核心业务动作，作为系统正常运行的重要指标，使用 INFO 级别。
5. **第三方服务远程调用**：打印请求和响应参数，便于在微服务架构中与第三方定位问题。

## 5. 日志等级设置规范

### 主要使用等级

- **DEBUG**：开发、测试阶段输出调试信息（参数、调试细节、返回值等），生产环境关闭。
- **INFO**：记录系统关键信息（初始化配置、业务状态变化），生产环境默认级别，用于运维和错误回溯。
- **WARN**：输出可预知的警告信息（如方法入参为空），需详尽记录以便分析。
- **ERROR**：记录不可预知的异常（如数据库连接失败、OOM），需输出方法入参、异常对象及堆栈。

### 示例：WARN/ERROR 区分

| 常见 WARN 级别异常                       | 常见 ERROR 级别异常            |
| ---------------------------------------- | ------------------------------ |
| 用户输入参数错误                         | 程序启动失败                   |
| 非核心组件初始化失败                     | 核心组件初始化失败             |
| 后端任务处理最终失败（无重试或重试失败） | 连不上数据库                   |
| 数据插入幂等                             | 核心业务依赖的外部系统持续失败 |
|                                          | OOM、程序技术异常              |

**注意**：避免滥用 ERROR 日志，防止重要问题被噪音掩盖。

## 6. 日志内容基本规范

1. **禁止使用具体日志实现类**
   ❌ 反例：

   ```java
   import org.apache.logging.log4j.LogManager;
   import org.apache.logging.log4j.Logger;
   
   public class UserService {
       private static final Logger logger = LogManager.getLogger(UserService.class);
       public void createUser(User user) {
           logger.info("User created: {}", user.getName()); // 违反规范（直接使用 Log4j2 实现类）
       }
   }
   ```

   ✅ 正例：

   ```java
   import org.slf4j.Logger;
   import org.slf4j.LoggerFactory;
   
   public class UserService {
       private static final Logger logger = LoggerFactory.getLogger(UserService.class);
       public void createUser(User user) {
           logger.info("User created: {}", user.getName()); // 正确（使用 SLF4J 门面）
       }
   }
   ```

2. **禁止打印 JSON 或对象**
   ❌ 反例：

   ```java
   public void processOrder(Order order) {
       logger.info("Order processed: {}", order.toJson()); // 违反规范（直接打印 JSON）
       logger.info("User info: {}", user.toString());      // 违反规范（直接打印对象）
   }
   ```

   ✅ 正例：

   ```java
   public void processOrder(Order order) {
       logger.info("Order processed: orderId={}, amount={}, status={}", 
                   order.getId(), order.getAmount(), order.getStatus()); // 拆分对象属性
   }
   ```

3. **敏感信息脱敏**
   ❌ 反例：

   ```java
   public void userLogin(String username, String password) {
       logger.info("Login attempt: username={}, password={}", username, password); // 违反规范（明文密码）
       logger.info("Card info: {}", creditCard.getNumber());                        // 违反规范（明文卡号）
   }
   ```

   ✅ 正例：

   ```java
   public void userLogin(String username, String password) {
       logger.info("Login attempt: username={}, password=******", username); // 密码脱敏
       logger.info("Card info: last4={}", maskCardNumber(creditCard.getNumber())); // 卡号脱敏
   }
   
   private String maskCardNumber(String cardNumber) {
       return cardNumber.length() > 4 
           ? "************" + cardNumber.substring(cardNumber.length() - 4) 
           : cardNumber;
   }
   ```

4. **日志格式统一**
   ❌ 反例：

   ```java
   public void updateUser(User user) {
       System.out.println("User updated: " + user.getName()); // 违反规范（使用 System.out）
       LOGGER.info("User updated: {}", user.getName());       // 违反规范（未使用统一工具类）
   }
   ```

   ✅ 正例：

   ```java
   public void updateUser(User user) {
       LogUtil.info("User updated: userId={}, name={}", user.getId(), user.getName()); // 使用统一工具类
   }
   ```

5. **避免字符串拼接**
   ❌ 反例：

   ```java
   public void processPayment(Payment payment) {
       logger.info("Payment processed: " + payment.getId() + ", amount: " + payment.getAmount()); // 字符串拼接
   }
   ```

   ✅ 正例：

   ```java
   public void processPayment(Payment payment) {
       logger.info("Payment processed: id={}, amount={}", payment.getId(), payment.getAmount()); // 使用占位符
   }
   ```

6. **使用英文打印**
   ❌ 反例：

   ```java
   logger.info("创建产品: {}", product.getName()); // 违反规范（中文日志）
   ```

   ✅ 正例：

   ```java
   logger.info("Product created: name={}, category={}", product.getName(), product.getCategory()); // 英文日志
   ```

7. **日志清晰明确**
   ❌ 反例：

   ```java
   logger.info("Something happened"); // 违反规范（描述模糊）
   logger.error("Operation failed");   // 违反规范（缺少上下文）
   ```

   ✅ 正例：

   ```java
   logger.info("Database connection established successfully"); // 明确描述
   logger.error("Failed to process order: orderId={}, error={}", orderId, ex.getMessage()); // 包含上下文
   ```

8. **日志一致性**
   ❌ 反例：

   ```java
   // 不同工具类前缀不一致
   public class LogUtil1 { public static void info(String msg) { logger.info("[CUSTOM] " + msg); } }
   public class LogUtil2 { public static void info(String msg) { logger.info(msg); } }
   ```

   ✅ 正例：

   ```java
   // 统一工具类（时间戳、级别等由 log4j2 配置定义）
   public class LogUtil {
       public static void info(String format, Object... args) {
           logger.info(format, args);
       }
   }
   ```

9. **日志配置（轮转机制）**
   ❌ 反例（log4j2.xml）：

   ```xml
   <Appenders>
       <File name="AppLog" fileName="/var/log/app.log">
           <PatternLayout pattern="%d %p %c{1.} [%t] %m%n"/>
       </File>
   </Appenders>
   ```

   ✅ 正例（log4j2.xml）：

   ```xml
   <Appenders>
       <RollingFile name="AppLog" fileName="/var/log/app.log" 
                    filePattern="/var/log/app-%d{yyyy-MM-dd}.log.gz">
           <PatternLayout pattern="%d %p %c{1.} [%t] %m%n"/>
           <Policies>
               <TimeBasedTriggeringPolicy/> <!-- 按时间轮转 -->
               <SizeBasedTriggeringPolicy size="100 MB"/> <!-- 单文件最大 100MB -->
           </Policies>
           <DefaultRolloverStrategy max="30"/> <!-- 最多保留 30 天日志 -->
       </RollingFile>
   </Appenders>
   ```

10. **使用统一日志工具类**
    ❌ 反例：

    ```java
    public class OrderService {
        private static final Logger logger = LoggerFactory.getLogger(OrderService.class);
        public void createOrder(Order order) {
            logger.info("Creating order: {}", order.getId()); // 直接使用 SLF4J
        }
    }
    ```

    ✅ 正例：

    ```java
    public class OrderService {
        public void createOrder(Order order) {
            LogUtil.info("Creating order: orderId={}, customerId={}", order.getId(), order.getCustomerId()); // 使用统一工具类
        }
    }
    ```

11. **异常信息记录**
    ❌ 反例：

    ```java
    try {
        processFile(filePath);
    } catch (IOException e) {
        logger.error("Failed to process file: " + e.getMessage()); // 违反规范（缺少堆栈）
    }
    ```

    ✅ 正例：

    ```java
    try {
        processFile(filePath);
    } catch (IOException e) {
        logger.error("Failed to process file: path={}", filePath, e); // 记录完整异常（含堆栈）
    }
    ```

## 7. 日志格式

### 摘要日志实例

```plaintext
|[2025-06-03 14:30:45.123] [TRACE_ID=78901234567890] [SPAN_ID=1234567890123456] [INFO] [http-nio-8080-exec-1] [com.example.service.UserService.getUserInfo] [/api/v1/users/{id}] [BIZ_ID=ORD20250603001] [UNIT_ID=SH001] [POSITION=data/user/123.json] - 用户信息查询成功，用户ID：123|
```

### 解析

- **调用时间**：2025-06-03 14:30:45.123
- **日志链路 id**：TRACE_ID=78901234567890（全局跟踪 ID），SPAN_ID=1234567890123456（当前链路 ID）
- **日志级别**：INFO
- **线程名**：http-nio-8080-exec-1
- **类名、方法名**：com.example.service.UserService.getUserInfo
- **接口名**：/api/v1/users/{id}
- **全局业务流水号**：BIZ_ID=ORD20250603001
- **单元号**：UNIT_ID=SH001（上海单元）
- **数据位置**：POSITION=data/user/123.json
- **日志消息**：用户信息查询成功，用户 ID：123