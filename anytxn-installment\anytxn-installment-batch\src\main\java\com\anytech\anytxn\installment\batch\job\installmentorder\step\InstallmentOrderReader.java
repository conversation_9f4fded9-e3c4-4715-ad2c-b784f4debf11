package com.anytech.anytxn.installment.batch.job.installmentorder.step;

import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.database.JdbcPagingItemReader;
import org.springframework.batch.item.database.Order;
import org.springframework.batch.item.database.PagingQueryProvider;
import org.springframework.batch.item.database.support.SqlPagingQueryProviderFactoryBean;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * 分期订单批量
 *
 * <AUTHOR>
 * @date 2019-06-06
 **/
public class InstallmentOrderReader extends JdbcPagingItemReader<InstallOrder> {

    private static final Logger logger = LoggerFactory.getLogger(InstallmentOrderReader.class);

    public InstallmentOrderReader(DataSource dataSource, String partitionKey,String organizationNumber) {
        super();
        this.setRowMapper((new BeanPropertyRowMapper<>(InstallOrder.class)));
        this.setQueryProvider(oraclePagingQueryProvider(dataSource, organizationNumber));
    }

    private PagingQueryProvider oraclePagingQueryProvider(DataSource dataSource, String organizationNumber) {
        SqlPagingQueryProviderFactoryBean providerFactoryBean = new SqlPagingQueryProviderFactoryBean();
        String sql = " STATUS in ('1','2','3','4','5','A','B','C') and ORGANIZATION_NUMBER = '" + organizationNumber + "'";

        sql += " and PARTITION_KEY >= :fromId and PARTITION_KEY <= :endId ";

        providerFactoryBean.setSelectClause("ORDER_ID, ORGANIZATION_NUMBER, ACCOUNT_MANAGEMENT_ID, CARD_NUMBER, PRODUCT_CODE, "+
                "TRANSACTION_DATE, ACQUIRE_REFERENCE_NO, AUTHORIZATION_CODE, INSTALLMENT_CCY, "+
                "TYPE, STATUS, FIRST_POST_DATE, INSTALLMENT_AMOUNT, UNPOSTED_AMOUNT, FIRST_TERM_AMOUNT, "+
                "TERM_AMOUNT, TERM, POSTED_TERM, TOTAL_FEE_AMOUNT, UNPOSTED_FEE_AMOUNT, FIRST_TERM_FEE, "+
                "TERM_FEE, FEE_TERM, POSTED_FEE_TERM, PAYMENT_WAY, FEE_FLAG, FEE_RATE, FEE_DERATE_FLAG, "+
                "DERATE_TERM, DERATE_FEE_AMOUNT, DISCOUNT_RATE, TOTAL_RECEIVED_FEE, TOTAL_DERATE_FEE, "+
                "RECEIVED_PENATLY_AMOUNT, TOTAL_RETURNED_AMOUNT, LIMIT_CODE, MERCHANT_ID,MERCHANT_NAME, MCC, RETRIEVAL_REFERENCE_NUMBER, "+
                "UPDATE_BY, STATUS_UPDATE_DATE, TRANSACTION_DESC, CREATE_TIME, UPDATE_TIME, VERSION_NUMBER, "+
                "ORIGIN_TRANSACTION_ID, LAST_MAINTAIN_DATE, TRANSACTION_CHANNEL, CUSTOMER_ID,GLOBAL_FLOW_NUMBER,REMAIN_TERM,EARLY_SETTLE_AMOUNT");
        providerFactoryBean.setFromClause("INSTALL_ORDER");
        providerFactoryBean.setWhereClause(sql);
        Map<String, Order> sortKey = new HashMap<>(10);
        sortKey.put("ORDER_ID", Order.ASCENDING);
        providerFactoryBean.setSortKeys(sortKey);
        providerFactoryBean.setDataSource(dataSource);
        try {
            logger.info("SQL where clause: {}", sql);
            return providerFactoryBean.getObject();
        } catch (Exception e) {
            logger.error("Failed to create PagingQueryProvider: exception={}", e.getMessage(), e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_SELECT_DATABASE_FAULT,e);
        }
    }
}
