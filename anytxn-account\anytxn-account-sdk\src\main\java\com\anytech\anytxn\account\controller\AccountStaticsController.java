package com.anytech.anytxn.account.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.account.base.service.IAccountStatisticsInfoService;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.business.base.account.domain.dto.AccountStatisticsInfoDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;



/**
 * 统计账户API
 *
 * <AUTHOR>
 * @date 2019/1/8
 */
@RestController
@Tag(name = "统计账户操作")
public class AccountStaticsController extends BizBaseController {
    private static final Logger logger = LoggerFactory.getLogger(AccountStaticsController.class);
    
    @Autowired
    private IAccountStatisticsInfoService accountStatisticsInfoService;

    /**
     * 根据管理账户id分页查询统计账户
     */
    @Operation(summary = " 根据管理账户id分页查询统计账户")
    @GetMapping("/account/statistics")
    AnyTxnHttpResponse<PageResultDTO<AccountStatisticsInfoDTO>> findStatisticsInfoByManagementId(
            @Parameter(name = "page", description = "当前页", example = "1")@RequestParam(value = "page", defaultValue = "1") Integer page,
            @Parameter(name = "rows", description = "每页大小", example = "8")@RequestParam(value = "rows", defaultValue = "8") Integer rows,
            @Parameter(name = "accountManagementId", description = "管理账户id")@RequestParam("accountManagementId") String accountManagementId) {
            logger.info("Find statistics info by management id request received: page={}, rows={}, accountManagementId={}", page, rows, accountManagementId);
            PageResultDTO<AccountStatisticsInfoDTO> pageResult= accountStatisticsInfoService.findListAccStaInfo(page,rows,accountManagementId);
            logger.info("Find statistics info by management id completed: accountManagementId={}, resultSize={}", accountManagementId, pageResult.getTotal());
        return AnyTxnHttpResponse.success(pageResult);
    }

    /**
     * 根据统计账户id查询统计账户明细
     */
    @Operation(summary = " 根据统计账户id查询统计账户明细")
    @GetMapping("/account/statistic/{statisticsId}")
    @Parameter(name = "statisticsId", required = true, description = "统计账户id")
    AnyTxnHttpResponse<AccountStatisticsInfoDTO> findStatisticsInfoDetailById(
            @PathVariable(value = "statisticsId") String statisticsId) {
            logger.info("Find statistics info detail by id request received: statisticsId={}", statisticsId);
            AccountStatisticsInfoDTO accountStatisticsInfoDTO =
                    accountStatisticsInfoService.findStaById(statisticsId);
            logger.info("Find statistics info detail by id completed: statisticsId={}", statisticsId);
        return AnyTxnHttpResponse.success(accountStatisticsInfoDTO);
    }

}
