package com.anytech.anytxn.accounting.controller;

import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.common.core.exception.AnyTxnException;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlacgnService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SystemTableDTO;
import com.anytech.anytxn.parameter.base.common.service.system.ISystemTableService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvcherAdjDTO;
import com.anytech.anytxn.accounting.base.exception.AnyTxnAccountantException;
import com.anytech.anytxn.accounting.base.enums.AccountantRespCodeEnum;
import com.anytech.anytxn.accounting.mapper.AccountantGlvcherAdjSelfMapper;
import com.anytech.anytxn.accounting.base.service.IGlVoucherAdjService;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 调账
 *
 * <AUTHOR>
 * @date 2019/12/04
 */
@RestController
@Tag(name = "会计类API")
@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
public class GlAdjController extends BizBaseController {
    private static final Logger logger = LoggerFactory.getLogger(GlAdjController.class);

    @Autowired
    private AccountantGlvcherAdjSelfMapper glvcherAdjSelfMapper;
    @Autowired
    private IGlVoucherAdjService iGlVoucherAdjService;
    @Autowired
    private ISystemTableService systemTableService;
    @Autowired
    private ITPmsGlacgnService itPmsGlacgnService;

    /**
     * 根据id查询当前调账数据
     */
    @Operation(summary = "根据id查询当前调账数据")
    @GetMapping("/accountant/voucheradj/{no}")
    AnyTxnHttpResponse<TAmsGlvcherAdjDTO> findGlAmsVcherAdj(@PathVariable("no") String no) {
        logger.info("Find gl ams vcher adj: no={}", no);
        try {
            TAmsGlvcherAdjDTO result = iGlVoucherAdjService.getGlAmsVcherAdjByNo(no);
            logger.info("Find gl ams vcher adj completed: no={}, result={}", no, result != null ? "found" : "not found");
            return AnyTxnHttpResponse.success(result);
        } catch (AnyTxnException e) {
            logger.error("Find gl ams vcher adj failed: no={}, error={}", no, e.getMessage(), e);
            return AnyTxnHttpResponse.fail(e.getErrCode(), e.getErrMsg(), e.getErrDetail());
        }
    }

    /**
     * 分页查询调账数据
     */
    @Operation(summary = "分页查询调账数据")
    @GetMapping("/accountant/voucheradj")
    AnyTxnHttpResponse<PageResultDTO<TAmsGlvcherAdjDTO>> getGlAmsVcherAdjByPage(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "rows", defaultValue = "8") Integer rows,
            @RequestParam(value = "postingDate", required = false) String postingDate,
            @RequestParam(value = "organizationNumber", required = false) String organizationNumber,
            @RequestParam(value = "no", required = false) String no) {
        logger.info("Get gl ams vcher adj by page: page={}, rows={}, postingDate={}, organizationNumber={}, no={}", page, rows, postingDate, organizationNumber, no);
        PageResultDTO<TAmsGlvcherAdjDTO> result = iGlVoucherAdjService.getGlAmsVcherAdjByPage(
                page, rows, postingDate, organizationNumber, no);
        logger.info("Get gl ams vcher adj by page completed: totalCount={}", result != null ? result.getTotalCount() : 0);
        return AnyTxnHttpResponse.success(result);
    }

    @Operation(summary = "新增调账数据")
    @PostMapping("/accountant/voucheradj/add")
    public AnyTxnHttpResponse add(@RequestBody TAmsGlvcherAdjDTO data) {
        logger.info("Add voucher adj: no={}", data != null ? data.getNo() : null);
        try {
            SystemTableDTO systemTableResDTO = systemTableService.findBySystemId("0000");
            String batchMode = systemTableResDTO.getBatchMode();
            if (!"0".equals(batchMode)) {
                logger.error("Add voucher adj failed: batch mode is not 0, batchMode={}", batchMode);
                throw new AnyTxnAccountantException(AccountantRespCodeEnum.P_BATCH_MODE_0_FAIL);
            }
            iGlVoucherAdjService.add(data);
            logger.info("Add voucher adj completed: no={}", data != null ? data.getNo() : null);
            return AnyTxnHttpResponse.success();
        } catch (AnyTxnException e) {
            logger.error("Add voucher adj failed: no={}, error={}", data != null ? data.getNo() : null, e.getMessage(), e);
            return AnyTxnHttpResponse.fail(e.getErrCode(), e.getErrMsg());
        }
    }

    @Operation(summary = "调整调账数据")
    @PutMapping("/accountant/voucheradj/update")
    public AnyTxnHttpResponse update(@RequestBody TAmsGlvcherAdjDTO data) {
        logger.info("Update voucher adj: no={}", data != null ? data.getNo() : null);
        try {
            SystemTableDTO systemTableResDTO = systemTableService.findBySystemId("0000");
            String batchMode = systemTableResDTO.getBatchMode();
            if (!"0".equals(batchMode)) {
                logger.error("Update voucher adj failed: batch mode is not 0, batchMode={}", batchMode);
                throw new AnyTxnAccountantException(AccountantRespCodeEnum.P_BATCH_MODE_0_FAIL);
            }
            iGlVoucherAdjService.modify(data);
            logger.info("Update voucher adj completed: no={}", data != null ? data.getNo() : null);
            return AnyTxnHttpResponse.success();
        } catch (AnyTxnException e) {
            logger.error("Update voucher adj failed: no={}, error={}", data != null ? data.getNo() : null, e.getMessage(), e);
            return AnyTxnHttpResponse.fail(e.getErrCode(), e.getErrMsg());
        }
    }


    @Operation(summary = "删除调账数据")
    @DeleteMapping("/accountant/voucheradj/{no}")
    public AnyTxnHttpResponse delete(@PathVariable("no") String no) {
        logger.info("Delete voucher adj: no={}", no);
        try {
            SystemTableDTO systemTableResDTO = systemTableService.findBySystemId("0000");
            String batchMode = systemTableResDTO.getBatchMode();
            if (!"0".equals(batchMode)) {
                logger.error("Delete voucher adj failed: batch mode is not 0, batchMode={}", batchMode);
                throw new AnyTxnAccountantException(AccountantRespCodeEnum.P_BATCH_MODE_0_FAIL);
            }
            iGlVoucherAdjService.delete(no);
            logger.info("Delete voucher adj completed: no={}", no);
            return AnyTxnHttpResponse.success();
        } catch (AnyTxnException e) {
            logger.error("Delete voucher adj failed: no={}, error={}", no, e.getMessage(), e);
            return AnyTxnHttpResponse.fail(e.getErrCode(), e.getErrMsg());
        }
    }


}