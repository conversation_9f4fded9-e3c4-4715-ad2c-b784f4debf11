package com.anytech.anytxn.installment.batch.job.installmentorder.step;

import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.installment.base.service.IInstallRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 分期订单处理
 *
 * <AUTHOR>
 * @date 2019-06-06
 **/
public class InstallmentOrderWriter implements ItemWriter<InstallOrderDTO> {

    private static final Logger logger = LoggerFactory.getLogger(InstallmentOrderWriter.class);

    @Autowired
    private IInstallRecordService installRecordService;

    @Override
    public void write(Chunk<? extends InstallOrderDTO> list) throws Exception {
        int size = list != null ? list.size() : 0;
        logger.info("Starting batch install order writer: itemCount={}", size);
        installRecordService.batchInstallOrderWriter((List<? extends InstallOrderDTO>) list);
        logger.info("Batch install order writer completed successfully: itemCount={}", size);
    }
}
