package com.anytech.anytxn.accounting.batch.job.acbalance.step.accountbalance;

import com.anytech.anytxn.accounting.base.service.IBalService;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlactbalDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountBalanceInfoDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2020/4/3
 *
 * 鐢熸垚鍒嗘埛棰濆害
 */
public class AccountBalanceProcessor implements ItemProcessor<AccountBalanceInfoDTO, AccountantGlactbalDTO> {

    private static final Logger logger = LoggerFactory.getLogger(AccountBalanceProcessor.class);

    @Autowired
    private IBalService balService;

    @Override
    public AccountantGlactbalDTO process(AccountBalanceInfoDTO item) throws Exception {
        logger.info("Account balance processing started: accountId={}", item.getAccountId());
        AccountantGlactbalDTO result = balService.accountBalanceSum(item);
        logger.info("Account balance processing completed: accountId={}", item.getAccountId());
        return result;
    }
}
