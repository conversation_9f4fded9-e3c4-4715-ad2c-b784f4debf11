package com.anytech.anytxn.account.job.newautopay.step;

import com.anytech.anytxn.account.base.domain.bo.GiroAutoPayBO;
import com.anytech.anytxn.account.base.service.IAccountManageInfoService;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 自扣处理
 *
 * <AUTHOR>
 * @date 2021/11/10
 */
public class AutoPaymentProcessor implements ItemProcessor<AccountManagementInfo, GiroAutoPayBO> {

    private static final Logger logger = LoggerFactory.getLogger(AutoPaymentProcessor.class);

    @Autowired
    private IAccountManageInfoService accountManageInfoService;

    @Override
    public GiroAutoPayBO process(AccountManagementInfo accountManagementInfo) {
        logger.info("Management account id: {}", accountManagementInfo.getAccountManagementId());
        GiroAutoPayBO result = accountManageInfoService.batchProcessAutoPay(accountManagementInfo);
        logger.info("Called accountManageInfoService.batchProcessAutoPay, result={}", result != null ? "success" : "null");
        return result;
    }
}
