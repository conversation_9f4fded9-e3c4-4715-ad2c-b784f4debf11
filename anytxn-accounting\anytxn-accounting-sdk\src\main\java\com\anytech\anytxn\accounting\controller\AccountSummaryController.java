package com.anytech.anytxn.accounting.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlamsSumResDTO;
import com.anytech.anytxn.accounting.base.service.IAccountSummaryService;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * 会计汇总
 * @date 2019-12-04 16:15
 **/
@RestController
@Tag(name = "会计汇总")
public class AccountSummaryController extends BizBaseController {
    private static final Logger logger = LoggerFactory.getLogger(AccountSummaryController.class);
    
    @Autowired
    private IAccountSummaryService accountSummaryService;

    @Operation(summary = "根据id查询")
    @GetMapping("/accountant/amssumById")
    @Parameter(name = "id", required = true, description = "技术主键")
    AnyTxnHttpResponse<TAmsGlamsSumResDTO> getTamsGlamsSum(@RequestParam("amssumId") String amssumId) {
        logger.info("Get tams glams sum by id: amssumId={}", amssumId);
        TAmsGlamsSumResDTO result = accountSummaryService.selectByPrimaryKey(amssumId);
        logger.info("Get tams glams sum by id completed: amssumId={}, result={}", amssumId, result != null ? "found" : "not found");
        return AnyTxnHttpResponse.success(result);
    }

    @Operation(summary = "分页查询分交易会计汇总流水表", description = "分页查询分交易会计汇总流水表")
    @GetMapping(value = "/accountant/amssum")
    public AnyTxnHttpResponse<PageResultDTO<TAmsGlamsSumResDTO>> getPage(   @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                                         @RequestParam(value = "rows", defaultValue = "8") Integer rows,
                                                                         @RequestParam(value = "postingDate", required = false) String postingDate,
                                                                         @RequestParam(value = "accountManagementId", required = false) String accountManagementId,
                                                                         @RequestParam(value = "globalFlowNo", required = false) String globalFlowNo,
                                                                         @RequestParam(value = "processInd",  required = false) String processInd) {
        logger.info("Get page: page={}, rows={}, postingDate={}, accountManagementId={}, globalFlowNo={}, processInd={}", page, rows, postingDate, accountManagementId, globalFlowNo, processInd);
        PageResultDTO<TAmsGlamsSumResDTO> result = accountSummaryService.findPage(
                page, rows, postingDate, accountManagementId, globalFlowNo,processInd);
        logger.info("Get page completed: totalCount={}", result != null ? result.getTotalCount() : 0);
        return AnyTxnHttpResponse.success(result);
    }
}
