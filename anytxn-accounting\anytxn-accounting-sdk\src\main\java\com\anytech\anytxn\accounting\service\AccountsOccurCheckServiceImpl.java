package com.anytech.anytxn.accounting.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.accounting.base.service.IAccountsOccurCheckService;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.google.common.collect.Lists;
import com.anytech.anytxn.accounting.base.constants.AccountantConstants;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlbalchkOccurDTO;
import com.anytech.anytxn.accounting.base.utils.AmountUtil;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvcherSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcher;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlacgnDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/4/15
 * 分户账户发生额
 */
@Service
public class AccountsOccurCheckServiceImpl implements IAccountsOccurCheckService {
    private static final Logger logger = LoggerFactory.getLogger(AccountsOccurCheckServiceImpl.class);
    
    @Resource
    private AccountantGlvcherSelfMapper tAmsGlvcherSelfMapper;
    @Resource
    private AccountsCheckServiceImpl accountsCheckService;
    @Resource
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    /**
     *  分户账户发生额计算
     */
    @Override
    public List<TAmsGlbalchkOccurDTO> caculateGlCheckOcur(TPmsGlacgnDTO tPmsGlacgnDTO) {
        logger.info("Calculate gl check occur: glAcct={}, organizationNumber={}", tPmsGlacgnDTO != null ? tPmsGlacgnDTO.getGlAcct() : null, tPmsGlacgnDTO != null ? tPmsGlacgnDTO.getOrganizationNumber() : null);
        
        // 机构分组
        OrganizationInfoResDTO org = organizationInfoService.findOrganizationInfo(tPmsGlacgnDTO.getOrganizationNumber());
        // 分户结果
        List<TAmsGlbalchkOccurDTO> result = Lists.newArrayList();
        // 账户今日会计传票
        List<AccountantGlvcher> tAmsGlvchers = tAmsGlvcherSelfMapper.selectListByAcctCurr(org.getToday(),
                org.getOrganizationNumber(),
                tPmsGlacgnDTO.getGlAcct(),
                tPmsGlacgnDTO.getCurrCode());

        if (CollectionUtils.isNotEmpty(tAmsGlvchers)) {
            // 传票按管理帐号分组
            Map<String, List<AccountantGlvcher>> group = tAmsGlvchers.stream().collect(Collectors.groupingBy(AccountantGlvcher::getAccountManagementId));
            // 分户处理
            group.forEach((accountManagementId, glvcherList) -> {
                TAmsGlbalchkOccurDTO tAmsGlbalchkOccur = new TAmsGlbalchkOccurDTO();
                tAmsGlbalchkOccur.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                tAmsGlbalchkOccur.setOrganizationNumber(org.getOrganizationNumber());
                tAmsGlbalchkOccur.setBranchid("DINERS");
                tAmsGlbalchkOccur.setAccountManagementId(accountManagementId);
                tAmsGlbalchkOccur.setCurrCode(tPmsGlacgnDTO.getCurrCode());
                tAmsGlbalchkOccur.setGlAcct(tPmsGlacgnDTO.getGlAcct());
                tAmsGlbalchkOccur.setGlBalance(BigDecimal.ZERO);
                tAmsGlbalchkOccur.setAcBalance(BigDecimal.ZERO);
                tAmsGlbalchkOccur.setGlGapBal(BigDecimal.ZERO);

                // 统计今日发生额
                BigDecimal occurDb = BigDecimal.ZERO;
                BigDecimal occurCr = BigDecimal.ZERO;
                // 当前管理账号传票余额汇总
                if (CollectionUtils.isNotEmpty(glvcherList)) {
                    for (AccountantGlvcher tAmsGlvcher : glvcherList) {
                        // 累计借方余额
                        if (StringUtils.equals("D", tAmsGlvcher.getDrcr())) {
                            occurDb = AmountUtil.addAmount(occurDb, tAmsGlvcher.getGlAmount());
                            // 累计贷方余额
                        } else if (StringUtils.equals("C", tAmsGlvcher.getDrcr())) {
                            occurCr = AmountUtil.addAmount(occurCr, tAmsGlvcher.getGlAmount());
                        }
                    }
                }

                // 计算总差额（借方： 借方发生总额 - 贷方发生总额； 贷方：贷方发生总额 - 借方发生总额 ）
                BigDecimal glBal=BigDecimal.ZERO;
                if (StringUtils.equals("D",tPmsGlacgnDTO.getGlBalFlag())){
                    glBal=AmountUtil.subtractAmount(occurDb,occurCr);
                }else if (StringUtils.equals("C",tPmsGlacgnDTO.getGlBalFlag())){
                    glBal=AmountUtil.subtractAmount(occurCr,occurDb);
                }
                tAmsGlbalchkOccur.setGlBalance(glBal);
                // 分户差额
                BigDecimal glBalance = accountsCheckService.getGlBalance(tPmsGlacgnDTO, org.getToday(), accountManagementId);
                logger.info("Today's sub-account balance: {}", glBalance);
                BigDecimal glBalance1 = accountsCheckService.getGlBalance(tPmsGlacgnDTO, org.getLastProcessingDay(), accountManagementId);
                logger.info("LastProcessingDay's occurrence amount: {}", glBalance1);
                BigDecimal bigDecimal = AmountUtil.subtractAmount(glBalance, glBalance1);
                logger.info("Sub-account difference: {}", bigDecimal);
                tAmsGlbalchkOccur.setAcBalance(bigDecimal);
                // 分户总差额
                tAmsGlbalchkOccur.setGlGapBal(AmountUtil.subtractAmount(tAmsGlbalchkOccur.getGlBalance(),tAmsGlbalchkOccur.getAcBalance()));
                tAmsGlbalchkOccur.setCreateTime(LocalDateTime.now());
                tAmsGlbalchkOccur.setUpdateTime(LocalDateTime.now());
                tAmsGlbalchkOccur.setUpdateBy(AccountantConstants.DEFAULT_USER);
                tAmsGlbalchkOccur.setVersionNumber(1L);
                tAmsGlbalchkOccur.setProcDate(org.getToday());
                logger.info("Data to be stored in list: accountManagementId={}, glBalance={}, acBalance={}", accountManagementId, tAmsGlbalchkOccur.getGlBalance(), tAmsGlbalchkOccur.getAcBalance());
                result.add(tAmsGlbalchkOccur);
            });
        }
        logger.info("Data to be written to sub-account total table: resultCount={}", result != null ? result.size() : 0);
        return CollectionUtils.isEmpty(result) ? null : result;
    }
}
