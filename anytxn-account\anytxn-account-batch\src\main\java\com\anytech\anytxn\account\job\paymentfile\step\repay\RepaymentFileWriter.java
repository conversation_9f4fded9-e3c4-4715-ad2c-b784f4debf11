package com.anytech.anytxn.account.job.paymentfile.step.repay;

import com.alibaba.fastjson.JSONObject;
import com.anytech.anytxn.account.job.paymentfile.model.RepaymentDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.base.settlement.enums.RejectCodeEnum;
import com.anytech.anytxn.business.common.service.PartitionKeyInitService;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.settlement.mapper.SettleRejectLogSelfMapper;
import com.anytech.anytxn.business.dao.settlement.model.SettleRejectLog;
import com.anytech.anytxn.business.dao.transaction.mapper.SettlementLogSelfMapper;
import com.anytech.anytxn.business.dao.transaction.model.SettlementLog;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.manager.IdGeneratorManager;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2021/1/8
 */
public class RepaymentFileWriter implements ItemWriter<RepaymentDTO> {

    private static final Logger logger = LoggerFactory.getLogger(RepaymentFileWriter.class);

    private String tenantId;

    @Autowired
    private SettlementLogSelfMapper settlementLogSelfMapper;
    @Autowired
    private SettleRejectLogSelfMapper settleRejectLogSelfMapper;
    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Resource
    private PartitionKeyInitService partitionKeyInitService;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    public RepaymentFileWriter(String tenantId) {
        this.tenantId = tenantId;
    }

    public void write(List<? extends RepaymentDTO> items) throws Exception {
        if (!CollectionUtils.isEmpty(items)) {
            List<SettlementLog> settleLogs = Lists.newArrayList();
            List<SettleRejectLog> rejectLogs = Lists.newArrayList();

            items.stream()
                    .filter(Objects::nonNull)
                    .filter(x-> !x.isErrorLine())
                    .forEach(x -> {
                        // 检查数据
                        RejectCodeEnum check = check(x);
                        if (check != null) {
                            rejectLogs.add(reject(x, check));
                        } else {
                            saveSettle(settleLogs, rejectLogs, x);
                        }
            });

            if (!CollectionUtils.isEmpty(settleLogs)) {
                settlementLogSelfMapper.insertSettlementLogBatch(settleLogs);
            }

            if (!CollectionUtils.isEmpty(rejectLogs)) {
                settleRejectLogSelfMapper.batchInsertSettleRejectLog(rejectLogs);
            }
        }
    }

    private RejectCodeEnum check(RepaymentDTO dto){
        if (StringUtils.isEmpty(dto.getBank())) {
            return RejectCodeEnum.ORG;
        } else if (StringUtils.isEmpty(dto.getCardNbr())) {
            return RejectCodeEnum.CARD;
        } else if (StringUtils.isEmpty(dto.getCurr())) {
            return RejectCodeEnum.TRANS_CURR;
        }

        return null;
    }

    private SettleRejectLog reject(RepaymentDTO dto, RejectCodeEnum rejectCode){
        SettleRejectLog rejectLog = new SettleRejectLog();

        rejectLog.setId(IdGeneratorManager.gen19Num().generateId(tenantId));
        // 暂时先专程json存储
        rejectLog.setLineContent(JSONObject.toJSONString(dto));
        rejectLog.setCreateTime(LocalDateTime.now());
        rejectLog.setUpdateTime(LocalDateTime.now());
        rejectLog.setUpdateBy(LoginUserUtils.getLoginUserName());
        rejectLog.setVersionNumber(1L);
        rejectLog.setOrganizationNumber(dto.getOrganizationNumber());
        rejectLog.setFileProcessStatus("2");
        rejectLog.setFileSourceName("repaymentLog");
        rejectLog.setRejectCode(rejectCode.getCoed());
        rejectLog.setRejectDesc(rejectCode.getDes());
        rejectLog.setFileDate(java.sql.Date.from(LocalDate.now().atStartOfDay(ZoneOffset.ofHours(8)).toInstant()));

        return rejectLog;
    }

    private void saveSettle(List<SettlementLog> settleLogs, List<SettleRejectLog> rejectLogs, RepaymentDTO x){
        SettlementLog settlementLog = new SettlementLog();
        settlementLog.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        settlementLog.setTxnCardNumber(x.getCardNbr());
        settlementLog.setTxnTransactionAmount(x.getAmtActual());
        settlementLog.setTxnTransactionCurrency(x.getCurr());
        settlementLog.setTxnBillingAmount(settlementLog.getTxnTransactionAmount());
        settlementLog.setTxnBillingCurrency(x.getCurr());
        settlementLog.setTxnTransactionDate(LocalDate.parse(x.getDatePr(), DateTimeFormatter.ofPattern("yyyyMMdd")).atTime(LocalTime.now()));
        settlementLog.setTxnPostMethod("1");
        settlementLog.setTxnTransactionCode("20010");
        settlementLog.setCreateTime(LocalDateTime.now());
        settlementLog.setUpdateTime(LocalDateTime.now());
        settlementLog.setUpdateBy(LoginUserUtils.getLoginUserName());
        settlementLog.setOrganizationNumber(x.getOrganizationNumber());
        settlementLog.setTxnTransactionSource("L");

        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(x.getCardNbr(), x.getOrganizationNumber());

        if (cardAuthorizationInfo == null) {
            logger.error("Query card number does not exist, card number: {}", x.getCardNbr());
            rejectLogs.add(reject(x, RejectCodeEnum.CARD));
        } else {
            settlementLog.setPartitionKey(partitionKeyInitService.partitionKeyGenerator(BeanMapping
                    .copy(cardAuthorizationInfo, CardAuthorizationDTO.class), null));
            settleLogs.add(settlementLog);
        }
    }

    @Override
    public void write(Chunk<? extends RepaymentDTO> chunk) throws Exception {
        write(chunk.getItems());
    }
}
