package com.anytech.anytxn.installment.batch.job.createorder.step;

import com.anytech.anytxn.installment.base.domain.bo.InstallSettlementLogBO;
import com.anytech.anytxn.installment.base.service.IInstallSettlementLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;


import java.util.List;

/**
 * 分期批量创建订单
 *
 * <AUTHOR>
 * @date 2019-07-17
 **/
public class CreateInstallOrderWriter implements ItemWriter<InstallSettlementLogBO> {

    private static final Logger logger = LoggerFactory.getLogger(CreateInstallOrderWriter.class);

    @Autowired
    private IInstallSettlementLogService installSettlementLogService;


    @Override
    public void write(Chunk<? extends InstallSettlementLogBO> list) throws Exception {
        int size = list != null ? list.size() : 0;
        logger.info("Starting batch create install order writer: itemCount={}", size);
        installSettlementLogService.batchCreateInstallOrderWriter((List<? extends InstallSettlementLogBO>) list);
        logger.info("Batch create install order writer completed successfully: itemCount={}", size);
    }

}
