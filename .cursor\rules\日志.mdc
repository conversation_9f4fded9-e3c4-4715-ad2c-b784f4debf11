为提升Java工程项目的日志管理效率与规范性，现对日志优化工作提示如下：

​​核心代码与接口类梳理​​：
1、全面扫描工程，精准识别核心代码模块及对外提供接口的类。
2、将这些类的关键信息整理成文档，存储至 logs 目录，并生成 writer_log.md 文件，详细记录类名、功能描述、日志生成计划等内容，以此管控日志生成进度。
3、依据writer_log.md规划的进度，为每个类添加日志记录功能。在关键业务逻辑、方法入口与出口、异常处理等位置，合理插入日志语句，确保日志能完整记录系统运行状态与关键信息。
4、日志语言统一​​：统一日志输出语言为英文。若现有日志存在中文内容，需将其准确翻译为英文，保证日志语言一致性，便于后续维护与国际化团队协作。

严格按照下面的约束执行：
1、无论原逻辑是否正确，是否有多余的注释掉的代码，都不能修改任何逻辑，尤其是删除原逻辑代码，只能调整日志的输出。
2、遵循writer_log.md的进度。
3、输出的日志不能包含判断、业务逻辑、查询或者比较复杂的逻辑，只做简单的关键点输出。
4、不得修改类和方法上面的注释，以及 // 或者 /** */ 里面的中文注释，只允许修改日志的输出为英文
5、不能为了加日志，而去修改业务代码，尤其是不能加try..catch...  遇上比较大（类行数超过1000行）的文件先忽略，但记录到文档中即可。