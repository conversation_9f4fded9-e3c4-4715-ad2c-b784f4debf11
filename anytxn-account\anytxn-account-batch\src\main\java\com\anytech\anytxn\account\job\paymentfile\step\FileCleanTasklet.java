package com.anytech.anytxn.account.job.paymentfile.step;

import com.anytech.anytxn.account.utils.FileUtil;
import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Value;

import jakarta.annotation.Resource;

/**
 * 文件清理
 * <AUTHOR>
 * @date 2021/1/8
 */
public class FileCleanTasklet implements Tasklet {

    private static final Logger logger = LoggerFactory.getLogger(FileCleanTasklet.class);

    @Resource
    private ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;

    @Value("${data-id:-1}")
    private String splitDbNum;

	@Resource(name = "autoPaymentFilePathConfig")
	private AnytxnFilePathConfig pathConfigInput;

	@Override
	public RepeatStatus execute(StepContribution stepContribution, ChunkContext chunkContext) throws Exception {
		String delPath = pathConfigInput.getShardingPath(Integer.valueOf(splitDbNum.substring(6, 7)));
		logger.info("Directory to delete files: {}", delPath);
		if (StringUtils.isNotEmpty(delPath)) {
			boolean b = FileUtil.deleteChildDir(delPath);
			logger.info("File deletion completed, file path: {}, result: {}", delPath, b);
		}
		return RepeatStatus.FINISHED;
	}
}
