package com.anytech.anytxn.account.service;

import cn.hutool.json.JSONUtil;
import com.anytech.anytxn.account.base.domain.dto.LoyaltyCustomerInfoResDTO;
import com.anytech.anytxn.account.base.domain.dto.LoyaltyInfoRespDTO;
import com.anytech.anytxn.account.base.domain.dto.TxnUpdateToLoyaltyReqDTO;
import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
//import com.anytech.anytxn.accounting.feign.TxnToLoyaltyFeign;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.account.base.domain.dto.AccountLoyaltyInfoDTO;
import com.anytech.anytxn.business.base.account.domain.model.LoyaltyAccountBasicModel;
import com.anytech.anytxn.business.base.account.domain.model.LoyaltyCardBasicModel;
import com.anytech.anytxn.business.base.account.domain.model.LoyaltyCustomerBasicModel;
import com.anytech.anytxn.business.dao.account.mapper.AccountLoyaltyInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.account.base.service.IAccountLoyaltyInfoService;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.account.model.AccountLoyaltyInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.ParmSysDictDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IParmSysDictService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 账户权益信息service
 *
 * <AUTHOR>
 * @Date 2024/3/22  16:28
 * @Version 1.0
 */
@Service
public class AccountLoyaltyInfoServiceImpl implements IAccountLoyaltyInfoService {
    private static final Logger logger = LoggerFactory.getLogger(AccountLoyaltyInfoServiceImpl.class);
    @Autowired
    private AccountLoyaltyInfoMapper accountLoyaltyInfoMapper;

    @Autowired
    private IOrganizationInfoService organizationInfoService;

//    @Autowired
//    private TxnToLoyaltyFeign loyaltyFeign;
    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;
    @Autowired
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;

    @Autowired
    private IParmSysDictService parmSysDictService;

    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;

    @Autowired
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;



    /**
     * 更新账户权益信息
     *
     * @param accountLoyaltyInfoUpdate
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public AccountLoyaltyInfoDTO updateAccountLoyaltyInfo(AccountLoyaltyInfoDTO accountLoyaltyInfoUpdate) {
        //必须参数校验
        check(accountLoyaltyInfoUpdate);
        String accountManagementId = accountLoyaltyInfoUpdate.getAccountManagementId();
        AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(accountManagementId);
        if(Objects.isNull(accountManagementInfo)){
            logger.error("Update account loyalty info failed, account management info not found, accountManagementId: {}", accountManagementId);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR, AccountingRepDetailEnum.LOYALY_SYNC_ACCT_NOT_E);
        }
        String loyaltyProjectId = accountLoyaltyInfoUpdate.getProjectId();          //权益ID
        String loyaltyGiven = accountLoyaltyInfoUpdate.getGiven();                  //权益账户姓
        String loyaltyFamily = accountLoyaltyInfoUpdate.getFamily();                //权益账户名
        String loyaltyAccountNumber = accountLoyaltyInfoUpdate.getAccountNumber();  //权益账户账号
        String loyaltyAccountType = accountLoyaltyInfoUpdate.getLoyaltyAccountType();      //权益账户类型
        logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", OrgNumberUtils.getOrg());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        logger.info("organizationInfoService.findOrganizationInfo completed: organizationNumber={}", organizationInfo.getOrganizationNumber());

        //更新权益信息
        //校验权益相关信息是否需要改变
        boolean updateTag = false;
        //0:权益改变 1：权益不变（其他信息改变）
        String updateOther = "1";
        Boolean flag = true;
        AccountLoyaltyInfo accountLoyaltyInfoOld = accountLoyaltyInfoMapper.selectByAccountManagementId(accountLoyaltyInfoUpdate.getAccountManagementId(), organizationInfo.getOrganizationNumber());
        if (!Objects.nonNull(accountLoyaltyInfoOld)) {
            logger.error("Update account loyalty info failed, account loyalty info not found, accountManagementId: {}", accountLoyaltyInfoUpdate.getAccountManagementId());
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR, AccountingRepDetailEnum.LOYALY_NOT_EXIST);
        }
        //调用loyalty接口 查询正在生效权益信息
        LoyaltyCustomerInfoResDTO effectLoyaltyInfo =  getEffectLoyaltyInfo(accountManagementId);
        if (Objects.nonNull(effectLoyaltyInfo) && !effectLoyaltyInfo.getProjectId().equals(loyaltyProjectId)) {
            accountLoyaltyInfoOld.setStartDate(getStartDate(accountManagementId,organizationInfo));
            flag = false;
            updateOther ="0";
        }
        accountLoyaltyInfoOld.setProjectId(loyaltyProjectId);
        accountLoyaltyInfoOld.setLoyaltyAccountType(StringUtils.isBlank(loyaltyAccountType)?"":loyaltyAccountType);
        accountLoyaltyInfoOld.setFamily(StringUtils.isBlank(loyaltyFamily)?"":loyaltyFamily);
        accountLoyaltyInfoOld.setGiven(StringUtils.isBlank(loyaltyGiven)?"":loyaltyGiven);
        accountLoyaltyInfoOld.setAccountNumber(StringUtils.isBlank(loyaltyAccountNumber)?"":loyaltyAccountNumber);
        updateTag = true;
        accountLoyaltyInfoOld.setUpdateTime(LocalDateTime.now());
        accountLoyaltyInfoOld.setUpdateBy(LoginUserUtils.getLoginUserName());
        //插入txn数据库
        int temp = accountLoyaltyInfoMapper.updateByAccountManagementId(accountLoyaltyInfoOld);
        if (temp != 1) {
            logger.error("Update account loyalty info to txn database failed, loyaltyProjectId: {}", loyaltyProjectId);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR, AccountingRepDetailEnum.LOYALY_UPDATE_E);
        }
        if (updateTag) {
            //同步给Loyalty(若同步失败会回滚)
            syncToLoyalty(accountLoyaltyInfoUpdate,accountManagementInfo,updateOther);
        }
        AccountLoyaltyInfo loyaltyInfo = accountLoyaltyInfoMapper.selectByAccountManagementId(accountLoyaltyInfoUpdate.getAccountManagementId(), organizationInfo.getOrganizationNumber());
        //构建响应对象返回
        return  buildLoyalty(effectLoyaltyInfo,loyaltyInfo,accountManagementId,flag);
    }

    /**
     * txn同步Loyalty 权益信息修改
     * @param accountLoyaltyInfo
     * @param accountManagementInfo
     * @param updateOther 0:权益改变 1：权益不变（其他信息改变）
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void syncToLoyalty(AccountLoyaltyInfoDTO accountLoyaltyInfo,AccountManagementInfo accountManagementInfo,String updateOther) {
        //匹配积分类型
        TxnUpdateToLoyaltyReqDTO txnUpdateToLoyalty = new TxnUpdateToLoyaltyReqDTO();
        txnUpdateToLoyalty.setAccountManagementId(accountLoyaltyInfo.getAccountManagementId());
        txnUpdateToLoyalty.setProjectId(accountLoyaltyInfo.getProjectId());
        txnUpdateToLoyalty.setFamily(accountLoyaltyInfo.getFamily());
        txnUpdateToLoyalty.setGiven(accountLoyaltyInfo.getGiven());
        txnUpdateToLoyalty.setAccountNumber(accountLoyaltyInfo.getAccountNumber());
        txnUpdateToLoyalty.setType(accountLoyaltyInfo.getLoyaltyType());
        String customerId = accountManagementInfo.getCustomerId();
        LoyaltyCardBasicModel loyaltyCardBasicModel = cardAuthorizationInfoSelfMapper.selectLoyaltyCardAuthInfo(customerId, accountManagementInfo.getProductNumber());
        if(Objects.isNull(loyaltyCardBasicModel)){
            logger.error("Sync to loyalty failed, card authorization info not found, accountManagementId: {}, productNumber: {}", accountLoyaltyInfo.getAccountManagementId(), accountManagementInfo.getProductNumber());
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR, AccountingRepDetailEnum.LOYALY_SYNC_ACCT_NOT_E);
        }
        txnUpdateToLoyalty.setCardNumber(loyaltyCardBasicModel.getCardNumber());
        txnUpdateToLoyalty.setProductNumber(loyaltyCardBasicModel.getProductNumber());
        AnyTxnHttpResponse stringAnyTxnHttpResponse = null;
        if(StringUtils.equalsAny("0",updateOther)){
            //生效方式C
            txnUpdateToLoyalty.setEffectMode("C");
//            stringAnyTxnHttpResponse   = loyaltyFeign.txnUpdateToLoyalty01(txnUpdateToLoyalty,"101");
            stringAnyTxnHttpResponse=null;
        }else {
//            stringAnyTxnHttpResponse = loyaltyFeign.txnUpdateToLoyalty02(txnUpdateToLoyalty,"101");
            stringAnyTxnHttpResponse=null;
        }
        logger.info("Sync response from loyalty: code={}, message={}", 
                stringAnyTxnHttpResponse != null ? stringAnyTxnHttpResponse.getCode() : "null",
                stringAnyTxnHttpResponse != null ? stringAnyTxnHttpResponse.getMessage() : "null");
        String syncResult = stringAnyTxnHttpResponse.getCode();
        if(!StringUtils.equalsAny("**********",syncResult)){
            logger.error("Sync to loyalty failed, accountManagementId: {}", accountLoyaltyInfo.getAccountManagementId());
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_ERR, AccountingRepDetailEnum.LOYALY_SYNC_E);
        }
        logger.info("Sync to loyalty success, accountManagementId: {}, loyaltyProjectId: {}", accountLoyaltyInfo.getAccountManagementId(), accountLoyaltyInfo.getProjectId());
    }

    /**
     * 匹配积分类型
     * @param projectId
     * @return
     */
    private String getLoyaltyType(String projectId) {
//        miles:VM001 cashback:VC001 investment:VI001
        //使用系统字典查询

        List<ParmSysDictDTO> parmSysDictDTOS = parmSysDictService.selectListByTypeId("LOYALTY_PROJECT_ID");
        if(CollectionUtils.isEmpty(parmSysDictDTOS)||CollectionUtils.isEmpty(parmSysDictDTOS)){
            return "";
        }
        for (ParmSysDictDTO parmSysDictDTO : parmSysDictDTOS) {
            if(CollectionUtils.isEmpty(parmSysDictDTO.getChildren())){
                return "";
            }

            for (ParmSysDictDTO child : parmSysDictDTO.getChildren()) {
                if(StringUtils.equalsAny(projectId,child.getCodeId())){
                    return StringUtils.isBlank(child.getCodeName())?"":child.getCodeName();
                }
            }
        }

        return "";
    }

    /**
     * 更新账户权益信息参数校验
     *
     * @param accountLoyaltyInfo
     */
    private void check(AccountLoyaltyInfoDTO accountLoyaltyInfo) {
        //校验管理账户ID是否存在
        if (StringUtils.isBlank(accountLoyaltyInfo.getAccountManagementId())) {
            logger.error("Update account loyalty info failed, accountManagementId is blank");
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_ERR, AccountingRepDetailEnum.LOYALY_UPDATE_PE);
        }
        if (StringUtils.isBlank(accountLoyaltyInfo.getProjectId())) {
            logger.error("Update account loyalty info failed, projectId is blank");
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_ERR, AccountingRepDetailEnum.LOYALY_UPDATE_PE2);
        }
        String type = getLoyaltyType(accountLoyaltyInfo.getProjectId());
        accountLoyaltyInfo.setLoyaltyType(type);
    }

    /**
     * 根据管理账户号查询账户权益信息
     *
     * @param accountManagementId
     * @return
     */
    @Override
    public AccountLoyaltyInfoDTO queryAccountLoyaltyInfo(String accountManagementId) {
        logger.info("Query account loyalty info by account management id: {}", accountManagementId);
        if (StringUtils.isBlank(accountManagementId)) {
            logger.error("Query account loyalty info failed, accountManagementId format is incorrect: {}", accountManagementId);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_ERR, AccountingRepDetailEnum.AM_NE);
        }
        //查询数据库中待生效权益
        AccountLoyaltyInfo dbLoyaltyInfo = accountLoyaltyInfoMapper.selectByAccountManagementId(accountManagementId, OrgNumberUtils.getOrg());
        if (ObjectUtils.isEmpty(dbLoyaltyInfo)) {
            logger.error("Query account loyalty info failed, loyalty info not found for accountManagementId: {}", accountManagementId);
            return null;
        }
        //调用loyalty接口 查询正在生效权益信息
        LoyaltyCustomerInfoResDTO effectLoyaltyInfo =  getEffectLoyaltyInfo(accountManagementId);

        //对比两权益信息是否完全相同
        Boolean flag = compara(effectLoyaltyInfo,dbLoyaltyInfo);
        //构建响应对象返回
        return buildLoyalty(effectLoyaltyInfo,dbLoyaltyInfo,accountManagementId,flag);
    }

    /**
     * 构建请求响应
     * @param effectLoyaltyInfo 生效
     * @param dbLoyaltyInfo 待生效
     * @return
     */
    private AccountLoyaltyInfoDTO buildLoyalty(LoyaltyCustomerInfoResDTO effectLoyaltyInfo, AccountLoyaltyInfo dbLoyaltyInfo,String accountManagementId,boolean flag) {
        AccountLoyaltyInfoDTO accountLoyaltyInfoDTO = new AccountLoyaltyInfoDTO();
        if (StringUtils.equalsAny(dbLoyaltyInfo.getProjectId(), effectLoyaltyInfo.getProjectId())){
            accountLoyaltyInfoDTO.setPreviousFamily(dbLoyaltyInfo.getFamily());
            accountLoyaltyInfoDTO.setPreviousGiven(dbLoyaltyInfo.getGiven());
            accountLoyaltyInfoDTO.setPreviousAccountNumber(dbLoyaltyInfo.getAccountNumber());
        } else {
            accountLoyaltyInfoDTO.setPreviousFamily(effectLoyaltyInfo.getFamily());
            accountLoyaltyInfoDTO.setPreviousGiven(effectLoyaltyInfo.getGiven());
            accountLoyaltyInfoDTO.setPreviousAccountNumber(effectLoyaltyInfo.getAccountNumber());
        }
        accountLoyaltyInfoDTO.setAccountManagementId(accountManagementId);
        accountLoyaltyInfoDTO.setPreviousProjectId(effectLoyaltyInfo.getProjectId());
        accountLoyaltyInfoDTO.setPreciousLoyaltyAccountType(typeConversion(effectLoyaltyInfo.getProjectId()));
        if(!flag){
            //不同，说明有待生效权益
            accountLoyaltyInfoDTO.setProjectId(dbLoyaltyInfo.getProjectId());
            accountLoyaltyInfoDTO.setLoyaltyAccountType(dbLoyaltyInfo.getLoyaltyAccountType());
            accountLoyaltyInfoDTO.setFamily(dbLoyaltyInfo.getFamily());
            accountLoyaltyInfoDTO.setGiven(dbLoyaltyInfo.getGiven());
            accountLoyaltyInfoDTO.setAccountNumber(dbLoyaltyInfo.getAccountNumber());
            accountLoyaltyInfoDTO.setStartDate(dbLoyaltyInfo.getStartDate());
        }
        accountLoyaltyInfoDTO.setCreateTime(dbLoyaltyInfo.getCreateTime());
        accountLoyaltyInfoDTO.setUpdateTime(dbLoyaltyInfo.getUpdateTime());
        accountLoyaltyInfoDTO.setUpdateBy(dbLoyaltyInfo.getUpdateBy());
        accountLoyaltyInfoDTO.setVersionNumber(dbLoyaltyInfo.getVersionNumber());
        return  accountLoyaltyInfoDTO;
    }

    /**
     * 权益账户类型转换
     * @param type
     * @return
     */
    private String typeConversion(String type) {
        switch(type){
            case "VM001":
                return "KrisFlyer";
            case "VI001":
                return "iFast";
            default:
                return "";
        }
    }

    /**
     * 对比
     * @param effectLoyaltyInfo 正在生效权益信息
     * @param dbLoyaltyInfo 数据库待生效权益
     * @return
     */
    private Boolean compara(LoyaltyCustomerInfoResDTO effectLoyaltyInfo, AccountLoyaltyInfo dbLoyaltyInfo) {
        //权益ID
        if(!StringUtils.equalsAny(effectLoyaltyInfo.getProjectId(),dbLoyaltyInfo.getProjectId())){
            return false;
        }
        return  true;
    }

    /**
     * 获取Loyalty正在生效的权益
     * @param accountManagementId
     * @return
     */
    private LoyaltyCustomerInfoResDTO getEffectLoyaltyInfo(String accountManagementId) {

        //1. 查询账户信息
        LoyaltyAccountBasicModel loyaltyAccountBasicModel = accountManagementInfoSelfMapper.selectLoyaltyAccountInfo(
                accountManagementId);
        if (org.springframework.util.ObjectUtils.isEmpty(loyaltyAccountBasicModel)) {
            logger.error("Get effective loyalty info failed, account management info not found, accountManagementId: {}", accountManagementId);
        }

        //2. 查询客户信息
        LoyaltyCustomerBasicModel loyaltyCustomerBasicModel = customerAuthorizationInfoSelfMapper.selectLoyaltyCustomerAuthInfo(
                loyaltyAccountBasicModel.getCustomerId());
        if (org.springframework.util.ObjectUtils.isEmpty(loyaltyCustomerBasicModel)) {
            logger.info("Get effective loyalty info failed, customer info not found, accountManagementId: {}, customerId: {}", accountManagementId, loyaltyAccountBasicModel.getCustomerId());
        }

        //3. 根据客户号，账产品编号查询卡信息
        LoyaltyCardBasicModel loyaltyCardBasicModel = cardAuthorizationInfoSelfMapper.selectLoyaltyCardAuthInfo(
                loyaltyCustomerBasicModel.getCustomerId(), loyaltyAccountBasicModel.getProductNumber());
        if (org.springframework.util.ObjectUtils.isEmpty(loyaltyCardBasicModel)) {
            logger.info("Get effective loyalty info failed, card authorization info not found, accountManagementId: {}, customerId: {}, productNumber: {}", accountManagementId, loyaltyAccountBasicModel.getCustomerId(), loyaltyAccountBasicModel.getProductNumber());

        }

//        AnyTxnHttpResponse<LoyaltyInfoRespDTO<LoyaltyCustomerInfoResDTO>> loyaltyInfoRespDTOAnyTxnHttpResponse = loyaltyFeign.customerCardProject(loyaltyCardBasicModel.getCardNumber(),
//                loyaltyAccountBasicModel.getAccountManagementId(), loyaltyAccountBasicModel.getProductNumber(), "101");
        AnyTxnHttpResponse<LoyaltyInfoRespDTO<LoyaltyCustomerInfoResDTO>> loyaltyInfoRespDTOAnyTxnHttpResponse =null;
        if (!StringUtils.equalsAny("**********", loyaltyInfoRespDTOAnyTxnHttpResponse.getCode())) {
            logger.error("Get effective loyalty info failed, query loyalty effective info failed, cardNumber: {}, accountManagementId: {}, productNumber: {}",
                    loyaltyCardBasicModel.getCardNumber(), accountManagementId, loyaltyAccountBasicModel.getProductNumber());
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.LOYALY_SELECT_E);
        }
            List<LoyaltyCustomerInfoResDTO> resDTOList = loyaltyInfoRespDTOAnyTxnHttpResponse.getData().getProjectList().stream().filter(x -> StringUtils.equalsAny("1", x.getProjectStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(resDTOList) || resDTOList.size() < 1) {
                logger.error("Get effective loyalty info failed, effective loyalty info not found, cardNumber: {}, accountManagementId: {}, productNumber: {}",
                        loyaltyCardBasicModel.getCardNumber(), accountManagementId, loyaltyAccountBasicModel.getProductNumber());
                throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.LOYALY_EFFECT_NOT_E);
            }
            return resDTOList.get(0);
        }



    /**
     * 获取积分生效日期
     * @param accountManagementId
     * @return
     */
    private LocalDate getStartDate(String accountManagementId,OrganizationInfoResDTO organizationInfo) {
        LoyaltyAccountBasicModel loyaltyAccountBasicModel = accountManagementInfoSelfMapper.selectLoyaltyAccountInfo(accountManagementId);
        int cycleDay = loyaltyAccountBasicModel.getCycleDay()+1;
        LocalDate today = organizationInfo.getToday();
        int year = today.getYear();
        int month = today.getMonthValue();
        if(month==12){
            year +=1;
            month=1;
        }else {
            month+=1;
        }
        return  LocalDate.of(year,month,cycleDay);
    }
}
