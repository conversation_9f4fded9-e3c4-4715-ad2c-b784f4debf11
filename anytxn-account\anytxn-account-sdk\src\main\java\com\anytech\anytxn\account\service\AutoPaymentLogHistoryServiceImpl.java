package com.anytech.anytxn.account.service;

import com.anytech.anytxn.account.base.utils.ListUtils;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.account.base.domain.bo.AutoPaymentBO;
import com.anytech.anytxn.account.base.enums.AccountingRepDetailEnum;
import com.anytech.anytxn.account.base.enums.AnyTxnAccountingRespCodeEnum;
import com.anytech.anytxn.account.base.exception.AnyTxnAccountingException;
import com.anytech.anytxn.account.base.service.IAutoPaymentLogHistoryService;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.account.domain.dto.AutoPaymentInfoHistoryDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AutoPaymentLogHisSearchKeyDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AutoPaymentInfoHistoryMapper;
import com.anytech.anytxn.business.dao.account.mapper.AutoPaymentInfoHistorySelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AutoPaymentInfoHistory;
import com.anytech.anytxn.business.dao.account.model.AutoPaymentLog;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 约定扣款
 *
 * @Author: Wenwu Huang
 * @Date: 2019/1/21 16:04
 */
@Service
public class AutoPaymentLogHistoryServiceImpl implements IAutoPaymentLogHistoryService {
    private static final Logger logger = LoggerFactory.getLogger(AutoPaymentLogHistoryServiceImpl.class);

    @Autowired
    private AutoPaymentInfoHistoryMapper autoPaymentInfoHistoryMapper;
    @Autowired
    private AutoPaymentInfoHistorySelfMapper autoPaymentInfoHistorySelfMapper;
    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private SequenceIdGen sequenceIdGen;


    @Value("${anytxn.batch.stmt.max-insert:500}")
    private int maxInsert;
    @Value("${anytxn.batch.stmt.max-update:500}")
    private int maxUpdate;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void batchSave(List<AutoPaymentBO> list) {
        List<AutoPaymentLog> autoPaymentLogs = new ArrayList<>();
        list.forEach(x->{
            if (!CollectionUtils.isEmpty(x.getAutoPayments())){
                autoPaymentLogs.addAll(x.getAutoPayments());
            } });
        if (!CollectionUtils.isEmpty(autoPaymentLogs)){
            logger.info("Calling organizationInfoService.findOrganizationInfo, orgNumber={}", OrgNumberUtils.getOrg());
            OrganizationInfoResDTO paramOrganizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
            logger.info("Called organizationInfoService.findOrganizationInfo, result={}", paramOrganizationInfo != null ? "success" : "null");
            LocalDate accruedThruDay = paramOrganizationInfo.getAccruedThruDay();
            List<AutoPaymentInfoHistory> autoPaymentInfoHisList = new ArrayList<>();
            autoPaymentLogs.forEach(x ->{
                AutoPaymentInfoHistory autoPaymentInfoHistory = new AutoPaymentInfoHistory();
                autoPaymentInfoHistory.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                autoPaymentInfoHistory.setOrganizationNumber(x.getOrganizationNumber());
                autoPaymentInfoHistory.setAccountManagementId(x.getAccountManagementId());
                autoPaymentInfoHistory.setAutoPaymentCreateDate(accruedThruDay);
                autoPaymentInfoHistory.setAutoPaymentBillingDate(null);
                autoPaymentInfoHistory.setAutoPaymentCurrency(x.getOriginalCurrency());
                autoPaymentInfoHistory.setAutoPaymentRepayAmount(x.getFinalPaymentAmount());
                autoPaymentInfoHistory.setAutoPaymentRealAmount(BigDecimal.ZERO);
                autoPaymentInfoHistory.setAutoPaymentResultCode("1");
                if(StringUtils.isNotEmpty(x.getAutoPaymentDebitAcctNumber())){
                    autoPaymentInfoHistory.setAutoPaymentDebitBraNumber(x.getAutoPaymentBranchNumber());
                    autoPaymentInfoHistory.setAutoPaymentDebitBankNumber(x.getAutoPaymentDebitBankNumber());
                }else {
                    autoPaymentInfoHistory.setAutoPaymentDebitBraNumber(x.getAutoPaymentOtherBranch());
                    autoPaymentInfoHistory.setAutoPaymentDebitBankNumber(x.getAutoPaymentOtherBankNumber());
                }
                autoPaymentInfoHistory.setCreateTime(LocalDateTime.now());
                autoPaymentInfoHistory.setUpdateTime(LocalDateTime.now());
                autoPaymentInfoHistory.setUpdateBy(LoginUserUtils.getLoginUserName());
                autoPaymentInfoHistory.setVersionNumber(1L);
                autoPaymentInfoHisList.add(autoPaymentInfoHistory);
            });

            //批量插入，防止单个sql过大设置阈值控制
            if (autoPaymentLogs.size() > maxInsert){
                List<List<AutoPaymentInfoHistory>> insertLists = ListUtils.fixedGrouping(autoPaymentInfoHisList, maxInsert);
                for (List<AutoPaymentInfoHistory> autoPaymentInfoHistories : insertLists){
                    autoPaymentInfoHistorySelfMapper.insertBatch(autoPaymentInfoHistories);
                }
            }else {
                autoPaymentInfoHistorySelfMapper.insertBatch(autoPaymentInfoHisList);
            }
        }
    }

    /**
     * 根据搜索条件分页查询自动还款历史记录
     * @param searchKey 搜索条件
     * @return PageResultDTO<AutoPaymentInfoHistoryDto>
     */
    @Override
    public PageResultDTO<AutoPaymentInfoHistoryDTO> findByCardNum(AutoPaymentLogHisSearchKeyDTO searchKey) {
        logger.info("Query automatic repayment history records by search criteria with pagination");
        String orgNum = null;
        String accountMid = null;
        if (null != searchKey.getCardNumber() && !"".equals(searchKey.getCardNumber())){
            CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(searchKey.getCardNumber(),searchKey.getOrganizationNumber());
            if (null == cardAuthorizationInfo){
                return null;
            }
            String customerId = cardAuthorizationInfo.getPrimaryCustomerId();
            List<AccountManagementInfo> accountManagementInfos = accountManagementInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(), customerId);
            if(!CollectionUtils.isEmpty(accountManagementInfos)){
                accountMid = accountManagementInfos.get(0).getAccountManagementId();
                orgNum = accountManagementInfos.get(0).getOrganizationNumber();
            }
        }
        Page<AutoPaymentInfoHistory> page = PageHelper.startPage(searchKey.getPage(), searchKey.getRows());
        List<AutoPaymentInfoHistory> autoPaymentHisList = autoPaymentInfoHistorySelfMapper.selectByOrgAndMid(orgNum,accountMid);
        List<AutoPaymentInfoHistoryDTO> autoPaymentInfoHistoryDtos = BeanMapping.copyList(autoPaymentHisList, AutoPaymentInfoHistoryDTO.class);
        return new PageResultDTO<>(searchKey.getPage(), searchKey.getRows(), page.getTotal(), page.getPages(), autoPaymentInfoHistoryDtos);
    }

    /**
     * 根据主键查询自动还款历史明细
     * @param id 自动还款历史id
     * @return AutoPaymentInfoHistoryDto
     */
    @Override
    public AutoPaymentInfoHistoryDTO selectById(String id) {
        if (null == id){
            logger.error("Query automatic repayment history details by primary key, primary key cannot be empty");
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.P_NOT_NULL, AccountingRepDetailEnum.AU_RE);
        }
        AutoPaymentInfoHistory autoPaymentLog = autoPaymentInfoHistoryMapper.selectByPrimaryKey(id);
        if (null == autoPaymentLog){
            logger.error("Failed to query automatic repayment history details by primary key, id: {}", id);
            throw new AnyTxnAccountingException(AnyTxnAccountingRespCodeEnum.D_NOT_EXIST, AccountingRepDetailEnum.Q_AU_AU);
        }
        return BeanMapping.copy(autoPaymentLog, AutoPaymentInfoHistoryDTO.class);
    }
}
